<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ncbs-claim-root</artifactId>
        <groupId>com.paic.ncbs</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>ncbs-claim</artifactId>
    <packaging>jar</packaging>


    <properties>
    <mybatis-generator-maven-plugin.version>1.3.5</mybatis-generator-maven-plugin.version>
    <mybatis.generator.configurationFile>src\main\java\com\paic\ncbs\claim\dao\mybatis_generator\mybatis-generator-config.xml
    </mybatis.generator.configurationFile>
    </properties>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.paic.ncbs.claim.ClaimApplication</mainClass>
                </configuration>
            </plugin>


            <!--swagger2.9.2版本导出-生成ASCIIDOC-->
           <!-- <plugin>
                <groupId>io.github.swagger2markup</groupId>
                <artifactId>swagger2markup-maven-plugin</artifactId>
                <version>1.3.1</version>
                <configuration>
                    &lt;!&ndash;此处端口一定要是当前项目启动所用的端口&ndash;&gt;
                    <swaggerInput>http://localhost:48913/claim/v2/api-docs</swaggerInput>
                    <outputFile>src/docs/asciidoc/generated</outputFile>
                    <config>
                        &lt;!&ndash; 除了ASCIIDOC之外，还有MARKDOWN和CONFLUENCE_MARKUP可选 &ndash;&gt;
                        <swagger2markup.generatedExamplesEnabled>true</swagger2markup.generatedExamplesEnabled>
                        <swagger2markup.inlineSchemaEnabled>false</swagger2markup.inlineSchemaEnabled>
                        <swagger2markup.pathsGroupedBy>TAGS</swagger2markup.pathsGroupedBy>
                        <swagger2markup.markupLanguage>ASCIIDOC</swagger2markup.markupLanguage>
                    </config>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.asciidoctor</groupId>
                <artifactId>asciidoctor-maven-plugin</artifactId>
                <configuration>
                    <sourceDirectory>src/docs/asciidoc</sourceDirectory>
                    <outputFile>src/docs/html</outputFile>
                    <backend>html5</backend>
                    <sourceHighlighter>coderay</sourceHighlighter>
                    <attributes>
                        <toc>left</toc>
                        <toclevels>3</toclevels>
                        <sectnums>true</sectnums>
                    </attributes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.asciidoctor</groupId>
                <artifactId>asciidoctor-maven-plugin</artifactId>
                <version>1.5.6</version>
            </plugin>-->


            <!--
                        <plugin>
                            <groupId>org.mybatis.generator</groupId>
                            <artifactId>mybatis-generator-maven-plugin</artifactId>
                            <version>${mybatis-generator-maven-plugin.version}</version>
                            <configuration>
                                <configurationFile>${mybatis.generator.configurationFile}</configurationFile>
                                <verbose>true</verbose>
                                <overwrite>true</overwrite>
                            </configuration>
                            <executions>
                                <execution>
                                    <id>generate</id>
                                    <goals>
                                        <goal>generate</goal>
                                    </goals>
                                </execution>
                            </executions>
                        </plugin>-->
        </plugins>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.yaml</include>
                    <include>**/*.json</include>
                    <include>**/*.xlsx</include>
                    <include>**/*.ftl</include>
                    <include>**/*.html</include>
                    <include>**/*.ttf</include>
                    <include>**/*.ttc</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>

</project>