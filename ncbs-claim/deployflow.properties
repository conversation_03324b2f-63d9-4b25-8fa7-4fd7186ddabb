#mavenæå»º
PPL_BUILD_CMD=maven
#ä»£ç åæ¯ä¸çç¼è¯æä»¶
PPL_MAVEN_FILE=pom.xml
#mavenç¼è¯æåå½ä»¤
PPL_MAVEN_CMD=clean package -Dmaven.test.skip=true
#mavenç¼è¯çæ¬
MAVEN_VERSION=3.2.5
#javaçæ¬(1.4, 1.6, 1.7, 1.8, 1.8.181)
JAVA_VERSION=1.8
#æåçæçæä»¶ç®å½ï¼æ­¤ç®å½å°è¢«å­æ¡£å°å¶ååºï¼åå¸çäº§çå¯¹è±¡é½éå­å¨äºæ­¤ç®å½ï¼
PPL_REL_PATH=rel
#åºç¨ç³»ç»å­ç¬¦ç¼ç ï¼éå¸¸ä¸æºä»£ç çå­ç¬¦ç¼ç ä¸è´ï¼
PPL_APP_ENCODING=UTF-8
#æ°æ®åºèæ¬å­ç¬¦ç¼ç ï¼Oracleä½¿ç¨GBKæGB18030ï¼Mysql|PostgreSQL|MongoDBä½¿ç¨UTF-8ï¼
PPL_SQL_ENCODING=UTF-8