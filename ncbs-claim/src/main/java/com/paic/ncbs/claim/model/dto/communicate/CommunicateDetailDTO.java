package com.paic.ncbs.claim.model.dto.communicate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
@ApiModel("沟通明细")
public class CommunicateDetailDTO extends EntityDTO {

	private static final long serialVersionUID = -6175512831684330367L;

	private String idAhcsCommunicateDetail;

	private String idAhcsCommunicateBase;

	@ApiModelProperty("发起人")
	private String initiatorUm;

	@ApiModelProperty("发起时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date initiatDate;

	@ApiModelProperty("处理人")
	private String dealUm;

	@ApiModelProperty("老处理人")
	private String oldDealUm;

	@ApiModelProperty("处理时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
	private Date dealDate;

	@ApiModelProperty("角色 0：沟通人 1：发起人")
	private String role;

	@ApiModelProperty("沟通内容")
	private String communicateContent;

	@ApiModelProperty("任务状态 0：待处理 1：已处理")
	private String taskStatus;

	@ApiModelProperty("报案号")
	private String reportNo;

	@ApiModelProperty("赔款金额")
	private BigDecimal payAmount;

}
