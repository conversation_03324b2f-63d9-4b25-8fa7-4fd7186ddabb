package com.paic.ncbs.claim.service.rule.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.feign.RuleFeign;
import com.paic.ncbs.claim.model.dto.rule.*;
import com.paic.ncbs.claim.service.rule.RuleService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class RuleServiceImpl implements RuleService {

    @Autowired
    private RuleFeign ruleFeign;


    public RuleResponseDTO fireRule(RuleRequestDTO requestDTO) {
        if (log.isInfoEnabled()) {
            log.info("fireRule request: [{}]", JSON.toJSONString(requestDTO));
        }
        String requestId = MDC.get(BaseConstant.REQUEST_ID);
        RuleResponseDTO ruleResponseDTO = ruleFeign.fireRules(requestId,requestDTO);
        if (log.isInfoEnabled()) {
            log.info("fireRule response: [{}]", JSON.toJSONString(ruleResponseDTO));
        }
        return ruleResponseDTO;
    }

    /**
     * 责任确认规则
     *
     * @param dutyConfirmParam
     * @return
     */
    @Override
    public RuleResultDTO dutyConfirmRule(AutoRuleParam dutyConfirmParam) {
        RuleRequestDTO requestDTO = new RuleRequestDTO();
        requestDTO.setDebug(false);
        requestDTO.setFireRuleMax(1);
        requestDTO.setPackageCode("claim/dutyConfirmRule");
        requestDTO.setPackageType("RULE_FLOW");
        requestDTO.setProcessCode("dutyConfirmFlow");

        Map<String, Map<String, Object>> paramObjects = new HashMap<>();
        paramObjects.put("dutyConfirmParam", BeanUtil.beanToMap(dutyConfirmParam));
        requestDTO.setParamObjects(paramObjects);
        // 调用责任确认规则引擎
        LogUtil.info("dutyConfirmRule规则引擎入参: {}",JSON.toJSONString(requestDTO));
        RuleResponseDTO result = fireRule(requestDTO);
        LogUtil.info("dutyConfirmRule规则引擎返回结果: {}", result);
        if (result != null && Objects.nonNull(result.getResultMap())) {
            RuleResultDTO ruleResultDTO = new RuleResultDTO();
            ruleResultDTO.setCode(result.getResultMap().get("code").toString());
            ruleResultDTO.setMessage(result.getResultMap().get("message").toString());
            ruleResultDTO.setProcessCode(result.getResultMap().get("processCode").toString());
            Object o = result.getResultMap().get("result");
            if(o instanceof JSONArray){
                List<DutyDetailInfoBO> dataList =
                        JSONArray.parseArray(((JSONArray) result.getResultMap().get("result")).toJSONString(),
                                DutyDetailInfoBO.class);
                ruleResultDTO.setData(dataList);
            } else if (o instanceof ArrayList){
                String jsonString = JSONArray.toJSONString(o);
                List<DutyDetailInfoBO> dataList =
                        JSONArray.parseArray(jsonString,DutyDetailInfoBO.class);
                ruleResultDTO.setData(dataList);
            }
            return ruleResultDTO;
        } else {
            return null;
        }
    }

    /**
     * 自动理赔调整规则引擎
     *
     * @param autoSettleParameter
     * @return
     */
    @Override
    public RuleResultDTO autoSettleRule(AutoSettleParameter autoSettleParameter) {
        RuleRequestDTO requestDTO = new RuleRequestDTO();
        requestDTO.setDebug(false);
        requestDTO.setFireRuleMax(1);
        requestDTO.setPackageCode("claim/autoSettleRule");
        requestDTO.setPackageType("RULE_FLOW");
        requestDTO.setProcessCode("autoSettleRuleFlow");

        Map<String, Map<String, Object>> paramObjects = new HashMap<>();
        paramObjects.put("autoSettleParameter", BeanUtil.beanToMap(autoSettleParameter));
        requestDTO.setParamObjects(paramObjects);
        // 调用规则引擎自动核赔
        LogUtil.info("autoSettleRule规则引擎入参: {}",JSON.toJSONString(requestDTO));
        RuleResponseDTO result = fireRule(requestDTO);
        LogUtil.info("autoSettleRule规则引擎返回结果: {}", result);
        if (result != null && Objects.nonNull(result.getResultMap())) {
            return BeanUtil.toBean(result.getResultMap(), RuleResultDTO.class);
        } else {
            return null;
        }
    }

    @Override
    public RuleResultDTO autoVerifyRule(CaseInfoBO caseInfoBO, BillInfoListBO billInfoListBO,
                                        DutyPayInfoListBO dutyPayInfoListBO) {
        RuleRequestDTO requestDTO = new RuleRequestDTO();
        requestDTO.setDebug(false);
        requestDTO.setFireRuleMax(1);
        requestDTO.setPackageCode("claim/autoVerifyRule");
        requestDTO.setPackageType("RULE_FLOW");
        requestDTO.setProcessCode("autoVerifyRuleFlow");

        Map<String, Map<String, Object>> paramObjects = new HashMap<>();
        AutoVerifyParameter param = new AutoVerifyParameter();
        param.setBillInfoBO(billInfoListBO);
        param.setCaseInfoBO(caseInfoBO);
        param.setDutyPayInfoListBO(dutyPayInfoListBO);
        paramObjects.put("autoVerifyParameter", BeanUtil.beanToMap(param));
        requestDTO.setParamObjects(paramObjects);
        // 调用规则引擎自动核赔
        LogUtil.info("autoVerifyRule规则引擎入参: {}",JSON.toJSONString(requestDTO));
        RuleResponseDTO result = fireRule(requestDTO);
        LogUtil.info("autoVerifyRule规则引擎返回结果: {}", result);
        if (result != null && Objects.nonNull(result.getResultMap())) {
            return BeanUtil.toBean(result.getResultMap(), RuleResultDTO.class);
        } else {
            return null;
        }
    }
}
