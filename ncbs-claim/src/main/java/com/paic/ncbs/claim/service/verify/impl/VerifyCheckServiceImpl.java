package com.paic.ncbs.claim.service.verify.impl;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.DutyAttributeConst;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.constant.SettleConst;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.SettleHelper;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.entity.common.PolicyDto;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.DutyAttributeMapper;
import com.paic.ncbs.claim.dao.mapper.duty.DutyDetailPayMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.ClmsDutyDetailBillSettleMapper;
import com.paic.ncbs.claim.dao.mapper.settle.DutyBillLimitInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.policy.PolicyMonthDto;
import com.paic.ncbs.claim.model.dto.report.QueryAccidentVo;
import com.paic.ncbs.claim.model.dto.settle.*;
import com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.RemitAmountDTO;
import com.paic.ncbs.claim.model.vo.duty.DutyBillLimitDto;
import com.paic.ncbs.claim.model.vo.duty.DutyLimitQueryVo;
import com.paic.ncbs.claim.model.vo.settle.VerifyInfoVO;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.ClmsGetPolicyMonthInfoService;
import com.paic.ncbs.claim.service.common.ClmsQueryPolicyInfoService;
import com.paic.ncbs.claim.service.estimate.EstimateChangeService;
import com.paic.ncbs.claim.service.risk.RiskCheckService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyPayService;
import com.paic.ncbs.claim.service.riskppt.RiskPropertyService;
import com.paic.ncbs.claim.service.settle.MaxPayService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import com.paic.ncbs.claim.service.verify.VerifyCheckService;
import com.paic.ncbs.claim.utils.JsonUtils;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;

@Service
@RefreshScope
@Slf4j
public class VerifyCheckServiceImpl implements VerifyCheckService {

    @Autowired
    private MaxPayService maxPayService;
    @Autowired
    private PolicyPayService policyPayService;
    @Autowired
    private RiskCheckService riskCheckService;
    @Autowired
    private RiskPropertyService riskPropertyService;
    @Autowired
    private RiskPropertyPayService riskPropertyPayService;
    @Autowired
    private DutyBillLimitInfoMapper dutyBillLimitInfoMapper;
    @Autowired
    private ClmsQueryPolicyInfoService clmsQueryPolicyInfoService;
    @Autowired
    private ClmsGetPolicyMonthInfoService clmsGetPolicyMonthInfoService;
    @Autowired
    private DutyDetailPayMapper dutyDetailPayMapper;
    @Autowired
    private DutyAttributeMapper dutyAttributeMapper;

    @Autowired
    private EstimateChangeService estimateChangeService;

    @Autowired
    private ReportInfoMapper reportInfoMapper;
    @Autowired
    private ClmsDutyDetailBillSettleMapper clmsDutyDetailBillSettleMapper;
    @Autowired
    private BpmService bpmService;


    /**
     * 保单月限额配置方案
     */
    @Value("#{${policyLimit.month:null}}")
    private Map<String,BigDecimal> policyMonthLimitMap;
    /**
     * 保单日限额配置方案
     */
    @Value("#{${policyLimit.day:null}}")
    private Map<String,BigDecimal> policyDayLimitMap;

//    @Value("#{${policyLimit.monthFrequencyCount}}")
    private Map<String,Integer> policyMonthFrequencyCountLimitMap;

    @Value("#{${policyLimit.monthFrequencyDay:null}}")
    private Map<String,Integer> policyMonthFrequencyDayLimitMap;

    @Value("#{${policyLimit.monthOrgCount:null}}")
    private Map<String,Integer> policyMonthOrgCountLimitMap;

    @Value("#{${policyLimit.dayOrgCount:null}}")
    private Map<String,Integer> policyDayOrgCountLimitMap;

    @Value("${special.productPackage}")
    private String specialProductPackage;

    @Value("#{${yibaoCompensation.packageCode:null}}")
    private Map<String,BigDecimal> yibaoCompensationMap;


    /**
     * 核赔之前校验
     *
     * @param verifyInfoVO
     */
    @Override
    public void checkVerifyBefore(VerifyInfoVO verifyInfoVO) throws Exception {
        String reportNo = verifyInfoVO.getReportNo();
        Integer caseTimes = verifyInfoVO.getCaseTimes();
        String indemnityModel = verifyInfoVO.getIndemnityModel();
        List<PolicyPayDTO> policyPayDTOList = policyPayService.getByReportNo(reportNo, caseTimes);

        bpmService.processCheck(reportNo, BpmConstants.OC_SETTLE_REVIEW,BpmConstants.OPERATION_SUBMIT);
        //校验是否有在途的未决审批
        if(estimateChangeService.checkEstimateChangePending(reportNo, caseTimes)){
            throw new GlobalBusinessException("当前案件存在审批中的未决修正，不能核赔通过！");
        }
        // 添加理算金额校验
        checkSettleAmount(policyPayDTOList);
        // 校验责任险保单累计限额
        riskPropertyPayService.checkRiskPay(reportNo,policyPayDTOList);
        // 健康险 在途任务校验
        riskCheckService.checkOtherCaseUnfinishedTask(reportNo,caseTimes);
        //通融案件不校验责任属性相关的所有因子
        if (!"6".equals(indemnityModel)) {
            // 添加每月赔付天数校验
            checkMonthPayDays(reportNo, caseTimes, policyPayDTOList);
            //checkMonthPayDays(reportNo,caseTimes);
            //校验保单月限额
            checkMonthLimitPay(reportNo, caseTimes, policyPayDTOList);
            //校验保单日限额
            checkDayLimitPay(reportNo, caseTimes, policyPayDTOList);
            //校验保单自然月限次数
            checkMonthFrequencyCountLimitPay(reportNo, caseTimes, policyPayDTOList);
            //校验保单自然月限天数
            checkMonthFrequencyDayLimitPay(reportNo, caseTimes, policyPayDTOList);
            //校验保单合同月机构限天数
            checkMonthOrgCountLimitPay(reportNo, caseTimes, policyPayDTOList);
            //校验保单日期机构限天数
            checkDayOrgCountLimitPay(reportNo, caseTimes, policyPayDTOList);
            //校验日免赔额
            checkDayRemit(reportNo, caseTimes, policyPayDTOList);
            //校验医保产品赔付金额
            checkYibaoPayAmount(reportNo, caseTimes, policyPayDTOList);
        }
    }

    private void checkYibaoPayAmount(String reportNo, Integer caseTimes, List<PolicyPayDTO> policyPayDTOList) {
        for (PolicyPayDTO policyPayDTO : policyPayDTOList) {
            if(null == yibaoCompensationMap || !yibaoCompensationMap.containsKey(policyPayDTO.getProductPackage())){
                continue;
            }
            BigDecimal amountLimit = yibaoCompensationMap.get(policyPayDTO.getProductPackage());
            if (amountLimit.compareTo(policyPayDTO.getSettleAmount()) < 0) {
                throw new GlobalBusinessException("该产品理算金额不能大于" + amountLimit + "元，请重新理算。");
            }
        }
    }

    /**
     * 校验月限额
     * @param reportNo
     * @param caseTimes
     * @param policyPayDTOList
     */
    private void checkMonthLimitPay(String reportNo, Integer caseTimes, List<PolicyPayDTO> policyPayDTOList) {
        for (PolicyPayDTO policyPayDTO : policyPayDTOList) {
            if(MapUtils.isNotEmpty(policyMonthLimitMap) && policyMonthLimitMap.containsKey(policyPayDTO.getProductPackage())) {
                // 查询本责任赔付的账单信息：账单的日期
                List<DutyBillLimitInfoDTO> dtoLimitList = dutyBillLimitInfoMapper.getBillLimitInfo(reportNo, caseTimes);
                // 得到保单的起止日期
                PolicyDto policyDto = clmsQueryPolicyInfoService.getPolicyDto(policyPayDTO.getPolicyNo());
                // 根据起止日期计算月数
                List<PolicyMonthDto> monthDtoList =
                        clmsGetPolicyMonthInfoService.getPolicyMonthInfo(policyDto.getPolicyStartDate(),
                                policyDto.getPolicyEndDate());
                for (PolicyMonthDto policyMonthDto : monthDtoList) {
                    BigDecimal monthAmount = BigDecimal.ZERO;
                    for (DutyBillLimitInfoDTO dutyBillLimitInfoDTO : dtoLimitList) {
                        if (null != dutyBillLimitInfoDTO.getBillDate() &&
                                dutyBillLimitInfoDTO.getBillDate().compareTo(policyMonthDto.getStartDate()) >= 0 &&
                                dutyBillLimitInfoDTO.getBillDate().compareTo(policyMonthDto.getEndDate()) <= 0) {
                            monthAmount = monthAmount.add(dutyBillLimitInfoDTO.getSettleClaimAmount());
                        }

                    }
                    if (monthAmount.compareTo(BigDecimal.ZERO) > 0) {
                        //查询保单合同月的历史赔付金额
                        //todo 后面确定了共享责任的获取方式后，这里需要修改：需细化到险种责任维度
                        BigDecimal monthLimit = policyMonthLimitMap.get(policyPayDTO.getProductPackage());
                        BigDecimal historyAmount = dutyBillLimitInfoMapper.getHistoryAmount(reportNo, policyPayDTO.getPolicyNo(), policyMonthDto.getStartDate(), policyMonthDto.getEndDate());
                        if (monthAmount.add(historyAmount).compareTo(monthLimit) > 0) {
                            LogUtil.audit("保单号：{}，月限额：{}，保单月起始日期：{}，终止日期：{}，历史赔付金额：{}，理算金额：{}，月限额不足，请重新理算。", policyPayDTO.getPolicyNo(), monthLimit,
                                    policyMonthDto.getSdate(), policyMonthDto.getEDate(),historyAmount, monthAmount);
                            throw new GlobalBusinessException("理算金额不能大于剩余月限额，请重新理算，保单月：" + policyMonthDto.getSdate() + "至" + policyMonthDto.getEDate());
                        }
                    }
                }
            }
        }
    }

    private void checkDayLimitPay(String reportNo, Integer caseTimes, List<PolicyPayDTO> policyPayDTOList) {
        for (PolicyPayDTO policyPayDTO : policyPayDTOList) {
            // 判断配置是不是有日限额
            if(null == policyDayLimitMap || !policyDayLimitMap.containsKey(policyPayDTO.getProductPackage())){
                continue;
            }
            BigDecimal baseDayLimit = policyDayLimitMap.get(policyPayDTO.getProductPackage());
            LogUtil.audit("案件：{}开发校验日限额：{}",policyPayDTO.getReportNo(),baseDayLimit);
            // 查询本责任赔付的账单信息：账单的日期
            List<DutyBillLimitInfoDTO> dutyBillLimitInfoDTOList = dutyBillLimitInfoMapper.getBillLimitInfo(reportNo, caseTimes);
            if(CollectionUtils.isEmpty(dutyBillLimitInfoDTOList)){
                LogUtil.audit("案件：{}无dutyBillLimit数据，无需校验！",policyPayDTO.getReportNo());
                break;
            }
            // 本案每日要赔付的金额
            Map<Date, BigDecimal> currentDayPayMap = dutyBillLimitInfoDTOList.stream().collect(Collectors.groupingBy(DutyBillLimitInfoDTO::getBillDate,
                    Collectors.reducing(BigDecimal.ZERO, DutyBillLimitInfoDTO::getSettleClaimAmount, BigDecimal::add)));
            for (Map.Entry<Date, BigDecimal> item : currentDayPayMap.entrySet()) {
                Date billDate = item.getKey();
                String strBillDate = DateUtils.dateFormat(billDate,DateUtils.SIMPLE_DATE_STR);
                BigDecimal dayPayAmount = item.getValue();
                BigDecimal hisDayPayAmount = dutyBillLimitInfoMapper.getHistoryAmount(reportNo, policyPayDTO.getPolicyNo(), billDate, billDate);
                hisDayPayAmount = Optional.ofNullable(hisDayPayAmount).orElse(BigDecimal.ZERO);
                if(dayPayAmount.add(hisDayPayAmount).compareTo(baseDayLimit) > 0){
                    LogUtil.audit("案件号：{}，保单号：{}，发票日期：{}，日限额：{}，历史赔付金额：{}，理算金额：{}，保单日限额不足，请重新理算。",
                            reportNo,policyPayDTO.getPolicyNo(), strBillDate,baseDayLimit,hisDayPayAmount, dayPayAmount);
                    throw new GlobalBusinessException("发票日期：" + strBillDate + ",理算金额不能大于剩余保单日限额，请重新理算。");
                }
            }
        }
    }

    /**
     * 校验理算金额
     * @param policyPayInfoArr
     */
    private void checkSettleAmount(List<PolicyPayDTO> policyPayInfoArr){
        if (CollectionUtils.isEmpty(policyPayInfoArr)) {
            throw new GlobalBusinessException(ErrorCode.Settle.POLICY_PAY_IS_NOT_EXSITS, "赔付数据不存在,请先初始化");
        }

        riskPropertyService.getRiskPropertyPlan(policyPayInfoArr);
        SettleHelper.setDetailExInfo(policyPayInfoArr);

        maxPayService.initPoliciesPayMaxPay(policyPayInfoArr, null);

        for (PolicyPayDTO policyPayDTO : policyPayInfoArr) {
            List<DutyPayDTO> allDutyPayList = new ArrayList<>();
            List<PlanPayDTO> planPayList = policyPayDTO.getPlanPayArr();
            // 非共享保额的场景理赔金额校验。
            planPayList.forEach(plan->{
                List<DutyPayDTO> dutyPayList = plan.getDutyPayArr();
                allDutyPayList.addAll(dutyPayList);
                dutyPayList.forEach(duty->{
                    if (duty.getMaxAmountPay().compareTo(nvl(duty.getSettleAmount(),0)) < 0) {
                        throw new GlobalBusinessException("理算金额不能大于剩余赔付额，请重新理算");
                    }
                });
            });

            // 共享保额再次校验
            Map<String,List<DutyPayDTO>> shareDuty = allDutyPayList.stream().filter(DutyPayDTO::getIsDutyShareAmount).collect(Collectors.groupingBy(DutyPayDTO::getShareDutyGroup));
            if (!shareDuty.isEmpty()){
                shareDuty.forEach((k,v)->{
                    BigDecimal maxAmountPay = Optional.ofNullable(v.get(0).getMaxAmountPay()).orElse(BigDecimal.ZERO);
                    BigDecimal settleAmount = v.stream().map(duty->
                            Optional.ofNullable(duty.getSettleAmount()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO,BigDecimal::add);
                    if (settleAmount.compareTo(BigDecimal.ZERO) <= 0) {
                        return;
                    }
                    if (settleAmount.compareTo(maxAmountPay) > 0) {
                        LogUtil.info("共享保额的责任理算金额总和大于共享的最大给付额,理算金额总和为{},剩余给付额为{},责任编码为:{}", settleAmount, maxAmountPay, k);
                        throw new GlobalBusinessException(ErrorCode.Settle.DUTY_SETTLE_AMOUNT_GT_MAX_AMOUNT,
                                "责任编码:"+ k +"理算金额总和:["+ settleAmount +"]不能大于共享最大给付额：["+ maxAmountPay +"]");
                    }
                });
            }
        }

    }

    /**
     *  校验每月赔付天数：(根据账单日期进行判断)
     * @param policyPayDTOS
     */
    private void checkMonthPayDays( String reportNo, Integer caseTimes,List<PolicyPayDTO> policyPayDTOS) {
        List<DutyAttributeValueDTO> dutyAttributeValueDTOList = dutyAttributeMapper.getDutyAttributePayDays(reportNo);
        if (CollectionUtils.isEmpty(dutyAttributeValueDTOList)) {
            return;
        }
        // 查询本责任赔付的账单信息：账单的日期
        List<DutyBillLimitInfoDTO> dtoLimitList = dutyBillLimitInfoMapper.getReportNoPolicyBillLimitInfo(reportNo, caseTimes);
        if (CollectionUtils.isEmpty(dtoLimitList)) {
            LogUtil.audit("案件：{}不存在赔付发票！", reportNo);
            return;
        }

        for (PolicyPayDTO policyPayDTO : policyPayDTOS) {
            // 得到保单的起止日期
            PolicyDto policyDto = clmsQueryPolicyInfoService.getPolicyDto(policyPayDTO.getPolicyNo());
            // 根据起止日期计算月数
            List<PolicyMonthDto> monthDtoList =
                    clmsGetPolicyMonthInfoService.getPolicyMonthInfo(policyDto.getPolicyStartDate(),
                            policyDto.getPolicyEndDate());
            for (PlanPayDTO planPayDTO : policyPayDTO.getPlanPayArr()) {
                for (DutyPayDTO dutyPayDTO : planPayDTO.getDutyPayArr()) {
                    // 判断责任赔付金额是否大于0
                    if (dutyPayDTO.getSettleAmount().compareTo(BigDecimal.ZERO) <= 0) {
                        continue;
                    }
                    // 判断赔付方式是否全部为赔付
                    boolean isPayMode =
                            Optional.ofNullable(dutyPayDTO.getDutyDetailPayArr()).orElseGet(ArrayList::new).stream()
                                    .allMatch(item -> SettleConst.INDEMNITY_MODE_PAY.equals(item.getIndemnityMode()));
                    if (!isPayMode) {
                        continue;
                    }
                    // 查询是否有每月赔付天数属性
                    Optional<DutyAttributeDTO> attributeDTOOpt =
                            dutyPayDTO.getAttributes().stream().filter(item -> DutyAttributeConst.EVERY_DAY_PAY_DAYS.equals(item.getAttrCode())).findFirst();
                    if (!attributeDTOOpt.isPresent()) {
                        continue;
                    }
                    //每月赔付天数
                    int payDays = Integer.parseInt(attributeDTOOpt.get().getAttrValue());

                    // 查询本责任赔付的账单信息：账单的日期
                    List<DutyBillLimitInfoDTO> dutyBillLimitInfoDTOS = dutyBillLimitInfoMapper.getReportNoPolicyBillLimitInfo(reportNo, caseTimes);
                    List<Date> billDateTempList =
                            dutyBillLimitInfoDTOS.stream()
                                    .filter(i -> planPayDTO.getPlanCode().equals(i.getPlanCode()) && dutyPayDTO.getDutyCode().equals(i.getDutyCode()))
                                    .map(DutyBillLimitInfoDTO::getBillDate).distinct().collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(billDateTempList)){
                        LogUtil.audit("案件：{}不存在赔付发票！",reportNo);
                        continue;
                    }

                    // 查询保单责任历史赔付天数
                    int count = 0;
                    for (PolicyMonthDto policyMonthDto : monthDtoList) {
                        List<Date> billDateList = new ArrayList<>();
                        for (Date billDate : billDateTempList) {
                            if (null != billDate &&
                                    billDate.compareTo(policyMonthDto.getStartDate()) >= 0 &&
                                    billDate.compareTo(policyMonthDto.getEndDate()) <= 0) {
                                // 最好去重一下，但SQL已经去重过了 先不处理
                                billDateList.add(billDate);
                            }
                        }
                        if (CollectionUtils.isEmpty(billDateList)){
                            // 当月没有赔付账单
                            continue;
                        }
                        count = count + billDateList.size();
                        // 查询保单责任历史赔付天数
                        DutyLimitQueryVo dutyLimitQueryVo =new DutyLimitQueryVo();
                        dutyLimitQueryVo.setPolicyNo(policyPayDTO.getPolicyNo());
                        dutyLimitQueryVo.setPlanCode(planPayDTO.getPlanCode());
                        dutyLimitQueryVo.setDutyCode(dutyPayDTO.getDutyCode());
                        dutyLimitQueryVo.setSatrtDate(policyMonthDto.getStartDate());
                        dutyLimitQueryVo.setEndDate(policyMonthDto.getEndDate());
                        List<DutyBillLimitDto> dtos= dutyBillLimitInfoMapper.getAllAlreadyPayTimes(dutyLimitQueryVo);

                        if (caseTimes > 1) {
                            // 重开案件去掉当前报案号对应的发票日期
                            dtos = dtos.stream().filter(dutyBillLimitDto -> !Objects.equals(reportNo,
                                    dutyBillLimitDto.getReportNo())).collect(Collectors.toList());
                        }
                        //去除金额为0的发票
                        dtos= dtos.stream().filter(dutyBillLimitDto -> !(dutyBillLimitDto.getSettleClaimAmount().compareTo(BigDecimal.ZERO) == 0)).collect(Collectors.toList());

                        if (CollectionUtils.isNotEmpty(dtos)) {
                            Map<Date, List<DutyBillLimitDto>> mothListMap = dtos.stream().sorted(Comparator.comparing(DutyBillLimitDto::getBillDate)).collect(Collectors.groupingBy(DutyBillLimitDto::getBillDate, LinkedHashMap::new, Collectors.toList()));
                            for (Map.Entry<Date, List<DutyBillLimitDto>> entry : mothListMap.entrySet()) {
                                List<DutyBillLimitDto> evDayList = entry.getValue();
                                List<String> caseNoList =
                                        evDayList.stream().map(DutyBillLimitDto::getCaseNo).collect(Collectors.toList());
                                Integer indmenityCount = dutyDetailPayMapper.getIndmenityInfo(caseNoList,
                                        dutyPayDTO.getDutyCode());
                                if (indmenityCount >= 1) {
                                    if(!billDateList.contains(entry.getKey())){
                                        billDateList.add(entry.getKey());
                                    }
                                }
                            }
                        }
                        // 计算是否超赔付
                        if (billDateList.size() > payDays) {
                            LogUtil.audit("案件：{}，赔付责任：{}，在{}-{}时间内赔付了{}天，超过配置{}！", reportNo,
                                    dutyPayDTO.getDutyCode(), policyMonthDto.getSdate(), policyMonthDto.getEDate(),
                                    JSON.toJSON(billDateList), payDays);
                            throw new GlobalBusinessException("当前案件在" + policyMonthDto.getSdate() + "至" + policyMonthDto.getEDate() + "超每月赔付天数！");
                        }
                        if(count == billDateTempList.size()){
                            // 账单已经校验完成后结束循环
                            break;
                        }
                    }
                }
            }
        }
    }

    /**
     *  校验日免赔
     * @param policyPayDTOS
     */
    private void checkDayRemit( String reportNo, Integer caseTimes,List<PolicyPayDTO> policyPayDTOS) throws Exception {
        // 查询本责任赔付的账单信息：账单的日期
        List<DutyBillLimitInfoDTO> dtoLimitList = dutyBillLimitInfoMapper.getReportNoPolicyBillLimitInfo(reportNo, caseTimes);
        if (CollectionUtils.isEmpty(dtoLimitList)) {
            LogUtil.audit("案件：{}不存在赔付发票！", reportNo);
            return;
        }

        for (PolicyPayDTO policyPayDTO : policyPayDTOS) {
            for (PlanPayDTO planPayDTO : policyPayDTO.getPlanPayArr()) {
                for (DutyPayDTO dutyPayDTO : planPayDTO.getDutyPayArr()) {
                    // 判断责任赔付金额是否大于0
                    if (dutyPayDTO.getSettleAmount().compareTo(BigDecimal.ZERO) <= 0) {
                        continue;
                    }
                    // 判断赔付方式是否全部为赔付
//                    boolean isPayMode =
//                            Optional.ofNullable(dutyPayDTO.getDutyDetailPayArr()).orElseGet(ArrayList::new).stream()
//                                    .allMatch(item -> SettleConst.INDEMNITY_MODE_PAY.equals(item.getIndemnityMode()));
//                    if (!isPayMode) {
//                        continue;
//                    }
                    // 查询是否有免赔属性
                    Optional<DutyAttributeDTO> attributeDTOOpt =
                            dutyPayDTO.getAttributes().stream().filter(item -> DutyAttributeConst.REMIT_AMOUNT_TYPE.equals(item.getAttrCode())).findFirst();
                    if (!attributeDTOOpt.isPresent()) {
                        continue;
                    }
                    //免赔类型
                    String remitAmountType = attributeDTOOpt.get().getAttrValue();
                    if (!"2".equals(remitAmountType)){
                        continue;
                    }

                    // 查询是否有免赔属性
                    String attributeRemitAmount = dutyAttributeMapper.getAttributeByAttrCodeInfo(dutyPayDTO.getIdCopyDuty(), DutyAttributeConst.REMIT_AMOUNT);
                    if(StringUtils.isEmptyStr(attributeRemitAmount)){
                        continue;
                    }
//                    Optional<DutyAttributeDTO> attributeAmountDTOOpt =
//                            dutyPayDTO.getAttributes().stream().filter(item -> DutyAttributeConst.REMIT_AMOUNT.equals(item.getAttrCode())).findFirst();
//                    if (!attributeAmountDTOOpt.isPresent()) {
//                        continue;
//                    }
//                    String attrValue = attributeAmountDTOOpt.get().getAttrValue();
//                    if(StringUtils.isEmptyStr(attrValue)){
//                        continue;
//                    }
                    // 查询本责任赔付的账单信息：账单的日期
                    BigDecimal remitAmount = new BigDecimal(attributeRemitAmount);
                    DutyDetailSettleRequest dutyDetailSettleRequest = new DutyDetailSettleRequest();
                    dutyDetailSettleRequest.setReportNo(reportNo);
                    dutyDetailSettleRequest.setCaseTimes(caseTimes);
                    dutyDetailSettleRequest.setPolicyNo(policyPayDTO.getPolicyNo());
                    dutyDetailSettleRequest.setPlanCode(planPayDTO.getPlanCode());
                    dutyDetailSettleRequest.setDutyCode(dutyPayDTO.getDutyCode());
                    List<ClmsDutyDetailBillSettleDTO> billSettleInfoByCondition = clmsDutyDetailBillSettleMapper.getBillSettleInfoByCondition(dutyDetailSettleRequest);
                    if (CollectionUtils.isEmpty(billSettleInfoByCondition)) {
                        continue;
                    }
                    Map<Date, BigDecimal> remitDayMap = billSettleInfoByCondition.stream().collect(Collectors.groupingBy(
                            ClmsDutyDetailBillSettleDTO::getBillDate, Collectors.reducing(BigDecimal.ZERO, dto -> dto.getExpendDayDeductible() == null ? BigDecimal.ZERO : dto.getExpendDayDeductible(), BigDecimal::add)
                    ));
                    Map<Date, BigDecimal> settleDayMap = dtoLimitList.stream().collect(Collectors.groupingBy(
                            DutyBillLimitInfoDTO::getBillDate, Collectors.reducing(BigDecimal.ZERO, dto -> dto.getSettleClaimAmount() == null ? BigDecimal.ZERO : dto.getSettleClaimAmount(), BigDecimal::add)
                    ));
                    for (Date date : remitDayMap.keySet()) {
                        // 查询历史日免赔抵扣额
                        RemitAmountDTO remitAmountDTO = new RemitAmountDTO();
                        remitAmountDTO.setPolicyNo(policyPayDTO.getPolicyNo());
                        remitAmountDTO.setPlanCode(planPayDTO.getPlanCode());
                        remitAmountDTO.setDutyCode(dutyPayDTO.getDutyCode());
                        Set<Date> dateSet = new HashSet<>();
                        dateSet.add(date);
                        remitAmountDTO.setDateList(dateSet);
                        List<ClmsDutyDetailBillSettleDTO> dutyUsedDayRemitAmount = clmsDutyDetailBillSettleMapper.getDutyUsedDayRemitAmount(remitAmountDTO);
                        dutyUsedDayRemitAmount = dutyUsedDayRemitAmount.stream().filter(i -> !reportNo.equals(i.getReportNo())).collect(Collectors.toList());
                        BigDecimal useRemit = BigDecimal.ZERO;
                        if(!CollectionUtils.isEmpty(dutyUsedDayRemitAmount)){
                            for (ClmsDutyDetailBillSettleDTO clmsDutyDetailBillSettleDTO : dutyUsedDayRemitAmount) {
                                useRemit = useRemit.add(null == clmsDutyDetailBillSettleDTO.getExpendDayDeductible() ? BigDecimal.ZERO : clmsDutyDetailBillSettleDTO.getExpendDayDeductible());
                            }
                        }
                        BigDecimal subtract = remitAmount.subtract(useRemit);
                        if(subtract.compareTo(BigDecimal.ZERO) < 0){
                            subtract = BigDecimal.ZERO;
                        }
                        BigDecimal exRemit = remitDayMap.get(date);
                        if(exRemit.compareTo(subtract) > 0){
                            throw new GlobalBusinessException("当前案件" + DateUtils.parseToFormatString(date, DateUtils.SIMPLE_DATE_STR) + "抵扣日免赔额超过剩余日免赔额，请重新理算");
                        }
                        BigDecimal settleAmount = settleDayMap.get(date);
                        if(settleAmount.compareTo(BigDecimal.ZERO) > 0){
                            if(exRemit.compareTo(subtract) != 0){
                                throw new GlobalBusinessException("当前案件" + DateUtils.parseToFormatString(date, DateUtils.SIMPLE_DATE_STR) + "未抵扣完日免赔额，请重新理算");
                            }
                        }
                    }


                }
            }
        }
    }


    private void checkMonthPayDays(String reportNo, Integer caseTimes) {
        List<DutyAttributeValueDTO> dutyAttributeValueDTOList = dutyAttributeMapper.getDutyAttributePayDays(reportNo);
        if(CollectionUtils.isEmpty(dutyAttributeValueDTOList)){
           return;
        }
        List<DutyBillLimitInfoDTO> dtoLimitList=  dutyBillLimitInfoMapper.getReportNoPolicyBillLimitInfo(reportNo,caseTimes);
        if(CollectionUtils.isEmpty(dtoLimitList)){
            return;
        }
        //按保单号分组
        Map<String, List<DutyBillLimitInfoDTO>> dtuyListMap = dtoLimitList.stream().collect(Collectors.groupingBy(DutyBillLimitInfoDTO::getPolicyNo));
        for (Map.Entry<String, List<DutyBillLimitInfoDTO>> entry: dtuyListMap.entrySet()){
            // 得到保单的起止日期
            PolicyDto policyDto = clmsQueryPolicyInfoService.getPolicyDto(entry.getKey());
            // 根据起止日期计算月数
            List<PolicyMonthDto> monthDtoList = clmsGetPolicyMonthInfoService.getPolicyMonthInfo(policyDto.getPolicyStartDate(),
                            policyDto.getPolicyEndDate());
            List<DutyBillLimitInfoDTO> dutyBillLimitDtoList = entry.getValue();
            for (DutyBillLimitInfoDTO billLimt : dutyBillLimitDtoList) {
                PolicyMonthDto policyMonthDto =  getStartEndDate(billLimt.getBillDate(),monthDtoList);
                //查询发票日所在合同月已赔付的记录
                List<DutyBillLimitDto> dutyBillLimitDtoLists = getDutyLimitMonth(policyMonthDto,billLimt,reportNo,caseTimes);
                List<DutyAttributeValueDTO> resultList=  dutyAttributeValueDTOList.stream().filter(dutyAttributeValueDTO -> Objects.equals(dutyAttributeValueDTO.getPolicyNo(),billLimt.getPolicyNo())&&
                        Objects.equals(dutyAttributeValueDTO.getPlanCode(),billLimt.getPlanCode())&&
                        Objects.equals(dutyAttributeValueDTO.getDutyCode(),billLimt.getDutyCode())).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(resultList)){
                    throw new GlobalBusinessException("责任属性异常");
                }
                Integer confiDayDays= Integer.valueOf(resultList.get(0).getAttributeValue());
                List<DutyBillLimitDto> dutyBillList=  dutyBillLimitDtoLists.stream().filter(dutyBillLimitDto -> !Objects.equals(dutyBillLimitDto.getBillDate(),billLimt.getBillDate())).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(dutyBillList) && dutyBillList.size()>=confiDayDays){
                    throw new GlobalBusinessException("超每月赔付天数，请重新理算");
                }
            }

        }




    }

    /**
     * 校验保单月赔付天数
     * @param reportNo
     * @param caseTimes
     * @param policyPayDTOList
     */
    private void checkMonthFrequencyDayLimitPay(String reportNo, Integer caseTimes, List<PolicyPayDTO> policyPayDTOList) {
        for (PolicyPayDTO policyPayDTO : policyPayDTOList) {
            if(MapUtils.isEmpty(policyMonthFrequencyDayLimitMap) || !policyMonthFrequencyDayLimitMap.containsKey(policyPayDTO.getProductPackage())) {
                return;
            }

            Integer limitCount = policyMonthFrequencyDayLimitMap.get(policyPayDTO.getProductPackage());

            // 查询本责任赔付的账单信息：账单的日期
            List<DutyBillLimitInfoDTO> dtoLimitList = dutyBillLimitInfoMapper.getYearlyPayDaysInfo(reportNo, caseTimes);
            for (DutyBillLimitInfoDTO dutyBillLimitInfoDTO : dtoLimitList) {
                Date startTime = DateUtils.getFirstDayOfMonth(dutyBillLimitInfoDTO.getBillDate());
                Date endTime = DateUtils.getLastSecondOfMonth(dutyBillLimitInfoDTO.getBillDate());
                DutyLimitQueryVo dutyLimitQueryVo =new DutyLimitQueryVo();
                dutyLimitQueryVo.setPolicyNo(policyPayDTO.getPolicyNo());
                dutyLimitQueryVo.setReportNo(reportNo);
                dutyLimitQueryVo.setSatrtDate(startTime);
                dutyLimitQueryVo.setEndDate(endTime);
                List<DutyBillLimitDto> policyAlreadyPayTimes = dutyBillLimitInfoMapper.getPolicyAlreadyPayTimes(dutyLimitQueryVo);
                if(CollectionUtils.isEmpty(policyAlreadyPayTimes)){
                    continue;
                }
                int count = policyAlreadyPayTimes.size() + 1;
                if(limitCount < count){
                    log.info("核赔校验超保单自然月限赔付天数，reportNo：{}，policyNo：{}，billDate:{}", reportNo, policyPayDTO.getPolicyNo(), dutyBillLimitInfoDTO.getBillDate());
                    throw new GlobalBusinessException("超保单自然月限赔付天数，请重新理算。");
                }
            }
        }
    }

    /**
     * 校验保单合同月机构赔付天数
     * @param reportNo
     * @param caseTimes
     * @param policyPayDTOList
     */
    private void checkMonthOrgCountLimitPay(String reportNo, Integer caseTimes, List<PolicyPayDTO> policyPayDTOList) {
        for (PolicyPayDTO policyPayDTO : policyPayDTOList) {
            if(MapUtils.isEmpty(policyMonthOrgCountLimitMap) || !policyMonthOrgCountLimitMap.containsKey(policyPayDTO.getProductPackage())) {
                return;
            }

            Integer limitCount = policyMonthOrgCountLimitMap.get(policyPayDTO.getProductPackage());

            // 得到保单的起止日期
            PolicyDto policyDto = clmsQueryPolicyInfoService.getPolicyDto(policyPayDTO.getPolicyNo());
            // 根据起止日期计算月数
            List<PolicyMonthDto> monthDtoList =
                    clmsGetPolicyMonthInfoService.getPolicyMonthInfo(policyDto.getPolicyStartDate(),
                            policyDto.getPolicyEndDate());

            // 查询本责任赔付的账单信息：账单的日期
            ClmsDutyDetailBillSettleDTO clmsDutyDetailBillSettleQuery = new ClmsDutyDetailBillSettleDTO();
            clmsDutyDetailBillSettleQuery.setReportNo(reportNo);
            clmsDutyDetailBillSettleQuery.setCaseTimes(caseTimes);
            List<ClmsDutyDetailBillSettleDTO> clmsDutyDetailBillSettleList = clmsDutyDetailBillSettleMapper.getClmsDutyDetailBillSettleListCase(clmsDutyDetailBillSettleQuery);
            clmsDutyDetailBillSettleList = clmsDutyDetailBillSettleList.stream().filter(i -> null == i.getSettleAmount() || BigDecimal.ZERO.compareTo(i.getSettleAmount()) != 0).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(clmsDutyDetailBillSettleList)){
                continue;
            }
            Map<Integer, List<String>> thisMonthMap = new HashMap<>();
            for (ClmsDutyDetailBillSettleDTO clmsDutyDetailBillSettleDTO : clmsDutyDetailBillSettleList) {
                PolicyMonthDto startEndDate = getStartEndDate(clmsDutyDetailBillSettleDTO.getBillDate(), monthDtoList);
                if(null == startEndDate){
                    continue;
                }
                ClmsDutyDetailBillSettleDTO billSettleDTOQuery = new ClmsDutyDetailBillSettleDTO();
                billSettleDTOQuery.setPolicyNo(policyPayDTO.getPolicyNo());
                billSettleDTOQuery.setBillStartDate(startEndDate.getStartDate());
                billSettleDTOQuery.setBillEndDate(startEndDate.getEndDate());
                List<ClmsDutyDetailBillSettleDTO> clmsPayBillSettleList = clmsDutyDetailBillSettleMapper.getClmsPayBillSettleList(billSettleDTOQuery);
                clmsPayBillSettleList = clmsPayBillSettleList.stream().filter(i -> !i.getReportNo().equals(policyPayDTO.getReportNo()) && (null == i.getSettleAmount() || BigDecimal.ZERO.compareTo(i.getSettleAmount()) != 0)).collect(Collectors.toList());
//                Set<String> hospitalCollect = clmsPayBillSettleList.stream().map(i -> i.getHospitalCode() + i.getBillDate()).collect(Collectors.toSet());
                List<String> thisMonth = thisMonthMap.get(startEndDate.getMonth());
                if(CollectionUtils.isEmpty(thisMonth)){
                    thisMonth = new ArrayList<>();
                }
                clmsPayBillSettleList.add(clmsDutyDetailBillSettleDTO);
                for (ClmsDutyDetailBillSettleDTO billSettleDTO : clmsPayBillSettleList) {
                    boolean matchResult = false;
                    for (int i = 0; i < thisMonth.size(); i++) {
                        String matchKey = thisMonth.get(i);
                        if(matchKey.contains(billSettleDTO.getBillDate().toString())){
                            if (matchKey.contains(billSettleDTO.getHospitalCode()) || matchKey.contains(billSettleDTO.getHospitalName())) {
                                matchResult = true;
                                String newMatchKey = matchKey + billSettleDTO.getHospitalCode() + billSettleDTO.getHospitalName();
                                thisMonth.set(i, newMatchKey);
                                break;
                            }
                        }
                    }
                    if (!matchResult) {
                        thisMonth.add(billSettleDTO.getBillDate() + billSettleDTO.getHospitalCode() + billSettleDTO.getHospitalName());
                    }
                }
//                hospitalCollect.add(clmsDutyDetailBillSettleDTO.getHospitalCode() + clmsDutyDetailBillSettleDTO.getBillDate());
                Integer limitCountTemp = limitCount;
                //长鹅AB升级产品特殊处理
                if(specialProductPackage.contains(policyPayDTO.getProductPackage()) && 0 == startEndDate.getMonth()){
                    limitCountTemp = 2;
                }
                if(thisMonth.size() > limitCountTemp){
                    log.info("核赔校验超保单合同月机构限赔付天数，reportNo：{}，policyNo：{}，billDate:{}", reportNo, policyPayDTO.getPolicyNo(), clmsDutyDetailBillSettleDTO.getBillDate());
                    throw new GlobalBusinessException("超保单合同月机构限赔付天数，请重新理算，保单月" + startEndDate.getSdate() + "至" + startEndDate.getEDate());
                }
                thisMonthMap.put(startEndDate.getMonth(), thisMonth);

            }

        }
    }

    /**
     * 校验保单机构日赔付次数
     * @param reportNo
     * @param caseTimes
     * @param policyPayDTOList
     */
    private void checkDayOrgCountLimitPay(String reportNo, Integer caseTimes, List<PolicyPayDTO> policyPayDTOList) {
        for (PolicyPayDTO policyPayDTO : policyPayDTOList) {
            if(MapUtils.isEmpty(policyDayOrgCountLimitMap) || !policyDayOrgCountLimitMap.containsKey(policyPayDTO.getProductPackage())) {
                return;
            }

            Integer limitCount = policyDayOrgCountLimitMap.get(policyPayDTO.getProductPackage());

            // 查询本责任赔付的账单信息：账单的日期
            ClmsDutyDetailBillSettleDTO clmsDutyDetailBillSettleQuery = new ClmsDutyDetailBillSettleDTO();
            clmsDutyDetailBillSettleQuery.setReportNo(reportNo);
            clmsDutyDetailBillSettleQuery.setCaseTimes(caseTimes);
            List<ClmsDutyDetailBillSettleDTO> clmsDutyDetailBillSettleList = clmsDutyDetailBillSettleMapper.getClmsDutyDetailBillSettleListCase(clmsDutyDetailBillSettleQuery);
            clmsDutyDetailBillSettleList = clmsDutyDetailBillSettleList.stream().filter(i -> null == i.getSettleAmount() || BigDecimal.ZERO.compareTo(i.getSettleAmount()) != 0).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(clmsDutyDetailBillSettleList)){
                continue;
            }
            Map<Date, List<String>> thisDayMap = new HashMap<>();
            for (ClmsDutyDetailBillSettleDTO clmsDutyDetailBillSettleDTO : clmsDutyDetailBillSettleList) {
                ClmsDutyDetailBillSettleDTO billSettleDTOQuery = new ClmsDutyDetailBillSettleDTO();
                billSettleDTOQuery.setPolicyNo(policyPayDTO.getPolicyNo());
                billSettleDTOQuery.setBillStartDate(clmsDutyDetailBillSettleDTO.getBillDate());
                billSettleDTOQuery.setBillEndDate(clmsDutyDetailBillSettleDTO.getBillDate());
                List<ClmsDutyDetailBillSettleDTO> clmsPayBillSettleList = clmsDutyDetailBillSettleMapper.getClmsPayBillSettleList(billSettleDTOQuery);
                clmsPayBillSettleList = clmsPayBillSettleList.stream().filter(i -> !i.getReportNo().equals(policyPayDTO.getReportNo()) && (null == i.getSettleAmount() || BigDecimal.ZERO.compareTo(i.getSettleAmount()) != 0)).collect(Collectors.toList());
//                Set<String> hospitalCollect = clmsPayBillSettleList.stream().map(ClmsDutyDetailBillSettleDTO::getHospitalCode).collect(Collectors.toSet());
                List<String> thisDay = thisDayMap.get(clmsDutyDetailBillSettleDTO.getBillDate());
                if(CollectionUtils.isEmpty(thisDay)){
                    thisDay = new ArrayList<>();
                }
                // 先添加当前结算单据到列表中，再进行匹配逻辑处理
                clmsPayBillSettleList.add(clmsDutyDetailBillSettleDTO);
                for (ClmsDutyDetailBillSettleDTO billSettleDTO : clmsPayBillSettleList) {
                    boolean matchResult = false;
                    for (int i = 0; i < thisDay.size(); i++) {
                        String matchKey = thisDay.get(i);
                        if (matchKey.contains(billSettleDTO.getHospitalCode()) || matchKey.contains(billSettleDTO.getHospitalName())) {
                            matchResult = true;
                            String newMatchKey = matchKey + billSettleDTO.getHospitalCode() + billSettleDTO.getHospitalName();
                            thisDay.set(i, newMatchKey);
                            break;
                        }
                    }
                    if (!matchResult) {
                        thisDay.add(billSettleDTO.getHospitalCode() + billSettleDTO.getHospitalName());
                    }
                }
//                hospitalCollect.add(clmsDutyDetailBillSettleDTO.getHospitalCode());
                if(thisDay.size() > limitCount){
                    log.info("核赔校验超保单机构日赔付次数，reportNo：{}，policyNo：{}，billDate:{}", reportNo, policyPayDTO.getPolicyNo(), clmsDutyDetailBillSettleDTO.getBillDate());
                    throw new GlobalBusinessException("超保单机构日赔付次数，请重新理算。");
                }
                thisDayMap.put(clmsDutyDetailBillSettleDTO.getBillDate(), thisDay);


            }

        }
    }

    /**
     * 校验保单月赔付次数
     * @param reportNo
     * @param caseTimes
     * @param policyPayDTOList
     */
    private void checkMonthFrequencyCountLimitPay(String reportNo, Integer caseTimes, List<PolicyPayDTO> policyPayDTOList) {
        for (PolicyPayDTO policyPayDTO : policyPayDTOList) {
            if(MapUtils.isEmpty(policyMonthFrequencyCountLimitMap) || !policyMonthFrequencyCountLimitMap.containsKey(policyPayDTO.getProductPackage())) {
                return;
            }
            if(null == policyPayDTO.getPolicySumPay() || policyPayDTO.getPolicySumPay().compareTo(BigDecimal.ZERO) <= 0){
                return;
            }
            Integer limitCount = policyMonthFrequencyCountLimitMap.get(policyPayDTO.getProductPackage());

            ReportInfoEntity reportInfo = reportInfoMapper.getReportInfo(policyPayDTO.getReportNo());
            QueryAccidentVo queryAccidentVo = new QueryAccidentVo();
            queryAccidentVo.setPolicyNo(policyPayDTO.getPolicyNo());
            queryAccidentVo.setStartDate(DateUtils.getFirstDayOfMonth(reportInfo.getReportDate()));
            queryAccidentVo.setEndDate(DateUtils.getLastSecondOfMonth(reportInfo.getReportDate()));
            List<String> reportInfoCount = reportInfoMapper.getReportInfoCount(queryAccidentVo);
            if (CollectionUtils.isEmpty(reportInfoCount) || reportInfoCount.size() < limitCount) {
                return;
            }
            log.info("核赔校验超保单自然月限赔付次数，reportNo：{}，policyNo：{}", reportNo, policyPayDTO.getPolicyNo());
            throw new GlobalBusinessException("超保单自然月限赔付次数，请重新理算。");

        }
    }

    private PolicyMonthDto getStartEndDate(Date billDate, List<PolicyMonthDto> monthDtoList) {

        for (PolicyMonthDto monthDto : monthDtoList) {
            if(billDate.compareTo(monthDto.getStartDate())>=0 && billDate.compareTo(monthDto.getEndDate())<=0){
                return monthDto;
            }
        }
        return null;
    }

    /**
     * 查询合同月内已赔付的记录
     *
     * @param policyMonthDto
     * @param billLimt
     * @param reportNo
     * @param caseTimes
     * @return
     */
    private List<DutyBillLimitDto> getDutyLimitMonth(PolicyMonthDto policyMonthDto, DutyBillLimitInfoDTO billLimt, String reportNo, Integer caseTimes) {
        DutyLimitQueryVo dutyLimitQueryVo =new DutyLimitQueryVo();
        dutyLimitQueryVo.setPolicyNo(billLimt.getPolicyNo());
        dutyLimitQueryVo.setPlanCode(billLimt.getPlanCode());
        dutyLimitQueryVo.setDutyCode(billLimt.getDutyCode());
        dutyLimitQueryVo.setSatrtDate(policyMonthDto.getStartDate());
        dutyLimitQueryVo.setEndDate(policyMonthDto.getEndDate());
        LogUtil.info("核赔校验每月赔付次数报案号={}，责任查询参数={}",reportNo, JsonUtils.toJsonString(dutyLimitQueryVo));
        List<DutyBillLimitDto> dtos= dutyBillLimitInfoMapper.getAllAlreadyPayTimes(dutyLimitQueryVo);
        if(org.springframework.util.CollectionUtils.isEmpty(dtos)){
            return dtos;
        }
        if(caseTimes>1){
            //重开案件去掉当前报案号对应的发票日期
            dtos= dtos.stream().filter(dutyBillLimitDto -> !Objects.equals(reportNo,dutyBillLimitDto.getReportNo())).collect(Collectors.toList());
        }
        List<DutyBillLimitDto> returnList=new ArrayList<>();
        Map<Date,List<DutyBillLimitDto>>  mothListMap=dtos.stream().sorted(Comparator.comparing(DutyBillLimitDto::getBillDate)).collect(Collectors.groupingBy(DutyBillLimitDto::getBillDate,LinkedHashMap::new,Collectors.toList()));
        for (Map.Entry<Date,List<DutyBillLimitDto>> entry : mothListMap.entrySet()){
            List<DutyBillLimitDto> evDayList = entry.getValue();
            List<String> caseNoList=getCaseNoList(evDayList);
            Integer count= dutyDetailPayMapper.getIndmenityInfo(caseNoList,billLimt.getDutyCode());
            if(count>=1){
                DutyBillLimitDto dto=new DutyBillLimitDto();
                dto.setBillDate(entry.getKey());
                returnList.add(dto);
            }
        }
        return returnList;
    }
    private List<String> getCaseNoList(List<DutyBillLimitDto> evDayList) {
        List<String> caseNolist=new ArrayList<>();
        for (DutyBillLimitDto dto : evDayList) {
            caseNolist.add(dto.getCaseNo());
        }
        return caseNolist;
    }

}
