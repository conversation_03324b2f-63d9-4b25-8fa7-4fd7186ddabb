package com.paic.ncbs.claim.service.sop;

import com.paic.ncbs.claim.dao.entity.sop.ClmsSopFile;
import com.paic.ncbs.claim.model.dto.sop.SopFileDTO;
import com.paic.ncbs.claim.model.vo.sop.SopFileVO;
import com.paic.ncbs.claim.service.base.BaseService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * SOP文件管理Service接口
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
public interface SopFileService {

    /**
     * 根据SOP主键获取文件列表
     *
     * @param idSopMain SOP主键
     * @return 文件列表
     */
    List<SopFileVO> getFileListByIdSopMain(String idSopMain);

    /**
     * 上传SOP文件
     *
     * @param idSopMain SOP主键
     * @param files 文件列表
     * @return 上传结果
     */
    List<SopFileVO> uploadSopFiles(String idSopMain, MultipartFile[] files);

    /**
     * 删除SOP文件
     *
     * @param idSopFile 文件主键
     * @return 操作结果
     */
    String deleteSopFile(String idSopFile);

    /**
     * 批量保存SOP文件
     *
     * @param idSopMain SOP主键
     * @param fileList 文件列表
     * @return 操作结果
     */
    String batchSaveSopFiles(String idSopMain, List<SopFileDTO> fileList);

    /**
     * 根据SOP主键删除所有文件
     *
     * @param idSopMain SOP主键
     * @return 操作结果
     */
    String deleteFilesByIdSopMain(String idSopMain);

    /**
     * 获取文件下载地址
     *
     * @param idSopFile 文件主键
     * @return 下载地址
     */
    String getFileDownloadUrl(String idSopFile);

    /**
     * 复制SOP文件到新的SOP
     *
     * @param sourceIdSopMain 源SOP主键
     * @param targetIdSopMain 目标SOP主键
     * @return 操作结果
     */
    String copySopFiles(String sourceIdSopMain, String targetIdSopMain);

}
