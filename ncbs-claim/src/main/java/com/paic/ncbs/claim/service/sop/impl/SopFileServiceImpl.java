package com.paic.ncbs.claim.service.sop.impl;

import com.paic.ncbs.claim.common.util.RapeCollectionUtils;
import com.paic.ncbs.claim.common.util.RapeStringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.sop.ClmsSopFile;
import com.paic.ncbs.claim.dao.mapper.sop.ClmsSopFileMapper;
import org.apache.commons.lang3.StringUtils;
import java.io.Serializable;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.sop.SopFileDTO;
import com.paic.ncbs.claim.model.vo.sop.SopFileVO;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import com.paic.ncbs.claim.service.iobs.IOBSFileUploadService;
import com.paic.ncbs.claim.service.sop.SopFileService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * SOP文件管理Service实现类
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Slf4j
@Service
public class SopFileServiceImpl implements SopFileService {

    @Autowired
    private ClmsSopFileMapper clmsSopFileMapper;

    @Autowired
    private IOBSFileUploadService iobsFileUploadService;

    @Override
    public List<SopFileVO> getFileListByIdSopMain(String idSopMain) {
        log.info("获取SOP文件列表，idSopMain：{}", idSopMain);

        
        List<ClmsSopFile> entityList = clmsSopFileMapper.selectByIdSopMain(idSopMain);
        List<SopFileVO> voList = new ArrayList<>();
        
        if (!RapeCollectionUtils.isEmpty(entityList)) {
            for (ClmsSopFile entity : entityList) {
                SopFileVO vo = new SopFileVO();
                BeanUtils.copyProperties(entity, vo);
                // 设置下载地址
                vo.setDownloadUrl(getFileDownloadUrl(entity.getIdSopFile()));
                voList.add(vo);
            }
        }
        
        log.info("获取SOP文件列表完成，共{}个文件", voList.size());
        return voList;
    }

    @Override
    @Transactional
    public List<SopFileVO> uploadSopFiles(String idSopMain, MultipartFile[] files) {
        log.info("上传SOP文件，idSopMain：{}，文件数量：{}", idSopMain, files != null ? files.length : 0);

        
        if (files == null || files.length == 0) {
            throw new GlobalBusinessException("请选择要上传的文件");
        }
        
        UserInfoDTO userInfo = getUserInfo();
        LocalDateTime now = LocalDateTime.now();
        List<SopFileVO> resultList = new ArrayList<>();
        
        for (MultipartFile file : files) {
            if (file.isEmpty()) {
                continue;
            }
            
            try {
                // 校验文件类型
                String fileName = file.getOriginalFilename();
                String fileFormat = getFileFormat(fileName);
                if (!isValidFileFormat(fileFormat)) {
                    throw new GlobalBusinessException("不支持的文件格式：" + fileFormat);
                }
                
                // 上传文件到IOBS
                String fileId = iobsFileUploadService.uploadFileToFilePlatform(fileName, file.getBytes());
                String fileUrl = iobsFileUploadService.getPerpetualDownloadUrl(fileId, fileName);
                
                // 保存文件信息
                ClmsSopFile entity = new ClmsSopFile();
                entity.setIdSopFile(UuidUtil.getUUID());
                entity.setIdSopMain(idSopMain);
                entity.setFileId(fileId);
                entity.setFileUrl(fileUrl);
                entity.setFileName(fileName);
                entity.setFileFormat(fileFormat);
                entity.setFileType("SOP");
                entity.setUploadTime(now);
                entity.setValidFlag("Y");
                entity.setCreatedBy(userInfo.getUserCode());
                entity.setSysCtime(now);
                entity.setUpdatedBy(userInfo.getUserCode());
                entity.setSysUtime(now);
                
                clmsSopFileMapper.insertSelective(entity);
                
                // 转换为VO
                SopFileVO vo = new SopFileVO();
                BeanUtils.copyProperties(entity, vo);
                vo.setDownloadUrl(fileUrl);
                vo.setFileSize(formatFileSize(file.getSize()));
                resultList.add(vo);
                
                log.info("上传文件成功，fileName：{}，fileId：{}", fileName, fileId);
                
            } catch (Exception e) {
                log.error("上传文件失败，fileName：{}", file.getOriginalFilename(), e);
                throw new GlobalBusinessException("上传文件失败：" + e.getMessage());
            }
        }
        
        log.info("上传SOP文件完成，成功上传{}个文件", resultList.size());
        return resultList;
    }

    @Override
    @Transactional
    public String deleteSopFile(String idSopFile) {
        log.info("删除SOP文件，idSopFile：{}", idSopFile);
        
        RapeStringUtils.checkIsEmpty(idSopFile, "文件主键不能为空");
        
        ClmsSopFile entity = clmsSopFileMapper.selectByPrimaryKey(idSopFile);
        if (entity == null) {
            throw new GlobalBusinessException("文件不存在");
        }
        
        // 删除IOBS文件
        try {
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(entity.getFileId())) {
                iobsFileUploadService.deleteByFileId(entity.getFileId());
            }
        } catch (Exception e) {
            log.warn("删除IOBS文件失败，fileId：{}", entity.getFileId(), e);
        }
        
        // 删除数据库记录
        clmsSopFileMapper.deleteByPrimaryKey(idSopFile);
        
        log.info("删除SOP文件完成，idSopFile：{}", idSopFile);
        return "删除成功";
    }

    @Override
    @Transactional
    public String batchSaveSopFiles(String idSopMain, List<SopFileDTO> fileList) {
        log.info("批量保存SOP文件，idSopMain：{}，文件数量：{}", idSopMain, fileList != null ? fileList.size() : 0);

        
        if (RapeCollectionUtils.isEmpty(fileList)) {
            return "保存成功";
        }
        
        UserInfoDTO userInfo = getUserInfo();
        LocalDateTime now = LocalDateTime.now();
        List<ClmsSopFile> entityList = new ArrayList<>();
        
        for (SopFileDTO fileDTO : fileList) {
            ClmsSopFile entity = new ClmsSopFile();
            BeanUtils.copyProperties(fileDTO, entity);
            
            if (org.apache.commons.lang3.StringUtils.isEmpty(entity.getIdSopFile())) {
                entity.setIdSopFile(UuidUtil.getUUID());
                entity.setCreatedBy(userInfo.getUserCode());
                entity.setSysCtime(now);
            }
            
            entity.setIdSopMain(idSopMain);
            entity.setUpdatedBy(userInfo.getUserCode());
            entity.setSysUtime(now);
            entityList.add(entity);
        }
        
        clmsSopFileMapper.batchInsert(entityList);
        
        log.info("批量保存SOP文件完成，保存{}个文件", entityList.size());
        return "保存成功";
    }

    @Override
    @Transactional
    public String deleteFilesByIdSopMain(String idSopMain) {
        log.info("删除SOP所有文件，idSopMain：{}", idSopMain);
        
        List<ClmsSopFile> fileList = clmsSopFileMapper.selectByIdSopMain(idSopMain);
        
        if (!RapeCollectionUtils.isEmpty(fileList)) {
            for (ClmsSopFile file : fileList) {
                try {
                    if (RapeStringUtils.isNotEmpty(file.getFileId())) {
                        iobsFileUploadService.deleteByFileId(file.getFileId());
                    }
                } catch (Exception e) {
                    log.warn("删除IOBS文件失败，fileId：{}", file.getFileId(), e);
                }
            }
        }
        
        clmsSopFileMapper.deleteByIdSopMain(idSopMain);
        
        log.info("删除SOP所有文件完成，删除{}个文件", fileList.size());
        return "删除成功";
    }

    @Override
    public String getFileDownloadUrl(String idSopFile) {
        ClmsSopFile entity = clmsSopFileMapper.selectByPrimaryKey(idSopFile);
        
        try {
            return iobsFileUploadService.getPerpetualDownloadUrl(entity.getFileId(), entity.getFileName());
        } catch (Exception e) {
            log.error("获取文件下载地址失败，idSopFile：{}", idSopFile, e);
            return null;
        }
    }

    @Override
    @Transactional
    public String copySopFiles(String sourceIdSopMain, String targetIdSopMain) {
        log.info("复制SOP文件，sourceIdSopMain：{}，targetIdSopMain：{}", sourceIdSopMain, targetIdSopMain);
        
        RapeStringUtils.checkIsEmpty(sourceIdSopMain, "源SOP主键不能为空");
        RapeStringUtils.checkIsEmpty(targetIdSopMain, "目标SOP主键不能为空");
        
        List<ClmsSopFile> sourceFiles = clmsSopFileMapper.selectByIdSopMain(sourceIdSopMain);
        
        if (!RapeCollectionUtils.isEmpty(sourceFiles)) {
            UserInfoDTO userInfo = getUserInfo();
            LocalDateTime now = LocalDateTime.now();
            List<ClmsSopFile> targetFiles = new ArrayList<>();
            
            for (ClmsSopFile sourceFile : sourceFiles) {
                ClmsSopFile targetFile = new ClmsSopFile();
                BeanUtils.copyProperties(sourceFile, targetFile);
                targetFile.setIdSopFile(UuidUtil.getUUID());
                targetFile.setIdSopMain(targetIdSopMain);
                targetFile.setCreatedBy(userInfo.getUserCode());
                targetFile.setSysCtime(now);
                targetFile.setUpdatedBy(userInfo.getUserCode());
                targetFile.setSysUtime(now);
                targetFiles.add(targetFile);
            }
            
            clmsSopFileMapper.batchInsert(targetFiles);
            log.info("复制SOP文件完成，复制{}个文件", targetFiles.size());
        } else {
            log.info("源SOP没有文件，无需复制");
        }
        
        return "复制成功";
    }

    /**
     * 获取文件格式
     */
    private String getFileFormat(String fileName) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(fileName)) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf(".");
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        
        return "";
    }

    /**
     * 校验文件格式是否有效
     */
    private boolean isValidFileFormat(String fileFormat) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(fileFormat)) {
            return false;
        }
        
        String[] validFormats = {"doc", "docx", "xls", "xlsx", "pdf", "txt"};
        for (String validFormat : validFormats) {
            if (validFormat.equalsIgnoreCase(fileFormat)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 获取当前用户信息
     */
    private UserInfoDTO getUserInfo() {
        return WebServletContext.getUser();
    }

}
