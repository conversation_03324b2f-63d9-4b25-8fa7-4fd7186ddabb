package com.paic.ncbs.claim.common.constant.communicate;

import java.util.HashMap;
import java.util.Map;

public class CommunicateConsts {


    public static final String COMMUNICATE_STATUS_PENDING = "0";
    public static final String COMMUNICATE_STATUS_PROCESSING = "1";
    public static final String COMMUNICATE_STATUS_FINISH = "2";


    public static final String COMMUNICATE_TASK_STATUS_PENDING = "0";
    public static final String COMMUNICATE_TASK_STATUS_FINISH = "1";


    public static final String COMMUNICATE_ROLE_COMMUNICATER = "0";
    public static final String COMMUNICATE_ROLE_INITIATOR = "1";


    public static final String AHCS_GTHJ = "AHCS_GTHJ";


    public static final String AHCS_GT_TYPE = "AHCS_GT_TYPE";


    public static final String AHCS_GTZT = "AHCS_GTZT";


    public static final int ZERO = 0;
    public static final int ONE = 1;
    public static final int TWO = 2;


    public static final String MEDICAL_INSURANCE_CONSULT = "01";

    public static final String NEGOTIATE = "02";

    public static final String DOCUMENTS_COMMUNICATE = "03";

    public static final String FREE_APPRAISAL = "04";

    public static final String OTHER_CONSULT = "05";

    public static final String ZERO_REJECT_COMMUNICATE = "06";

    public static final String UW_NEGOTIATE = "07";


    public static final Map<String, String> COMMUNICATE_TITLE_MAP = new HashMap<String, String>();
    static {
        COMMUNICATE_TITLE_MAP.put(MEDICAL_INSURANCE_CONSULT, "医保咨询");
        COMMUNICATE_TITLE_MAP.put(NEGOTIATE, "协谈");
        COMMUNICATE_TITLE_MAP.put(DOCUMENTS_COMMUNICATE, "单证沟通");
        COMMUNICATE_TITLE_MAP.put(FREE_APPRAISAL, "免鉴定");
        COMMUNICATE_TITLE_MAP.put(OTHER_CONSULT, "其他咨询");
        COMMUNICATE_TITLE_MAP.put(ZERO_REJECT_COMMUNICATE, "零注拒沟通");
        COMMUNICATE_TITLE_MAP.put(UW_NEGOTIATE, "理赔核保协谈");
    }

}
