package com.paic.ncbs.claim.service.sop;

import com.paic.ncbs.claim.model.vo.sop.SopMainVO;

import java.util.List;

/**
 * SOP匹配服务接口
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
public interface SopMatchService {

    /**
     * 根据案件信息匹配SOP规则
     *
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @param taskDefinitionBpmKey 当前环节代码
     * @return 匹配的SOP列表
     */
    List<SopMainVO> matchSopByCase(String reportNo, Integer caseTimes, String taskDefinitionBpmKey);

    /**
     * 根据产品、方案、险种、环节匹配SOP规则
     *
     * @param productCode 产品代码
     * @param planCode 方案代码
     * @param dutyCode 险种代码
     * @param taskDefinitionBpmKey 环节代码
     * @return 匹配的SOP列表
     */
    List<SopMainVO> matchSopByConditions(String productCode, String planCode, String dutyCode, String taskDefinitionBpmKey);

    /**
     * 获取案件的产品信息
     *
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 产品代码
     */
    String getCaseProductCode(String reportNo, Integer caseTimes);

    /**
     * 获取案件的方案信息
     *
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 方案代码
     */
    String getCasePlanCode(String reportNo, Integer caseTimes);

    /**
     * 获取案件的险种信息
     *
     * @param reportNo 报案号
     * @param caseTimes 赔付次数
     * @return 险种代码列表
     */
    List<String> getCaseDutyCodes(String reportNo, Integer caseTimes);

}
