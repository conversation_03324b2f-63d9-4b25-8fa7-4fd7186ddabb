package com.paic.ncbs.claim.model.dto.investigate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
@ApiModel(description = "调查任务表VO")
public class InvestigateTaskDTO extends EntityDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String idAhcsInvestigateTask;

    @ApiModelProperty(value = "ahcs_investigate表关联id")
    private String idAhcsInvestigate;

    @ApiModelProperty(value = "报案号")
    private String reportNo;

    @ApiModelProperty(value = "赔付次数")
    private Integer caseTimes;

    @ApiModelProperty(value = "调查人(UM)")
    private String investigatorUm;

    @ApiModelProperty(value = "调查人姓名")
    private String investigatorUmName;

    @ApiModelProperty(value = "分配人(UM)")
    private String dispatchUm;

    @ApiModelProperty(value = "任务状态", example = "1、调查处理，2、提调审批，3、提请协助，4、调查任务审核，5、完成任务")
    private String taskStatus;

    @ApiModelProperty(value = "调查机构")
    private String investigateDepartment;

    @ApiModelProperty(value = "调查机构名")
    private String investigateDepartmentName;

    @ApiModelProperty(value = "是否为主调查任务")
    private String isPrimaryTask;

    @ApiModelProperty(value = "是否为异地调查任务")
    private String isOffsiteTask;

    @ApiModelProperty(value = "分配信息/协查事项/分配意见")
    private String dispatchOpinion;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "完成日期")
    private Date finishDate;

    @ApiModelProperty(value = "调查结论")
    private String investigateConclusion;

    @ApiModelProperty(value = "调查定性")
    private String investigateQualitative;

    @ApiModelProperty(value = "异常子项")
    private String abnormalDetail;

    @ApiModelProperty(value = "证据资料")
    private String hasEvidence;

    @ApiModelProperty(value = "证据资料子项")
    private String evidenceDetail;

    @ApiModelProperty(value = "关联审批表id")
    private String idAhcsInvestigateAudit;

    @ApiModelProperty(value = "意见")
    private String operation;

    @ApiModelProperty(value = "")
    private String isStorage;

    @ApiModelProperty(value = "是否包含公估费")
    private String isHasAdjustingFee;

    @ApiModelProperty(value = "公估费")
    private BigDecimal commonEstimateFee  ;

    @ApiModelProperty("任务类型，1-线上委托 2-线下委托")
    private String taskType;

    @ApiModelProperty(value = "调查委托书文件ID")
    private String fileId;
}