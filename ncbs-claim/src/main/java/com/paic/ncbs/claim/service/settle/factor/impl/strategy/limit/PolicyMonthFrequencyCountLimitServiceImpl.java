package com.paic.ncbs.claim.service.settle.factor.impl.strategy.limit;

import cn.hutool.core.collection.CollectionUtil;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoMapper;
import com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO;
import com.paic.ncbs.claim.model.dto.duty.DutyPayDTO;
import com.paic.ncbs.claim.model.dto.report.QueryAccidentVo;
import com.paic.ncbs.claim.model.dto.settle.PlanPayDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPayDTO;
import com.paic.ncbs.claim.model.dto.settle.factor.BIllSettleResultDTO;
import com.paic.ncbs.claim.service.settle.factor.interfaces.strategy.limit.ExtendedLimitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;


/**
 * 定制化产品方案月赔付次数实现
 */
@Slf4j
@Service
@Order(2)
@RefreshScope
public class PolicyMonthFrequencyCountLimitServiceImpl implements ExtendedLimitService {
    @Autowired
    private ReportInfoMapper reportInfoMapper;

    /**
     * 保单月限额配置方案
     */
//    @Value("#{${policyLimit.monthFrequencyCount}}")
    private Map<String, Integer> policyMonthFrequencyLimitMap;

    /**
     * 执行保单是否匹配的本扩展
     *
     * @param policyPayDTO
     * @return
     */
    @Override
    public boolean isMatch(PolicyPayDTO policyPayDTO) {
        return policyMonthFrequencyLimitMap != null && policyMonthFrequencyLimitMap.containsKey(policyPayDTO.getProductPackage());
    }

    @Override
    public void cumulativeLimit(PolicyPayDTO policyPayDTO) {
        if (!isMatch(policyPayDTO)) {
            return;
        }
        log.info("案件:{},配置了保单月赔付次数开始处理！", policyPayDTO.getReportNo());
        Integer limitCount = policyMonthFrequencyLimitMap.get(policyPayDTO.getProductPackage());
        ReportInfoEntity reportInfo = reportInfoMapper.getReportInfo(policyPayDTO.getReportNo());
        QueryAccidentVo queryAccidentVo = new QueryAccidentVo();
        queryAccidentVo.setPolicyNo(policyPayDTO.getPolicyNo());
        queryAccidentVo.setStartDate(DateUtils.getFirstDayOfMonth(reportInfo.getReportDate()));
        queryAccidentVo.setEndDate(DateUtils.getLastSecondOfMonth(reportInfo.getReportDate()));
        List<String> reportInfoCount = reportInfoMapper.getReportInfoCount(queryAccidentVo);
        if (CollectionUtils.isEmpty(reportInfoCount) || reportInfoCount.size() < limitCount) {
            return;
        }

        List<PlanPayDTO> plans = policyPayDTO.getPlanPayArr();
        for (PlanPayDTO planPayDTO : plans) {
            List<DutyPayDTO> dutyPayDTOS = planPayDTO.getDutyPayArr();
            for (DutyPayDTO dutyPayDTO : dutyPayDTOS) {
                List<DutyDetailPayDTO> details = dutyPayDTO.getDutyDetailPayArr();
                for (DutyDetailPayDTO detailPayDTO : details) {
                    if (CollectionUtil.isNotEmpty(detailPayDTO.getBillSettleResultDTOList())) {
                        for (BIllSettleResultDTO bIllSettleResultDTO : detailPayDTO.getBillSettleResultDTOList()) {
                            updateBillSettleResult(bIllSettleResultDTO, limitCount);
                        }
                    }
                }
            }
        }

    }


    /**
     * 更新责任明细发票数据
     */
    private void updateBillSettleResult(BIllSettleResultDTO bIllSettleResultDTO, Integer limitCount) {
        bIllSettleResultDTO.setAutoSettleAmount(BigDecimal.ZERO);
        bIllSettleResultDTO.setExceedMothPayDays("Y");
        bIllSettleResultDTO.setRemark("案件所在自然月累计赔付次数已达月限次数" + limitCount + "次，本次可赔付金额为0");
        log.info("报案号={},所在自然月已超月限次 该发票责任明细发票理算数据为0", bIllSettleResultDTO.getReportNo());
    }


}
