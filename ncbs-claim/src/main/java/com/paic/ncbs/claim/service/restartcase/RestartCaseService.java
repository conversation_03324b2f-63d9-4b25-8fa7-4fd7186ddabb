package com.paic.ncbs.claim.service.restartcase;

import com.paic.ncbs.claim.dao.entity.restartcase.RestartCaseRecordEntity;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyFormDTO;
import com.paic.ncbs.claim.model.dto.report.RestartCaseRecordDTO;
import com.paic.ncbs.claim.model.dto.restartcase.ApprovalProcessDTO;
import com.paic.ncbs.claim.model.vo.endcase.WholeCasePageResult;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO;
import com.paic.ncbs.claim.model.vo.restartcase.RestartCaseVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @date 2023-05-19 14:02
 */
public interface RestartCaseService {

    WholeCasePageResult getHistoryCaseList(WholeCaseVO wholeCaseVO) throws GlobalBusinessException;


    List<RestartCaseRecordEntity> getRestartCaseList(String reportNo, Integer caseTimes) throws GlobalBusinessException;


    void addRestartCase(RestartCaseVO restartCaseVO);

    void updateRestartCase(RestartCaseVO restartCaseVO);

    void approvalProcess(ApprovalProcessDTO approvalProcessDTO);

    void giveUpRestart(ApprovalProcessDTO approvalProcessDTO);
    EstimatePolicyFormDTO getRestartCaseDetail(String reportNo, Integer caseTimes);

    EstimatePolicyFormDTO getRestartModifyCaseDetail(String reportNo, Integer caseTimes);
    EstimatePolicyFormDTO getRestartCaseApprovalDetail(String reportNo, Integer caseTimes);

    List<Map<String,Object>> getHistoryReportPayList(String reportNo, Integer caseTimes);

    void updateIsNewest(String reportNo, Integer caseTimes);
    List<RestartCaseRecordDTO> getRestratCases(String reportNo, Integer caseTimes);

    List<RestartCaseRecordDTO> getThisRestratCases(String reportNo, Integer caseTimes);
    List<RestartCaseRecordEntity> getThisRestartCaseList(String reportNo, Integer caseTimes) throws GlobalBusinessException;

}
