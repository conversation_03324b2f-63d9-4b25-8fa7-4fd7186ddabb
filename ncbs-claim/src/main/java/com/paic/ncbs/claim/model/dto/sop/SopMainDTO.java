package com.paic.ncbs.claim.model.dto.sop;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * SOP信息DTO
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Data
@ApiModel("SOP信息")
public class SopMainDTO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    private String idSopMain;

    @ApiModelProperty("sop名称")
    private String sopName;

    @ApiModelProperty("版本号")
    private String versionNo;

    @ApiModelProperty("sop简要描述")
    private String sopDescription;

    @ApiModelProperty("批次号")
    private String batchNo;

    @ApiModelProperty("是否全流程（Y/N）")
    private String isAllProcess;

    @ApiModelProperty("sop规则内容")
    private String sopContent;

    @ApiModelProperty("发布人员")
    private String publisherCode;

    @ApiModelProperty("发布人员姓名")
    private String publisherName;

    @ApiModelProperty("发布时间")
    private LocalDateTime publishTime;

    @ApiModelProperty("是否有效（Y/N）")
    private String validFlag;

    @ApiModelProperty("生效日期")
    private LocalDateTime effectiveDate;

    @ApiModelProperty("失效日期")
    private LocalDateTime invalidDate;

    @ApiModelProperty("状态（01-暂存、02-有效、03-无效）")
    private String status;

    @ApiModelProperty("文件类型（01-文本 02-文件 03-所有）")
    private String fileType;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("SOP文件列表")
    private List<SopFileDTO> fileList;

    @ApiModelProperty("SOP配置列表")
    private List<SopConfigDTO> configList;

    @ApiModelProperty("适用产品列表")
    private List<String> productCodes;

    @ApiModelProperty("适用方案列表")
    private List<String> groupCodes;

    @ApiModelProperty("适用险种列表")
    private List<String> planCodes;

    @ApiModelProperty("适用环节列表")
    private List<String> taskBpmKeys;

    @ApiModelProperty("状态名称")
    private String statusName;

    @ApiModelProperty("文件类型名称")
    private String fileTypeName;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("系统创建时间")
    private LocalDateTime sysCtime;

    @ApiModelProperty("修改人")
    private String updatedBy;

    @ApiModelProperty("系统更新时间")
    private LocalDateTime sysUtime;

    @ApiModelProperty("操作类型（01-暂存、02-发布、03-停用、04-新增）")
    private String initFlag;

}
