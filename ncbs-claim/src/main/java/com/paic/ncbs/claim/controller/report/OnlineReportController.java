package com.paic.ncbs.claim.controller.report;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.batch.AutoCloseRequestVO;
import com.paic.ncbs.claim.model.vo.batch.OnlineBatchAutoClose;
import com.paic.ncbs.claim.model.vo.batch.OnlineBatchResponse;
import com.paic.ncbs.claim.model.vo.endcase.OneTimeCloseRequestVO;
import com.paic.ncbs.claim.model.vo.openapi.OneTimeCloseCaseRequest;
import com.paic.ncbs.claim.model.vo.openapi.OneTimeCloseCaseResponse;
import com.paic.ncbs.claim.model.vo.report.*;
import com.paic.ncbs.claim.service.ahcs.BatchAutoCloseService;
import com.paic.ncbs.claim.service.compensation.BatchAutoCloseCompensationService;
import com.paic.ncbs.claim.service.report.OnlineReportService;
import com.paic.ncbs.claim.service.report.ReportInfoService;
import com.paic.ncbs.claim.utils.JsonUtils;
import io.swagger.annotations.Api;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 线上保单
 */
@Api(tags = "线上保单-微保/美团点评-渠道-报案")
@RestController
@Validated
@RequestMapping("/public/report")
public class OnlineReportController {
    
    @Autowired
    private OnlineReportService onlineReportService;
    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;
    @Autowired
    private ReportInfoService reportInfoService;
    @Autowired
    private BatchAutoCloseService batchAutoCloseService;
    @Autowired
    private BatchAutoCloseCompensationService batchAutoCloseCompensationService;

    /**
     * 线上保单校验接口
     * @param onlineReportCheckVO
     * @return
     */
    @PostMapping(value = "/checkOnlineReport",produces = {"application/json;charset=utf-8"})
    public ResponseResult<Object> checkOnlineReport(@RequestBody OnlineReportCheckVO onlineReportCheckVO) {
        LogUtil.info("线上报案-报案前检查接口-入参={}", JSON.toJSONString(onlineReportCheckVO));
        OnlineReportCheckResponseVO onlineReportCheckResponseVO;
        try {
            onlineReportCheckResponseVO = onlineReportService.checkOnlineReport(onlineReportCheckVO);
        } catch (GlobalBusinessException e) {
            LogUtil.info("线上报案-报案前参数校验失败={}", e);
            return ResponseResult.fail(GlobalResultStatus.FAIL.getCode(),"线上报案参数-报案前校验-" + e.getMessage());
        }catch (Exception exception) {
            LogUtil.error("线上报案前检查失败={}", exception);
            return ResponseResult.fail(GlobalResultStatus.FAIL.getCode(),"线上报案前检查" + exception.getMessage());
        }
        return ResponseResult.success(onlineReportCheckResponseVO);
    }

    /**
     * 线上保单接口
     * @param onlineReportVO
     * @return
     */
    @PostMapping(value = "/saveOnlineReport",produces = {"application/json;charset=utf-8"})
    public ResponseResult<Object> saveOnlineReport(@RequestBody OnlineReportVO onlineReportVO) {
        LogUtil.info("线上报案-入参={}", JSON.toJSONString(onlineReportVO));
        OnlineReportResponseVO onlineReportResponseVO;
        try {
            onlineReportResponseVO = onlineReportService.saveOnlineReport(onlineReportVO);
        } catch (GlobalBusinessException e) {
            LogUtil.info("线上报案参数校验失败={}", e);
            return ResponseResult.fail(GlobalResultStatus.FAIL.getCode(),"线上报案参数校验失败" + e.getMessage());
        }catch (Exception exception) {
            LogUtil.error("线上报案失败={}", exception);
            return ResponseResult.fail(GlobalResultStatus.FAIL.getCode(),"线上报案失败" + exception.getMessage());
        }
        return ResponseResult.success(onlineReportResponseVO);
    }

    /**
     *  官网查询接口 根据报案号查询报案详情
     * @param reportNo
     * @return
     */
    @GetMapping(value = "/getOnlineReportInfo",produces = {"application/json;charset=utf-8"})
    public ResponseResult<Object> getReportNoInfo(@RequestParam(value ="reportNo" ,required = false) String reportNo,
                                                  @RequestParam(value ="policyNo" ,required = false) String policyNo) {
        LogUtil.info("官网查询接口2-入参-reportNo={},policyNo={}", reportNo,policyNo);
        if (StringUtils.isEmptyStr(reportNo) && StringUtils.isEmptyStr(policyNo)){
            return ResponseResult.fail(GlobalResultStatus.FAIL.getCode(),"查询失败，保单号、报案号不能同时为空");
        }
        List<String> reportNos= new ArrayList<>();
        if (StringUtils.isEmptyStr(reportNo)) {
            reportNos.addAll(ahcsPolicyInfoMapper.getReportNoByPolicyNo(policyNo));
        } else {
            reportNos.add(reportNo);
        }
        List<ReportInfoForSX> reportInfoS = new ArrayList<>();
        for (String no : reportNos) {
            reportInfoS.addAll(reportInfoService.getReportNoInfoForSX(no));
        }
        if (!CollectionUtils.isEmpty(reportInfoS)){
            reportInfoS = reportInfoS.stream().sorted(Comparator.comparing(ReportInfoForSX::getReportDate).reversed()).collect(Collectors.toList());
        }
        return ResponseResult.success(reportInfoS);
    }

    /**
     * 退运险批量结案
     * @return
     */
    @PostMapping(value = "/batchCloseCase",produces = {"application/json;charset=utf-8"})
    public ResponseResult<Object> batchCloseCase(@RequestBody @Valid @NotNull(message ="入参不能为空") AutoCloseRequestVO autoCloseRequestVO) {
        LogUtil.info("退运险批量结案入口-start入参={}", JsonUtils.toJsonString(autoCloseRequestVO));
        try {
            OnlineBatchResponse onlineBatchResponse = batchAutoCloseService.saveOnlineBatchAutoClose(autoCloseRequestVO);
            LogUtil.info("退运险批量结案-end返参={}", JsonUtils.toJsonString(onlineBatchResponse));
            if("该批次号正在处理中".equals(onlineBatchResponse.getMsg())){
                ResponseResult<Object> responseResult = new ResponseResult<>();
                responseResult.setCode(GlobalResultStatus.WAIT.getCode());
                responseResult.setMsg(GlobalResultStatus.WAIT.getMsg());
                responseResult.setData(onlineBatchResponse);
                return responseResult;
            }
            return ResponseResult.success(onlineBatchResponse);
        } catch (GlobalBusinessException e) {
            LogUtil.info("退运险批量结案={}", e);
            return ResponseResult.fail(GlobalResultStatus.FAIL.getCode(),"退运险批量结案" + e.getMessage());
        }catch (Exception exception) {
            LogUtil.error("退运险批量结案={}", exception);
            return ResponseResult.fail(GlobalResultStatus.FAIL.getCode(),"退运险批量结案" + exception.getMessage());
        }
    }

    /**
     * 美团一步结案
     * @return
     */
    @PostMapping(value = "/oneTimeCloseCase",produces = {"application/json;charset=utf-8"})
    public ResponseResult<Object> oneTimeCloseCase(@RequestBody @Valid @NotNull(message ="入参不能为空") OneTimeCloseCaseRequest oneTimeCloseRequestVO) {
        LogUtil.info("一步结案入口-start入参={}", JsonUtils.toJsonString(oneTimeCloseRequestVO));
        AutoCloseRequestVO autoCloseRequestVO = new AutoCloseRequestVO();
        autoCloseRequestVO.setThirdBatchNo(oneTimeCloseRequestVO.getThirdRequestNo());
        autoCloseRequestVO.setReopenNum(oneTimeCloseRequestVO.getReopenNum());
        List<OnlineBatchAutoClose> batchCloseList = new ArrayList<>();
        OnlineBatchAutoClose autoClose = new OnlineBatchAutoClose();
        BeanUtils.copyProperties(oneTimeCloseRequestVO,autoClose);
        batchCloseList.add(autoClose);
        autoCloseRequestVO.setBatchCloseList(batchCloseList);
        try {
            OneTimeCloseCaseResponse oneTimeCloseCaseResponse = batchAutoCloseService.saveOnlineOneTimeCloseCase(autoCloseRequestVO);
            LogUtil.info("一步结案结案-end返参={}", JsonUtils.toJsonString(oneTimeCloseCaseResponse));
            return ResponseResult.success(oneTimeCloseCaseResponse);
        } catch (GlobalBusinessException e) {
            LogUtil.info("一步结案结案={}", e);
            return ResponseResult.fail(GlobalResultStatus.FAIL.getCode(),"一步结案结案" + e.getMessage());
        }catch (Exception exception) {
            LogUtil.error("一步结案结案={}", exception);
            return ResponseResult.fail(GlobalResultStatus.FAIL.getCode(),"一步结案结案" + exception.getMessage());
        }
    }

    /**
     * 美团一步零结
     * @return
     */
    @PostMapping(value = "/oneTimeZeroCancel",produces = {"application/json;charset=utf-8"})
    public ResponseResult<Object> oneTimeZeroCancel(@RequestBody @Valid @NotNull(message ="入参不能为空") OneTimeCloseRequestVO oneTimeCloseRequestVO) {
        LogUtil.info("一步零结入口-start入参={}", JsonUtils.toJsonString(oneTimeCloseRequestVO));
        AutoCloseRequestVO autoCloseRequestVO = new AutoCloseRequestVO();
        autoCloseRequestVO.setThirdBatchNo(oneTimeCloseRequestVO.getThirdRequestNo());
        autoCloseRequestVO.setReopenNum(oneTimeCloseRequestVO.getReopenNum());
        List<OnlineBatchAutoClose> batchCloseList = new ArrayList<>();
        OnlineBatchAutoClose autoClose = new OnlineBatchAutoClose();
        BeanUtils.copyProperties(oneTimeCloseRequestVO,autoClose);
        batchCloseList.add(autoClose);
        autoCloseRequestVO.setBatchCloseList(batchCloseList);
        try {
            OneTimeCloseCaseResponse oneTimeCloseCaseResponse = batchAutoCloseService.saveOnlineOneTimeZeroCancel(autoCloseRequestVO);
            LogUtil.info("一步零结结案-end返参={}", JsonUtils.toJsonString(oneTimeCloseCaseResponse));
            return ResponseResult.success(oneTimeCloseCaseResponse);
        } catch (GlobalBusinessException e) {
            LogUtil.info("一步零结案={}", e);
            return ResponseResult.fail(GlobalResultStatus.FAIL.getCode(),"一步零结结案" + e.getMessage());
        }catch (Exception exception) {
            LogUtil.error("一步零结结案={}", exception);
            return ResponseResult.fail(GlobalResultStatus.FAIL.getCode(),"一步零结结案" + exception.getMessage());
        }
    }

    /**
     * 退运险批量结案补偿
     * @return
     */
    @GetMapping(value = "/batchCompensation",produces = {"application/json;charset=utf-8"})
    public void batchCompensation(@RequestParam(value ="reportNo" ,required = false) String reportNo,
                                  @RequestParam(value ="thirdBatchNo" ,required = false) String thirdBatchNo) {
        LogUtil.info("退运险批量结案补偿入参reportNo={},thirdBatchNo={}", reportNo, thirdBatchNo);
        if(StringUtils.isEmptyStr(reportNo) && StringUtils.isEmptyStr(thirdBatchNo)){
            return;
        }
        try {
            batchAutoCloseCompensationService.batchCompensation(reportNo, thirdBatchNo);
            LogUtil.info("退运险批量结案补偿-end");
        } catch (GlobalBusinessException e) {
            LogUtil.info("退运险批量结案补偿={}", e);
        }catch (Exception exception) {
            LogUtil.error("退运险批量结案补偿={}", exception);
        }
    }
}