package com.paic.ncbs.claim.model.dto.sop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * SOP配置DTO
 *
 * <AUTHOR>
 * @since 2025-09-10
 */
@Data
@ApiModel("SOP配置信息")
public class SopConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("配置id")
    private String idSopConfig;

    @ApiModelProperty("SOP主键ID")
    private String idSopMain;

    @ApiModelProperty("产品代码")
    private String productCode;

    @ApiModelProperty("方案代码")
    private String groupCode;

    @ApiModelProperty("险种代码")
    private String planCode;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("方案名称")
    private String groupName;

    @ApiModelProperty("险种名称")
    private String planName;

}
