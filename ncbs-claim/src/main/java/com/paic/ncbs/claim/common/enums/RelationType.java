package com.paic.ncbs.claim.common.enums;

import cn.hutool.core.util.StrUtil;

public enum RelationType {
	SELF("00", "本人","1"),
	SPOUSE("01", "配偶","3"),
	PARENTS("02", "父母","2"),
	CHILDREN("03", "子女","4"),
	BROTHER_AND_SISTER("05", "兄弟姐妹","10"),
	EMPLOYER("06", "雇主","7"),
	EMPLOYEE("07", "雇员","13"),
	GRANDPARENT("08", "祖父母、外祖父母","14"),
	GRANDCHILD("09", "祖孙、外祖孙","15"),
	GUARDIAN("10", "监护人","6"),
	PUPIL("11", "被监护人","11"),
	FRIEND("12", "朋友","12"),
	THIRTEEN("13", "批量报案","9"),
	UNKNOWN("98", "未知","9"),
	OTHER("99", "其他","9");

	private final String type;

	private final String name;

	private final String code;
	RelationType(String type, String name,String code) {
		this.type = type;
		this.name = name;
		this.code = code;
	}

	public String getType() {
		return type;
	}


	public String getName() {
		return name;
	}

	public String getCode(){return code;}

	public static String getName(String type) {
		if(StrUtil.isEmpty(type)){
			return null;
		}
		for (RelationType value : RelationType.values()) {
			if (type.equals(value.getType())) {
				return value.getName();
			}
		}
		return null;
	}
	public static String getCode(String type) {
		if(StrUtil.isEmpty(type)){
			return null;
		}
		for (RelationType value : RelationType.values()) {
			if (type.equals(value.getType())) {
				return value.getCode();
			}
		}
		return null;
	}
}
