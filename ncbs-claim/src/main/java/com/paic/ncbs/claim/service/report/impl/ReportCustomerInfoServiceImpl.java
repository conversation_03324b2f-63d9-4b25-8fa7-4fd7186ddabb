package com.paic.ncbs.claim.service.report.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.paic.ncbs.base.util.MeshSendUtils;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.ConfigConstValues;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.ProductClassEnum;
import com.paic.ncbs.claim.common.page.Pager;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.RapeCheckUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsInsuredPresonEntity;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsInsuredPresonMapper;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimateChangeMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportAccidentMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportCustomerInfoMapper;
import com.paic.ncbs.claim.dao.mapper.riskppt.RiskPropertyMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.feign.OcasFeign;
import com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO;
import com.paic.ncbs.claim.model.dto.estimate.ClmsEstimateRecord;
import com.paic.ncbs.claim.model.dto.ocas.OcasInsuredDTO;
import com.paic.ncbs.claim.model.dto.realname.OcasRealNameDTO;
import com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO;
import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;
import com.paic.ncbs.claim.model.vo.report.HistoryCaseVO;
import com.paic.ncbs.claim.model.vo.report.OcasRealNameVo;
import com.paic.ncbs.claim.model.vo.report.ReportCustomerInfoVO;
import com.paic.ncbs.claim.model.vo.taskdeal.*;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.estimate.ClmsEstimateRecordService;
import com.paic.ncbs.claim.service.report.ReportCustomerInfoService;
import com.paic.ncbs.claim.service.report.ReportInfoService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.*;


@Service
@Slf4j
public class ReportCustomerInfoServiceImpl extends BaseServiceImpl<ReportCustomerInfoEntity>
        implements ReportCustomerInfoService {

    @Autowired
    private ReportCustomerInfoMapper reportCustomerInfoMapper;

    @Autowired
    private CaseProcessService caseProcessService;

    @Autowired
    private ClmsEstimateRecordService clmsEstimateRecordService;

    @Autowired
    private PolicyPayService policyPayService;

    @Autowired
    private ReportInfoService reportInfoService;
    @Autowired
    private OcasMapper ocasMapper;
    @Autowired
    private ReportAccidentMapper reportAccidentMapper;
    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;
    @Autowired
    private AhcsInsuredPresonMapper ahcsInsuredPresonMapper;
    @Autowired
    private OcasFeign ocasFeign;
    @Autowired
    private RiskPropertyMapper riskPropertyMapper;

    @Value("${switch.mesh}")
    private Boolean switchMesh;
    @Autowired
    protected RestTemplate restTemplate;
    @Value("${es.claimInfoUrl:http://ncbs-policy-query.lb.ssdev.com:48047/query-policy/es/queryClaimInfoByParam}")
    private String esUrl;

    /**
     * 人身险
     */
    public static final String PRODUCT_TYPE_PERSON = "1";

    /**
     * 非人身险
     */
    public static final String PRODUCT_TYPE_NOT_PERSON = "0";

    @Autowired
    private EstimateChangeMapper estimateChangeMapper;


    @Override
    public BaseDao<ReportCustomerInfoEntity> getDao() {
        return reportCustomerInfoMapper;
    }

    @Override
    public ReportCustomerInfoEntity getReportCustomerInfoByReportNo(String reportNo) {
        return reportCustomerInfoMapper.getReportCustomerInfoByReportNo(reportNo);
    }

    @Override
    public ResponseResult<Map<String, Object>> getHistoryByPolicyNo(String policyNo ,Pager pager,String certificateNo,String insuredName) {
        List<ReportCustomerInfoEntity> customerInfo = new ArrayList<>();
        ReportCustomerInfoEntity  reportCustomerInfoEntity = new  ReportCustomerInfoEntity();
        reportCustomerInfoEntity.setCertificateNo(certificateNo);
        reportCustomerInfoEntity.setName(insuredName);
        customerInfo.add(reportCustomerInfoEntity) ;
        if (!CollectionUtils.isEmpty(customerInfo)){
           return    getMapResponseResult(customerInfo,pager,policyNo);
        }
        return ResponseResult.success(new ArrayList<HistoryCaseVO>(), pager);
    }



    private ResponseResult<Map<String, Object>> getMapResponseResult(List<ReportCustomerInfoEntity> customerInfo, Pager pager, String policyNo) {
        PageHelper.startPage(pager.getPageIndex(), pager.getPageRows(), true);
        PageHelper.orderBy(" reportDate desc");
        List<HistoryCaseDTO> list = reportInfoService.getHistoryByPolicyNos(customerInfo,policyNo);
        List<HistoryCaseVO> historyCaseList = transformVO(list);
        for (HistoryCaseVO historyCaseVO : historyCaseList) {
            String reportNo = historyCaseVO.getReportNo();
            Short caseTimes = historyCaseVO.getCaseTimes();
            //赔款
            BigDecimal sumPayFee = policyPayService.getSumPayFee(reportNo, Integer.valueOf(caseTimes));
            if (BigDecimal.ZERO.compareTo(sumPayFee) == 0) {
                historyCaseVO.setPolicySumPay(null);
            } else {
                historyCaseVO.setPolicySumPay(sumPayFee);
            }
            //流程状态c
            String processStatusName = caseProcessService.getCaseProcessStatusName(reportNo, Integer.valueOf(caseTimes));
            historyCaseVO.setProcessStatusName(processStatusName);
            //未决金额
            List<ClmsEstimateRecord> records = clmsEstimateRecordService.getRecordByReportNoAndType(reportNo, String.valueOf(caseTimes), null);
            if (!"已结案".equals(processStatusName) && !CollectionUtils.isEmpty(records)) {
                BigDecimal estimateAmt = records.get(0).getEstimateAmount();
                if(estimateAmt == null){
                    estimateAmt = new BigDecimal("0");
                }
                historyCaseVO.setPendingAmount(estimateAmt.toString());
                //没结案不展示赔款金额
                historyCaseVO.setPolicySumPay(null);
            } else {
                historyCaseVO.setPendingAmount(null);
            }
        }
        PageInfo<HistoryCaseDTO> pageInfo = new PageInfo<>(list);
        pager.setTotalRows((int) pageInfo.getTotal());
        LogUtil.info("根据客户号查询历史案件列表出参historyCaseList:{}", JSON.toJSONString(historyCaseList));
        PageMethod.clearPage();
        return ResponseResult.success(historyCaseList, pager);
    }

    private List<HistoryCaseVO> transformVO(List<HistoryCaseDTO> historyCaseDTOs) {
        List<HistoryCaseVO> historyCaseList = new ArrayList<>();
        if (RapeCheckUtil.isListNotEmpty(historyCaseDTOs)) {
            for (HistoryCaseDTO dto : historyCaseDTOs) {
                HistoryCaseVO historyCaseVO = new HistoryCaseVO();
                BeanUtils.copyProperties(dto, historyCaseVO);
                CaseProcessDTO caseProcess = new CaseProcessDTO();
                caseProcess.setReportNo(dto.getReportNo());
                caseProcess.setCaseTimes(Integer.valueOf(dto.getCaseTimes()));
                CaseProcessDTO caseProcessDTO = caseProcessService.getProcessStatusAndEndAmount(caseProcess);
                if(caseProcessDTO == null ){
                    continue;
                }
                historyCaseVO.setCaseStatusName(caseProcessDTO.getProcessStatusName());
                historyCaseVO.setEndCaseAmount(caseProcessDTO.getEndCaseAmount());
                historyCaseVO.setEstimateAmount(caseProcessDTO.getEstimateAmount());
                historyCaseVO.setEndCaseDate(caseProcessDTO.getEndCaseDate());
                historyCaseVO.setDepartmentCode(caseProcessDTO.getDepartmentCode());
                historyCaseVO.setDepartmentAbbrName(caseProcessDTO.getDepartmentAbbrName());
                historyCaseList.add(historyCaseVO);
            }
        }
        return historyCaseList;
    }

    @Override
    @Transactional
    public void updateCustomerById(ReportCustomerInfoVO reportCustomerInfoVO) throws InterruptedException {
        OcasInsuredDTO insuredDTO = ocasMapper.getInsuredBaseInfo(reportCustomerInfoVO.getIdPlyRiskPerson());
        if(null == insuredDTO){
            return;
        }
        ReportCustomerInfoEntity customerInfoEntity = reportCustomerInfoMapper.selectByPrimaryKey(reportCustomerInfoVO.getIdAhcsReportCustomer());
        if(null == customerInfoEntity){
            return;
        }
        ReportAccidentEntity reportAccident = reportAccidentMapper.getReportAccident(customerInfoEntity.getReportNo());
        String accidentDetail = reportAccident.getAccidentDetail();
        if (StringUtils.isNotEmpty(accidentDetail)){
            reportAccident.setAccidentDetail(accidentDetail.replace(customerInfoEntity.getName(),insuredDTO.getInsuredName()));
            reportAccidentMapper.updateByPrimaryKeySelective(reportAccident);
        }
        // 更新被保险人信息
        List<AhcsPolicyInfoEntity> policyInfoEntities = ahcsPolicyInfoMapper.selectByReportNo(customerInfoEntity.getReportNo());
        for (AhcsPolicyInfoEntity policyInfoEntity : policyInfoEntities) {
            List<AhcsInsuredPresonEntity> insuredPresonEntities = ahcsInsuredPresonMapper.getAhcsInsuredPersionById(policyInfoEntity.getIdAhcsPolicyInfo());
            for (AhcsInsuredPresonEntity insuredPresonEntity : insuredPresonEntities) {
                insuredPresonEntity.setName(insuredDTO.getInsuredName());
                insuredPresonEntity.setCertificateType(insuredDTO.getCertificateType());
                insuredPresonEntity.setCertificateNo(insuredDTO.getCertificateNo());
                if(StringUtils.isNotEmpty(insuredDTO.getAge())){
                    insuredPresonEntity.setAge(Integer.parseInt(insuredDTO.getAge()));
                }
                insuredPresonEntity.setClientNo(insuredDTO.getClientNo());
                insuredPresonEntity.setSexCode(insuredDTO.getSexCode());
                insuredPresonEntity.setBirthday(insuredDTO.getBirthday());
                insuredPresonEntity.setMobileTelephone(insuredDTO.getMobileTelephone());
                insuredPresonEntity.setIsSociaSecurity(insuredDTO.getIsSociaSecurity());
                insuredPresonEntity.setPersonnelNature(insuredDTO.getPersonnelNature());
                insuredPresonEntity.setUpdatedDate(new Date());
                insuredPresonEntity.setUpdatedBy(WebServletContext.getUserId());
                ahcsInsuredPresonMapper.updateByPrimaryKey(insuredPresonEntity);
            }
        }

        ReportCustomerInfoEntity entity = new ReportCustomerInfoEntity();
        entity.setIdAhcsReportCustomer(reportCustomerInfoVO.getIdAhcsReportCustomer());
        entity.setName(insuredDTO.getInsuredName());
        entity.setCertificateNo(insuredDTO.getCertificateNo());
        entity.setCertificateType(insuredDTO.getCertificateType());
        if(StringUtils.isNotEmpty(insuredDTO.getAge())){
            entity.setAge(Integer.parseInt(insuredDTO.getAge()));
        }
        entity.setClientNo(insuredDTO.getClientNo());
        entity.setSexCode(insuredDTO.getSexCode());
        entity.setBirthday(insuredDTO.getBirthday());
        entity.setClientCluster(insuredDTO.getPersonnelAttribute());
        entity.setUpdatedDate(new Date());
        entity.setUpdatedBy(WebServletContext.getUserId());
        updateByPrimaryKeySelective(entity);
    }

    @Override
    public void autoRealName(OcasRealNameVo ocasRealNameVo) {
        log.info("自动实名接口调用，ocasRealNameVo:{}", JsonUtils.toJsonString(ocasRealNameVo));
//        ValidationUtil.validate(ocasRealNameVo);
        List<AhcsPolicyInfoEntity> policyInfoEntities = ahcsPolicyInfoMapper.selectByReportNo(ocasRealNameVo.getReportNo());
        if(CollectionUtils.isEmpty(policyInfoEntities)){
            log.error("自动实名化接口报案号查询保单数据为空，reportNo:{}", ocasRealNameVo.getReportNo());
            throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("保单数据查询异常"));
        }
        AhcsPolicyInfoEntity ahcsPolicyInfoEntity = policyInfoEntities.get(0);
        OcasRealNameDTO ocasRealNameDTO = new OcasRealNameDTO();
        ocasRealNameDTO.setPolicyNo(ahcsPolicyInfoEntity.getPolicyNo());
        String riskType = ocasMapper.getTargetType(ahcsPolicyInfoEntity.getPolicyNo());

        List<AhcsInsuredPresonEntity> insuredPresonEntities = ahcsInsuredPresonMapper.getAhcsInsuredPersionById(ahcsPolicyInfoEntity.getIdAhcsPolicyInfo());
        if(!CollectionUtils.isEmpty(insuredPresonEntities) && !"1000".equals(riskType)){
            ocasRealNameDTO.setRiskGroupName(insuredPresonEntities.get(0).getSchemeName());
        }
        ocasRealNameDTO.setName(ocasRealNameVo.getInsuredName());
        ocasRealNameDTO.setCertificateType(ocasRealNameVo.getCertificateType());
        ocasRealNameDTO.setCertificateNo(ocasRealNameVo.getCertificateNo());
        ocasRealNameDTO.setIdPlyRiskPerson(ocasRealNameVo.getIdPlyRiskPerson());
        log.info("调用批改自动实名接口，参数：{}", JsonUtils.toJsonString(ocasRealNameDTO));
        String result = ocasFeign.autoRealName(ocasRealNameDTO);
        log.info("调用批改自动实名接口，结果：{}", result);
        JSONObject jsonObject = JSONObject.parseObject(result);
        String resultCode = jsonObject.getString("resultCode");
        String resultMessage = jsonObject.getString("resultMessage");
        if(!"000000".equals(resultCode)){
            log.error("调用批改自动实名接口异常,reportNo:{}", ocasRealNameVo.getReportNo());
            throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("调用批改自动实名接口异常:" + resultMessage));
        }
    }

    @Override
    public List<ClaimInfoToESVO> getHistoryCaseListNew(String reportNo, Integer caseTimes, Pager pager) {
        List<ClaimInfoToESVO> claimInfoToESVOs = Lists.newArrayList();
        List<String> policyNoByReportNos = ahcsPolicyInfoMapper.getPolicyNoByReportNo(reportNo);
        String policyNo = null;
        if(ListUtils.isNotEmpty(policyNoByReportNos)) {
            policyNo = policyNoByReportNos.get(0);
        }
        if(StringUtils.isEmptyStr(policyNo)) {
            return claimInfoToESVOs;
        }
        Map<String, String> productMap = ocasMapper.getPlyBaseInfo(policyNo);
        String productClass = MapUtils.getString(productMap,"productClass");
        /**
         * 意外险健康险：
         * 	真实被保险人：按照标的人来  objectCertNo：1233
         * 	虚拟被保险人：按照保单来 policyNo：3456
         *
         * 雇主责任险：
         * 	勾选标的人：按照标的人来 objectCertNo：1233
         * 	未勾选：按照保单来 policyNo：3456
         *
         * 其它：
         * 	按照保单来。
         */
        String isSearchByPolicy = "0";
        String objectCertNo = null;
        if(ProductClassEnum.PRODUCT_CLASS_02.getType().equals(productClass) ||ProductClassEnum.PRODUCT_CLASS_03.getType().equals(productClass)) {
//            List<AhcsPolicyInfoEntity> ahcsPolicyInfoEntities = ahcsPolicyInfoMapper.selectByReportNo(reportNo);
//            if(ListUtils.isNotEmpty(ahcsPolicyInfoEntities) && "1".equals(ahcsPolicyInfoEntities.get(0).getIsFamily())) {
//                isSearchByPolicy = "2";
//            }else {
                // 意外险健康险 真实被保险人
                ReportCustomerInfoEntity reportCustomerInfoEntity = reportCustomerInfoMapper.getReportCustomerInfoByReportNo(reportNo);
                if (reportCustomerInfoEntity != null && (!"200".equals(reportCustomerInfoEntity.getClientCluster()) && !"020".equals(reportCustomerInfoEntity.getClientCluster()))) {
                    isSearchByPolicy = "1";
                    objectCertNo = reportCustomerInfoEntity.getCertificateNo();
                }
//            }
        }else {
            // 如果勾选了标的人的方案是雇主责任险 按照标的人查询
            CaseRiskPropertyDTO caseDTO = new CaseRiskPropertyDTO();
            caseDTO.setReportNo(reportNo);
            caseDTO.setCaseTimes(caseTimes);
            List<CaseRiskPropertyDTO> caseRiskList = riskPropertyMapper.getLastTaskIdCaseRiskPropertyList(caseDTO);
            if(ListUtils.isNotEmpty(caseRiskList)) {
                for(CaseRiskPropertyDTO caseRiskPropertyDTO:caseRiskList) {
                    if(BaseConstant.TARGET_TYPE_EMPLOYER.equals(caseRiskPropertyDTO.getRiskGroupType())) {
                        isSearchByPolicy = "1";
                        objectCertNo = caseRiskPropertyDTO.getCertificateNo();
                        break;
                    }
                }
            }
        }

        switch (isSearchByPolicy) {
            case "0":
                claimInfoToESVOs = searchByPolicyNo(policyNo, pager);
                break;
            case "1":
                claimInfoToESVOs = searchByObjectCertNo(objectCertNo, pager);
                break;
            case "2":
                claimInfoToESVOs = searchByObjectOr(objectCertNo, policyNo, pager);
                break;
        }

        // 特殊处理前端显示
        processSpecial(claimInfoToESVOs);
        return claimInfoToESVOs;
    }

    /**
     * 特殊处理前端显示
     * @param claimInfoToESVOS
     */
    private void processSpecial(List<ClaimInfoToESVO> claimInfoToESVOS) {
        if(claimInfoToESVOS==null || claimInfoToESVOS.size()==0) {
            return ;
        }
        for(ClaimInfoToESVO claimInfoToESVO:claimInfoToESVOS) {
            String reportNo = claimInfoToESVO.getReportNo();
            Integer caseTimes = claimInfoToESVO.getCaseTimes();
            String policyNo = claimInfoToESVO.getPolicyNo();
            // 人身险标识 事故经过
            if("GLOBAL".equals(claimInfoToESVO.getDataSource())) {
                claimInfoToESVO.setProductType(PRODUCT_TYPE_PERSON);
            }else {
                boolean isProductPerson = this.isProductPerson(policyNo,reportNo,caseTimes);
                if(isProductPerson) {
                    claimInfoToESVO.setProductType(PRODUCT_TYPE_PERSON);
                }else {
                    claimInfoToESVO.setProductType(PRODUCT_TYPE_NOT_PERSON);
                    // 事故经过
                    ReportAccidentEntity reportAccidentEntity = this.reportAccidentMapper.getReportAccident(reportNo);
                    if(reportAccidentEntity!=null) {
                        claimInfoToESVO.setAccidentDetail(reportAccidentEntity.getAccidentDetail());
                    }
                }
            }
            // 未决金额处理
            if("NCBS".equals(claimInfoToESVO.getDataSource())) {
                // 不为已结案
                if(!ConfigConstValues.WHOLE_CASE_STATUS_END.equals(claimInfoToESVO.getCaseStatus())) {
                    //未决金额
                    List<ClmsEstimateRecord> records = clmsEstimateRecordService.getRecordByReportNoAndType(reportNo, String.valueOf(caseTimes), null);
                    if(ListUtils.isNotEmpty(records)){
                        BigDecimal estimateAmount = records.get(0).getEstimateAmount();
                        String estimateType = records.get(0).getEstimateType();
                        if("01".equals(estimateType) && estimateAmount.compareTo(BigDecimal.ZERO) == 0) {
                            claimInfoToESVO.setEstimateAmount(null);
                        }else {
                            claimInfoToESVO.setEstimateAmount(String.valueOf(estimateAmount));
                        }
                    }
                    BigDecimal changeAmt = estimateChangeMapper.getEstimateChangeAmount(reportNo,caseTimes);
                    if(changeAmt != null){
                        //有修正金额，替换为修正金额
                        claimInfoToESVO.setEstimateAmount(changeAmt.toString());
                    }
                }
            }
        }
    }

    /**
     * 判断是否人身险产品
     * @param policyNo
     * @param reportNo
     * @param caseTimes
     * @return
     */
    private boolean isProductPerson(String policyNo,String reportNo,Integer caseTimes) {
        boolean isProductPerson = false;
        Map<String, String> productMap = ocasMapper.getPlyBaseInfo(policyNo);
        String productClass = MapUtils.getString(productMap,"productClass");
        if(ProductClassEnum.PRODUCT_CLASS_02.getType().equals(productClass) ||ProductClassEnum.PRODUCT_CLASS_03.getType().equals(productClass)) {
            // 意外险健康险
            isProductPerson = true;
        }else {
            // 如果勾选了标的人的方案是雇主责任险 按照标的人查询
            CaseRiskPropertyDTO caseDTO = new CaseRiskPropertyDTO();
            caseDTO.setReportNo(reportNo);
            caseDTO.setCaseTimes(caseTimes);
            List<CaseRiskPropertyDTO> caseRiskList = riskPropertyMapper.getLastTaskIdCaseRiskPropertyList(caseDTO);
            if(ListUtils.isNotEmpty(caseRiskList)) {
                for(CaseRiskPropertyDTO caseRiskPropertyDTO:caseRiskList) {
                    if(BaseConstant.TARGET_TYPE_EMPLOYER.equals(caseRiskPropertyDTO.getRiskGroupType())) {
                        isProductPerson = true;
                        break;
                    }
                }
            }
        }
        return isProductPerson;
    }

    private List<ClaimInfoToESVO> searchByPolicyNo(String policyNo, Pager pager) {
        ClaimESInfoQueryVO claimESInfoQueryVO = new ClaimESInfoQueryVO();
        claimESInfoQueryVO.setPolicyNo(policyNo);
        claimESInfoQueryVO.setRows(pager.getPageRows());
        claimESInfoQueryVO.setPage(pager.getPageIndex());
        return claimInfoRequestES(claimESInfoQueryVO, pager);
    }

    private List<ClaimInfoToESVO> searchByObjectCertNo(String objectCertNo, Pager pager) {
        ClaimESInfoQueryVO claimESInfoQueryVO = new ClaimESInfoQueryVO();
        claimESInfoQueryVO.setObjectCertNo(objectCertNo);
        claimESInfoQueryVO.setRows(pager.getPageRows());
        claimESInfoQueryVO.setPage(pager.getPageIndex());
        return claimInfoRequestES(claimESInfoQueryVO, pager);
    }

    private List<ClaimInfoToESVO> searchByObjectOr(String objectCertNo, String policyNo, Pager pager) {
        ClaimESInfoQueryVO claimESInfoQueryVO = new ClaimESInfoQueryVO();
        claimESInfoQueryVO.setObjectCertNo(objectCertNo);
        claimESInfoQueryVO.setPolicyNo(policyNo);
        claimESInfoQueryVO.setRows(pager.getPageRows());
        claimESInfoQueryVO.setPage(pager.getPageIndex());
        return claimInfoRequestES(claimESInfoQueryVO, pager);
    }


    private List<ClaimInfoToESVO> claimInfoRequestES(ClaimESInfoQueryVO claimESInfoQueryVO, Pager pager) {
        List<ClaimInfoToESVO> claimESInfoVOList = new ArrayList<>();
        ClaimESInfoResultVO claimESInfoResultVO = this.requestES(claimESInfoQueryVO);
        if (null != claimESInfoResultVO) {
            String responseCode = claimESInfoResultVO.getResponseCode();
            if (StringUtils.isNotEmpty(responseCode) && "000000".equals(responseCode)) {
                ClaimESInfoResultDataVO claimESInfoResultDataVO = claimESInfoResultVO.getData();
                if (null != claimESInfoResultDataVO) {
                    claimESInfoVOList = claimESInfoResultDataVO.getList();
                    pager.setTotalRows(claimESInfoResultDataVO.getTotal());
                }
            } else {
                throw new GlobalBusinessException("理赔调用ES查询理赔信息异常!");
            }
        } else {
            throw new GlobalBusinessException("理赔调用ES查询理赔信息异常!");
        }
        return claimESInfoVOList;
    }


    private ClaimESInfoResultVO requestES(ClaimESInfoQueryVO claimESInfoQueryVO) {
        log.info("理赔调用被保人查询ES, url: {}, params: {}", esUrl, JSON.toJSONString(claimESInfoQueryVO));
        ClaimESInfoResultVO claimESInfoResultVO = new ClaimESInfoResultVO();
        if (switchMesh){
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json;charset:utf-8");
            String result = MeshSendUtils.post(esUrl, JSON.toJSONString(claimESInfoQueryVO),headers);
            if (StringUtils.isNotEmpty(result)) {
                claimESInfoResultVO = JSON.parseObject(result, ClaimESInfoResultVO.class);
            } else {
                throw new GlobalBusinessException("理赔调用ES被保人查询异常!");
            }
        }else {
            HttpHeaders header = new HttpHeaders();
            header.add("Content-Type", "application/json;charset:utf-8");
            HttpEntity<ClaimESInfoQueryVO> httpEntity = new HttpEntity<>(claimESInfoQueryVO, header);
            claimESInfoResultVO = restTemplate.postForObject(esUrl, httpEntity, ClaimESInfoResultVO.class);
        }
        log.info("理赔调用被保人查询ES, result: {}", JSON.toJSONString(claimESInfoResultVO));
        return claimESInfoResultVO;
    }

}
