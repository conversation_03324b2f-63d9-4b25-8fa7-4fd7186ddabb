package com.paic.ncbs.claim.service.investigate.impl;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.investigate.InvestigateConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.enums.SyncCaseStatusEnum;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateMapper;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateTaskAuditMapper;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateTaskMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoExMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.feign.mesh.TpaGlobalRequest;
import com.paic.ncbs.claim.feign.TPAFeign;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateScoreDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskAuditDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskDTO;
import com.paic.ncbs.claim.model.dto.mq.syncstatus.SyncCaseStatusDto;
import com.paic.ncbs.claim.model.dto.notice.NoticesDTO;
import com.paic.ncbs.claim.model.dto.problem.ProblemCaseRequestDTO;
import com.paic.ncbs.claim.model.dto.problem.ProblemCaseResponseDTO;
import com.paic.ncbs.claim.model.dto.problem.RequestData;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateAuditVO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateTaskAuditVO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateTaskVO;
import com.paic.ncbs.claim.model.vo.investigate.TpaInvestigateVO;
import com.paic.ncbs.claim.mq.producer.MqProducerSyncCaseStatusService;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.investigate.InvestigateScoreService;
import com.paic.ncbs.claim.service.investigate.InvestigateService;
import com.paic.ncbs.claim.service.investigate.InvestigateTaskAuditService;
import com.paic.ncbs.claim.service.investigate.InvestigateTaskService;
import com.paic.ncbs.claim.service.notice.NoticeService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import com.paic.ncbs.claim.utils.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service("investigateTaskAuditService")
public class InvestigateTaskAuditServiceImpl implements InvestigateTaskAuditService {

	@Autowired
    BpmService bpmService;

    @Autowired
    private InvestigateTaskAuditMapper investigateTaskAuditDao;

    @Autowired
    private InvestigateTaskMapper investigateTaskDao;

    @Autowired
    private InvestigateMapper investigateDao;

	@Autowired
	InvestigateService investigateService;

	@Autowired
	private InvestigateScoreService investigateScoreService;

	@Autowired
	private InvestigateTaskService investigateTaskService;
	@Autowired
	private TaskInfoMapper taskInfoMapper;

	@Autowired
	private UserInfoService userInfoService ;

	@Autowired
	private TaskInfoService taskInfoService ;
	@Autowired
	private CaseProcessService caseProcessService ;
	@Autowired
	private MqProducerSyncCaseStatusService mqProducerSyncCaseStatusService;
	@Autowired
	private ReportInfoExMapper reportInfoExMapper;
	@Autowired
	private TPAFeign tpaFeign;

	@Autowired
	private TpaGlobalRequest tpaGlobalRequest;

	@Autowired
	private IOperationRecordService operationRecordService;
	@Autowired
	private NoticeService noticeService;

	@Override
	@Transactional
	public void finishTaskAudit(InvestigateTaskAuditDTO investigateTaskAuditDTO, String userId) throws GlobalBusinessException {
		String feeAuditOption = investigateTaskAuditDTO.getFeeAuditOption();
		LogUtil.audit("公估费审核是否通过:"+feeAuditOption);
		if(StringUtils.isEmptyStr(investigateTaskAuditDTO.getIdAhcsInvestigateTaskAudit())){
			String auditId = investigateTaskAuditDao.getTaskAuditIdByTaskId(investigateTaskAuditDTO.getIdAhcsInvestigateTask());
			investigateTaskAuditDTO.setIdAhcsInvestigateTaskAudit(auditId);
		}

		investigateTaskAuditDTO.setInitiatorUm(userId);
		// 调度处理会更改列表的处理人，实际表中处理人并未更新
		investigateTaskAuditDTO.setReviewUserUm(userId);
		int result = investigateTaskAuditDao.modifyTaskAudit(investigateTaskAuditDTO);
		LogUtil.audit("更新任务审核记录表结果:"+result);
		String taskId = investigateTaskAuditDao.getTaskAuditIdByTaskId2(investigateTaskAuditDTO.getIdAhcsInvestigateTaskAudit());
		LogUtil.audit("调查审核id:"+taskId);
		investigateTaskAuditDTO.setIdAhcsInvestigateTask(taskId);
		InvestigateTaskDTO taskDTO = investigateTaskDao.getInvestigateTaskById(investigateTaskAuditDTO.getIdAhcsInvestigateTask());

		boolean isPass = false;
		String invesTaskId = investigateTaskAuditDTO.getIdAhcsInvestigateTask();
		if (InvestigateConstants.AHCS_INVESTIGATE_AUDIT_OPINION_PASS.equals(investigateTaskAuditDTO.getReviewOpinion())) {
			isPass = true;
			LogUtil.audit("#调查·完成任务审核#通过！");
			taskDTO.setTaskStatus(InvestigateConstants.AHCS_INVESTIGATE_TASK_STATUS_FINISH);
			taskDTO.setFinishDate(new Date());
			if (InvestigateConstants.VALIDATE_FLAG_YES.equals(taskDTO.getIsPrimaryTask())){
				List<InvestigateScoreDTO> investigateScoreDTOList = investigateTaskAuditDTO.getInvestigateScoreDTOList();

				if (ListUtils.isNotEmpty(investigateScoreDTOList)){
					for (InvestigateScoreDTO investigateScoreDTO : investigateScoreDTOList){
						investigateScoreDTO.setScoreValue(investigateScoreService.getInvestigateScore(investigateScoreDTO));
						investigateScoreDTO.setScoreTime(new Date());
						investigateScoreDTO.setScoreUm(userId);
						investigateScoreService.saveInvestigateScore(investigateScoreDTO);
					}
				}
			}
		} else {
			LogUtil.audit("#调查·完成任务审核#退回！");
			taskDTO.setTaskStatus(InvestigateConstants.AHCS_INVESTIGATE_TASK_STATUS_PROCESSING);
		}
		investigateTaskDao.modifyInvestigateTaskWiThoutUpdatetime(taskDTO);

		if (InvestigateConstants.VALIDATE_FLAG_YES.equals(taskDTO.getIsPrimaryTask())) {
			LogUtil.audit("是主调："+taskDTO.getIsPrimaryTask());
			InvestigateDTO investigateDTO = new InvestigateDTO();
			investigateDTO.setIdAhcsInvestigate(taskDTO.getIdAhcsInvestigate());
			if (isPass) {
				LogUtil.audit("已通过："+isPass);
				if (StringUtils.isEmptyStr(feeAuditOption)) {
					feeAuditOption = InvestigateConstants.VALIDATE_FLAG_NO;
				}
				LogUtil.audit("修改调查idAhcsInvestigate:{},feeAuditOption:{}",taskDTO.getIdAhcsInvestigate(), feeAuditOption);
				investigateService.modifyInvestigateForOption(taskDTO.getIdAhcsInvestigate(), feeAuditOption);

				investigateDTO.setInvestigateStatus(InvestigateConstants.AHCS_INVESTIGATE_STATUS_FINISH);
				investigateDTO.setInvestigateQualitative(taskDTO.getInvestigateQualitative());
			} else {
				LogUtil.audit("更新调查表状态为处理中");
				investigateDTO.setInvestigateStatus(InvestigateConstants.AHCS_INVESTIGATE_STATUS_PROCESSING);
			}
			investigateDao.modifyInvestigate(investigateDTO);
		}
		LogUtil.audit("开始同步调查审批结果给调查中台：调查类型："+investigateTaskAuditDTO.getSurveyType()+"任务类型："+taskDTO.getTaskType());
		//调查类型为“外部调查”并且任务类型是线上委托时，同步调查审批结果给调查中台

		if("02".equals(investigateTaskAuditDTO.getSurveyType()) &&"1".equals(taskDTO.getTaskType())){
			TpaInvestigateVO tpaInvestigateVO = new TpaInvestigateVO();
			if(isPass){
				tpaInvestigateVO.setReviewOpinion("1");
				tpaInvestigateVO.setDescription(investigateTaskAuditDTO.getDescription());

				if(investigateTaskAuditDTO.getInvestigateScoreDTOList().size()>0){
					tpaInvestigateVO.setInvestigateScore(tpaInvestigateVO.getTpaInvestigateScore(investigateTaskAuditDTO.getInvestigateScoreDTOList().get(0)));
				}
			}else{
				tpaInvestigateVO.setReviewOpinion("2");
				tpaInvestigateVO.setDescription(investigateTaskAuditDTO.getDescription());
				tpaInvestigateVO.setRejectReason(investigateTaskAuditDTO.getRejectReason());
			}
			LogUtil.audit("审核结果回调中台请求报文："+JsonUtils.toJsonString(tpaInvestigateVO));
//			String res = tpaGlobalRequest.fainshTask(tpaInvestigateVO);
//			LogUtil.audit("审核结果回调中台返回报文："+res);
//			Map<String, Object> resMap = JsonUtils.jsonToMap(res);
//			if(!"000000".equals(resMap.get("code"))){
//				throw new GlobalBusinessException(resMap.get("msg").toString());
//			}
		}
		if (InvestigateConstants.VALIDATE_FLAG_YES.equals(taskDTO.getIsPrimaryTask())) {
			LogUtil.audit("调工作流");
			this.completeInvestigateTaskAudit(taskDTO, isPass, userId, investigateTaskAuditDTO.getDescription());
			if(!isPass){
               //taskInfoMapper.updateTaskReback(invesTaskId,"0");
				taskInfoMapper.updateTask(invesTaskId,"0");
				caseProcessService.updateCaseProcess(taskDTO.getReportNo(), taskDTO.getCaseTimes(),
						CaseProcessStatus.INVESTIGATING.getCode());
				//操作记录
				operationRecordService.insertOperationRecordByLabour(taskDTO.getReportNo(), BpmConstants.OC_INVESTIGATE_REVIEW, "不通过", investigateTaskAuditDTO.getDescription());
				//调查退回添加消息提醒
				NoticesDTO noticesDTO = new NoticesDTO();
				noticesDTO.setNoticeClass(BpmConstants.NOTICE_CLASS_OC_BACK);
				noticesDTO.setNoticeSubClass(BpmConstants.NSC_INVESTIGATE_REVIEW);
				noticesDTO.setReportNo(taskDTO.getReportNo());
				noticesDTO.setSourceSystem(BpmConstants.SOURCE_SYSTEM_OC);
				noticesDTO.setCaseTimes(taskDTO.getCaseTimes());
				TaskInfoDTO taskInfoDto = taskInfoMapper.getTaskDtoByTaskId(invesTaskId);
				noticeService.saveNotices(noticesDTO,taskInfoDto.getAssigner());
			}

		} else {
			LogUtil.audit("完成异地调查审核任务");
			this.completeNlinvestigateTaskAudit(taskDTO, isPass, userId);
		}

		//调查完成MQ通知渠道
		if(isPass) {
			SyncCaseStatusDto dto = new SyncCaseStatusDto();
			dto.setReportNo(taskDTO.getReportNo());
			dto.setCaseTimes(taskDTO.getCaseTimes());
			dto.setCaseStatus(SyncCaseStatusEnum.ENDINVESTIGATE);
			mqProducerSyncCaseStatusService.syncCaseStatus(dto);
			List<ReportInfoExEntity>  reportInfos = reportInfoExMapper.getReportInfoEx(taskDTO.getReportNo());
			ReportInfoExEntity reportInfo = reportInfos.get(0);
			if("1".equals(reportInfo.getClaimDealWay())
					&& !"channel".equals(reportInfo.getCompanyId())
					&& 1 == taskDTO.getCaseTimes()) {
				String investigateApprovalTaskId = taskInfoMapper.getLatestTaskId(taskDTO.getReportNo(), taskDTO.getCaseTimes(),BpmConstants.OC_INVESTIGATE_APPROVAL);
				ProblemCaseRequestDTO problemCaseRequestDTO = new ProblemCaseRequestDTO();
				problemCaseRequestDTO.setCompanyId(reportInfo.getCompanyId());
				RequestData requestData = new RequestData();
				requestData.setRegistNo(taskDTO.getReportNo());
				requestData.setProblemNo(investigateApprovalTaskId);
				requestData.setProblemType("05");
				requestData.setCaseConclusion(null);
				//TPA问题件答复接口增加沟通人
				requestData.setDealUser(WebServletContext.getUserName()+"-"+userId);
				try {
					requestData.setReplyTime(DateUtils.parseToFormatString(new Date(), DateUtils.DATE_FORMAT_YYYYMMDDHHMMSS));
				} catch (ParseException e) {
					LogUtil.error("TPA问题件答复接口日期转换错误：{}",e.getMessage());
				}
				requestData.setCaseConclusionDetail(null);
				requestData.setRemark(investigateTaskAuditDTO.getDescription());
				problemCaseRequestDTO.setRequestData(requestData);
				LogUtil.info("TPA全流程案件，报案号：{},问题件答复，答复参数：{}",taskDTO.getReportNo(), JSON.toJSONString(problemCaseRequestDTO));
				ProblemCaseResponseDTO response = tpaFeign.response(problemCaseRequestDTO);
				LogUtil.info("TPA全流程案件，报案号：{},问题件答复，答复返回：{}",taskDTO.getReportNo(), JSON.toJSONString(response));
			}

		}
	}

	public void completeInvestigateTaskAudit(InvestigateTaskDTO taskDTO, boolean isPass, String userId, String description) throws GlobalBusinessException {
		bpmService.completeTask_oc(taskDTO.getReportNo(),taskDTO.getCaseTimes(),BpmConstants.OC_INVESTIGATE_REVIEW);
		//释放跳转状态之前的状态 ，限报案跟踪、收单、理算
		// zjtang 最新逻辑已无挂起状态，且主流程只会有一个流程未处理的情况，故调整查询逻辑为查询挂起的主流程改为查询未处理的主流程
		TaskInfoDTO taskInfoDTO = taskInfoService.ownNewSuspendProcess(taskDTO.getReportNo(),
				taskDTO.getCaseTimes(),BpmConstants.SUPEND_USE, BaseConstant.STRING_0);
		if(isPass) {
			if (null != taskInfoDTO && BpmConstants.SURVER.contains(taskInfoDTO.getTaskDefinitionBpmKey())) {
				bpmService.suspendOrActiveTask_oc(taskDTO.getReportNo(), taskDTO.getCaseTimes(), taskInfoDTO.getTaskDefinitionBpmKey(), false);
				caseProcessService.updateCaseProcess(taskDTO.getReportNo(), taskDTO.getCaseTimes(),
						BpmConstants.TASK_PROCESS_MAP.get( taskInfoDTO.getTaskDefinitionBpmKey())	);
			}
			//发送调查审批完成邮件
//			mailSendService.sendCaseMail(taskDTO.getReportNo(),taskInfoDTO.getAssigner());
			//操作记录
			operationRecordService.insertOperationRecordByLabour(taskDTO.getReportNo(), BpmConstants.OC_INVESTIGATE_REVIEW, "通过", description);
		}
	}


	@Transactional(rollbackFor = Exception.class)
	public void completeNlinvestigateTaskAudit(InvestigateTaskDTO taskDTO, boolean isPass, String userId) throws GlobalBusinessException {
		Map<String, Object> extVariable = new HashMap<>();

		extVariable.put(InvestigateConstants.ID_AHCS_INVESTIGATE_TASK, taskDTO.getIdAhcsInvestigateTask());
		if (isPass) {
			extVariable.put(InvestigateConstants.PASS_REVIEW, InvestigateConstants.BPM_BOOLEAN_TRUE);
		} else {
			extVariable.put(InvestigateConstants.PASS_REVIEW, InvestigateConstants.BPM_BOOLEAN_FALSE);
		}
	}



	@Override
	public InvestigateTaskAuditVO getInvestigateTaskAuditForReportByInvestigateId(String idAhcsInvestigate) throws GlobalBusinessException {

		InvestigateTaskAuditVO investigateTaskAuditVO =  investigateTaskAuditDao.getInvestigateTaskAuditForReportByInvestigateId(idAhcsInvestigate);

		if (null == investigateTaskAuditVO){
			investigateTaskAuditVO = new InvestigateTaskAuditVO();
		}

		List<InvestigateTaskVO> investigateTaskVOS = investigateTaskService.getInvestigateTaskByInvestigateId(idAhcsInvestigate)
				.stream()
				.filter(p -> InvestigateConstants.VALIDATE_FLAG_YES.equals(p.getIsPrimaryTask())
				|| InvestigateConstants.VALIDATE_FLAG_YES.equals(p.getIsOffsiteTask()))
				.collect(Collectors.toList());

		if (ListUtils.isNotEmpty(investigateTaskVOS)){
			int index = 1;
			for (InvestigateTaskVO investigateTaskVO : investigateTaskVOS){
				List<InvestigateTaskAuditVO> investigateTaskAuditVOList = investigateTaskAuditDao.listInvestigateTaskAuditsByTaskId(investigateTaskVO.getIdAhcsInvestigateTask());
				if (ListUtils.isNotEmpty(investigateTaskAuditVOList)
						&& InvestigateConstants.VALIDATE_FLAG_YES.equals(investigateTaskVO.getIsPrimaryTask())){
						investigateTaskVO.setReturnTimes(investigateTaskAuditVOList.size()-1);
						investigateTaskVO.setTaskName("调查（本地）");
				}
				if (InvestigateConstants.VALIDATE_FLAG_YES.equals(investigateTaskVO.getIsOffsiteTask())){
					investigateTaskAuditVO.setHasOffsiteTask(InvestigateConstants.VALIDATE_FLAG_YES);
					investigateTaskVO.setTaskName("异地协查-" + index);
				}
				index++;
			}
		}
		investigateTaskAuditVO.setInvestigateTaskVOS(investigateTaskVOS);
		investigateTaskAuditVO.setInvestigateScoreDTOList(investigateScoreService.listInvestigateScore(idAhcsInvestigate,InvestigateConstants.VALIDATE_FLAG_YES));


		InvestigateScoreDTO investigateScoreDTO = investigateScoreService.listScore(idAhcsInvestigate);
		investigateTaskAuditVO.setInvestigateScoreDTO(investigateScoreDTO);
		investigateTaskAuditVO.setFeeAuditOption(investigateTaskAuditVO.getIsHasAdjustingFee());

		if (null !=investigateTaskAuditVO.getReviewUserUm()){
			investigateTaskAuditVO.setReviewUserName( StringUtils.isEmptyStr(investigateTaskAuditVO.getReviewUserUm()) ? "":userInfoService.getUserNameById(investigateTaskAuditVO.getReviewUserUm()));
		}
		return investigateTaskAuditVO;
	}


	@Override
	public InvestigateAuditVO getApproveInfoByIdAhcsInvestigateTask(String idAhcsInvestigateTask) {
		InvestigateAuditVO approveInfoByIdAhcsInvestigateTask = investigateTaskAuditDao.getApproveInfoByIdAhcsInvestigateTask(idAhcsInvestigateTask);
		approveInfoByIdAhcsInvestigateTask=approveInfoByIdAhcsInvestigateTask ==null?new InvestigateAuditVO():approveInfoByIdAhcsInvestigateTask;
		return approveInfoByIdAhcsInvestigateTask;
	}
}