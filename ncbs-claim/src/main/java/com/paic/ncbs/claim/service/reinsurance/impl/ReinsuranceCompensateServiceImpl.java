package com.paic.ncbs.claim.service.reinsurance.impl;

import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.enums.ReinsuranceClaimTypeEnum;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimateChangeMapper;
import com.paic.ncbs.claim.dao.mapper.reinsurance.SendReinsuranceRecordMapper;
import com.paic.ncbs.claim.model.dto.estimate.EstimateChangeDTO;
import com.paic.ncbs.claim.model.dto.reinsurance.RepayCalDTO;
import com.paic.ncbs.claim.service.reinsurance.ReinsuranceCompensateService;
import com.paic.ncbs.claim.service.reinsurance.ReinsuranceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@RefreshScope
@Slf4j
@Service
public class ReinsuranceCompensateServiceImpl implements ReinsuranceCompensateService {

    @Autowired
    private ReinsuranceService reinsuranceService;

    @Autowired
    private SendReinsuranceRecordMapper sendReinsuranceRecordMapper;
    @Autowired
    private WholeCaseBaseMapper wholeCaseBaseMapper;
    @Autowired
    private EstimateChangeMapper estimateChangeMapper;

    @Value("${switch.reinsuranceCompensate:N}")
    private String switchReinsuranceCompensate;

    @Async("asyncPool")
    @Override
    public void reinsuranceCompensate(List<RepayCalDTO> dtos) {
        log.info("ReinsuranceCompensateService.reinsuranceCompensate begin, switchReinsuranceCompensate={}", switchReinsuranceCompensate);

        // 配置开关
        if (ConstValues.NO.equals(switchReinsuranceCompensate)) {
            return;
        }

        // 没有入参则直接查库
        if (CollectionUtils.isEmpty(dtos)) {
            dtos = sendReinsuranceRecordMapper.getCompensateList();
        }

        if (CollectionUtils.isEmpty(dtos)) {
            log.info("ReinsuranceCompensateService.reinsuranceCompensate, dtos empty");
            return;
        }

        for (RepayCalDTO dto : dtos) {
            String reportNo = dto.getReportNo();
            Integer caseTimes = dto.getCaseTimes();
            WholeCaseBaseEntity wholeCase = wholeCaseBaseMapper.getWholeCaseBaseByReportNoAndCaseTimes(reportNo, caseTimes);
            if (Objects.isNull(wholeCase)) {
                continue;
            }
            // 重开
            if (caseTimes > 1) {
                dto.setClaimType(ReinsuranceClaimTypeEnum.CASE_REOPEN);
                reinsuranceService.compensate(dto);
            } else {
                // 首案立案
                if (ConstValues.YES.equals(wholeCase.getIsRegister())) {
                    dto.setClaimType(ReinsuranceClaimTypeEnum.REGISTER);
                    reinsuranceService.compensate(dto);
                }

                // 首案未决调整
                List<EstimateChangeDTO> estimateChangeList = estimateChangeMapper.getLastEstimateChangeList(reportNo, caseTimes);
                if (CollectionUtils.isNotEmpty(estimateChangeList)) {
                    dto.setClaimType(ReinsuranceClaimTypeEnum.ESTIMATE_LOSS);
                    reinsuranceService.compensate(dto);
                }
            }

            // 结案
            if (ConstValues.CASE_STATUS_END.equals(wholeCase.getWholeCaseStatus())) {
                dto.setClaimType(ReinsuranceClaimTypeEnum.ENDCASE);
                dto.setIndemnityConclusion(wholeCase.getIndemnityConclusion());
                reinsuranceService.compensate(dto);
            }
        }

    }

    @Override
    public void compensateOnBusinessLink(List<RepayCalDTO> dtos) {
        log.info("ReinsuranceCompensateService.compensateOnBusinessLink begin, switchReinsuranceCompensate={}", switchReinsuranceCompensate);

        // 配置开关
        if (ConstValues.NO.equals(switchReinsuranceCompensate)) {
            return;
        }

        // 没有入参则直接查库
        if (CollectionUtils.isEmpty(dtos)) {
            dtos = sendReinsuranceRecordMapper.getCompensateList();
        }

        if (CollectionUtils.isEmpty(dtos)) {
            log.info("ReinsuranceCompensateService.compensateOnBusinessLink, dtos empty");
            return;
        }

        for (RepayCalDTO dto : dtos) {
            String reportNo = dto.getReportNo();
            Integer caseTimes = dto.getCaseTimes();
            WholeCaseBaseEntity wholeCase = wholeCaseBaseMapper.getWholeCaseBaseByReportNoAndCaseTimes(reportNo, caseTimes);
            if (Objects.isNull(wholeCase)) {
                continue;
            }
            reinsuranceService.compensate(dto);
        }
    }
}
