package com.paic.ncbs.claim.dao.mapper.endcase;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseExEntity;
import com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WholeCaseBaseExMapper extends BaseDao<WholeCaseBaseExEntity> {

    int deleteByPrimaryKey(String idClmWholeCaseBaseEx);

    int insert(WholeCaseBaseExEntity record);

    int insertSelective(WholeCaseBaseExEntity record);

    WholeCaseBaseExEntity selectByPrimaryKey(String idClmWholeCaseBaseEx);

    List<WholeCaseBaseExEntity> selectByReportNo(String report);

    int updateByPrimaryKeySelective(WholeCaseBaseExEntity record);

    int updateByPrimaryKey(WholeCaseBaseExEntity record);

    void insertList(@Param("list") List<WholeCaseBaseExEntity> list);

    /**
     * 案件重开，数据拷贝
     * @param dto
     */
    void copyForCaseReopen(CaseReopenCopyDTO dto);
}