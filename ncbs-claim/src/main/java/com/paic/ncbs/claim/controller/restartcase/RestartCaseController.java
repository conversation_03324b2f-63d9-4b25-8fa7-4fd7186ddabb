package com.paic.ncbs.claim.controller.restartcase;

import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyFormDTO;
import com.paic.ncbs.claim.model.dto.restartcase.ApprovalProcessDTO;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO;
import com.paic.ncbs.claim.model.vo.restartcase.RestartCaseVO;
import com.paic.ncbs.claim.service.common.ClaimUpdateDocumentFullDateService;
import com.paic.ncbs.claim.service.restartcase.RestartCaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @date 2023-05-17 11:17
 */
@Api(tags = "案件重开")
@RestController
@RequestMapping("action/restartcase")
@Slf4j
public class RestartCaseController {

    @Autowired
    private RestartCaseService restartCaseService;
    @Autowired
    private ClaimUpdateDocumentFullDateService claimUpdateDocumentFullDateService;

    /**
     * 案件查询
     */
    @ApiOperation("查询已结案件信息列表")
    @PostMapping(value = "/listCase", produces = {"application/json"})
    public ResponseResult<Object> listCase(@RequestBody WholeCaseVO queryVO) {
        LogUtil.audit("#获取结案信息列表#入参# reportNo={},caseNo={},reportBatchNo={},policyNo={}", queryVO.getReportNo(), queryVO.getCaseNo(), queryVO.getReportBatchNo(), queryVO.getPolicyNo());
        try {
            this.validParam(queryVO);
            queryVO.setUserId(WebServletContext.getUserId());
            return ResponseResult.success(restartCaseService.getHistoryCaseList(queryVO));
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
    }

    @ApiOperation("重开审批处理")
    @PostMapping(value = "/approvalProcess", produces = {"application/json"})
    public ResponseResult<Object> approvalProcess(@RequestBody ApprovalProcessDTO approvalProcessDTO) {
        LogUtil.audit("#重开审批处理#入参# reportNo={},caseTimes={},isAgree={}", approvalProcessDTO.getReportNo(), approvalProcessDTO.getCaseTimes(), approvalProcessDTO.getIsAgree());
        if (StringUtils.isEmptyStr(approvalProcessDTO.getReportNo())) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "报案号不能为空");
        }
        if (StringUtils.isEmptyStr(approvalProcessDTO.getIdClmRestartCaseRecord())) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "表主键不能为空");
        }
        if (approvalProcessDTO.getIsAgree() < 0 || approvalProcessDTO.getIsAgree() > 1) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "审批结果入参错误");
        }
        restartCaseService.approvalProcess(approvalProcessDTO);
        return ResponseResult.success();
    }

    @ApiOperation("重开修改放弃")
    @PostMapping(value = "/giveUpRestart", produces = {"application/json"})
    public ResponseResult<Object> giveUpRestart(@RequestBody ApprovalProcessDTO approvalProcessDTO) {
        LogUtil.audit("#重开修改放弃#入参# reportNo={},caseTimes={}", approvalProcessDTO.getReportNo(), approvalProcessDTO.getCaseTimes(), approvalProcessDTO.getIsAgree());
        if (StringUtils.isEmptyStr(approvalProcessDTO.getReportNo())) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "报案号不能为空");
        }
        if (StringUtils.isEmptyStr(approvalProcessDTO.getIdClmRestartCaseRecord())) {
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "表主键不能为空");
        }
        restartCaseService.giveUpRestart(approvalProcessDTO);
        return ResponseResult.success();
    }

    private void validParam(WholeCaseVO queryVO) {
        boolean isMatch;
        if (StringUtils.isNotEmpty(queryVO.getReportNo())) {
            //只允许有字母和数字
            isMatch = StringUtils.validLetterNumber(queryVO.getReportNo());
            if (!isMatch) {
                throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "案件号输入不符合规范");
            }
        }
        if (StringUtils.isNotEmpty(queryVO.getPolicyNo())) {
            isMatch = StringUtils.validLetterNumberAndSize(queryVO.getPolicyNo(), "0", "20");
            if (!isMatch) {
                throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "保单号输入不符合规范");
            }
        }
    }

    /**
     * 重开记录 查
     */
    @ApiOperation("查询重开案件信息列表")
    @PostMapping(value = "/listRestartCase", produces = {"application/json"})
    public ResponseResult<Object> listRestartCase(@RequestBody WholeCaseVO queryVO) {
        LogUtil.audit("#查询重开案件信息列表#入参# reportNo={},caseNo={},reportBatchNo={},policyNo={}", queryVO.getReportNo(), queryVO.getCaseNo(), queryVO.getReportBatchNo(), queryVO.getPolicyNo());
        String reportNo = queryVO.getReportNo();
        Integer caseTimes = queryVO.getCaseTimes();
        try {
            //return ResponseResult.success(restartCaseService.getRestartCaseList(reportNo, caseTimes));
            return ResponseResult.success(restartCaseService.getRestratCases(reportNo, caseTimes));
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
    }

    /**
     * 查询本次重开案件信息列表
     */
    @ApiOperation("查询本次重开案件信息列表")
    @PostMapping(value = "/thisRestartCaseList", produces = {"application/json"})
    public ResponseResult<Object> thisRestartCaseList(@RequestBody WholeCaseVO queryVO) {
        LogUtil.audit("#查询本次重开案件信息列表#入参# reportNo={},caseNo={},reportBatchNo={},policyNo={}", queryVO.getReportNo(), queryVO.getCaseNo(), queryVO.getReportBatchNo(), queryVO.getPolicyNo());
        String reportNo = queryVO.getReportNo();
        Integer caseTimes = queryVO.getCaseTimes();
        try {
            return ResponseResult.success(restartCaseService.getThisRestratCases(reportNo, caseTimes));
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
    }

    /**
     * 重开记录 增
     */
    @ApiOperation("增加重开案件信息")
    @PostMapping(value = "/addRestartCase", produces = {"application/json"})
    public ResponseResult<Object> addRestartCase(@RequestBody RestartCaseVO restartCaseVO) {
        LogUtil.audit("addRestartCase重开案件号{}, 增加重开案件信息#入参{}",restartCaseVO.getReportNo(), JSONObject.toJSONString(restartCaseVO));
        try {
            restartCaseService.addRestartCase(restartCaseVO);
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
        return ResponseResult.success();
    }

    /**
     * 重开修改案件信息
     */
    @ApiOperation("重开修改案件信息")
    @PostMapping(value = "/updateRestartCase", produces = {"application/json"})
    public ResponseResult<Object> updateRestartCase(@RequestBody RestartCaseVO restartCaseVO) {
        LogUtil.audit("updateRestartCase重开修改案件号{}, 重开修改案件信息#入参{}",restartCaseVO.getReportNo(), JSONObject.toJSONString(restartCaseVO));
        try {
            restartCaseService.updateRestartCase(restartCaseVO);
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
        return ResponseResult.success();
    }
    /**
     * 案件重开详情
     */
    @ApiOperation("查看重开案件信息详情")
    @PostMapping(value = "/getRestartCaseDetail", produces = {"application/json"})
    public ResponseResult<Object> getRestartCaseDetail(@RequestBody EstimatePolicyFormDTO queryVO) {
        LogUtil.audit("#查询重开案件详情#入参# reportNo={},caseTimes={},reportBatchNo={},policyNo={}", queryVO.getReportNo(), queryVO.getCaseTimes());
        String reportNo = queryVO.getReportNo();
        Integer caseTimes = queryVO.getCaseTimes();
        try {
            return ResponseResult.success(restartCaseService.getRestartCaseDetail(reportNo, caseTimes));
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
    }

    /**
     * 案件重开修改详情
     */
    @ApiOperation("查看案件重开修改详情")
    @PostMapping(value = "/getRestartModifyCaseDetail", produces = {"application/json"})
    public ResponseResult<Object> getRestartModifyCaseDetail(@RequestBody EstimatePolicyFormDTO queryVO) {
        LogUtil.audit("#查看案件重开修改详情#入参# reportNo={},caseTimes={},reportBatchNo={},policyNo={}", queryVO.getReportNo(), queryVO.getCaseTimes());
        String reportNo = queryVO.getReportNo();
        Integer caseTimes = queryVO.getCaseTimes();
        try {
            return ResponseResult.success(restartCaseService.getRestartModifyCaseDetail(reportNo, caseTimes));
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
    }

    /**
     * 案件重开审批详情
     */
    @ApiOperation("查看重开案件审批信息详情")
    @PostMapping(value = "/getRestartCaseApprovalDetail", produces = {"application/json"})
    public ResponseResult<Object> getRestartCaseApprovalDetail(@RequestBody EstimatePolicyFormDTO queryVO) {
        LogUtil.audit("#查询重开案件详情#入参# reportNo={},caseTimes={},reportBatchNo={},policyNo={}", queryVO.getReportNo(), queryVO.getCaseTimes());
        String reportNo = queryVO.getReportNo();
        Integer caseTimes = queryVO.getCaseTimes();
        try {
            return ResponseResult.success(restartCaseService.getRestartCaseApprovalDetail(reportNo, caseTimes));
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
    }

    /**
     * 本案历史赔付信息
     */
    @ApiOperation("本案历史赔付信息")
    @GetMapping(value = "/getHistoryReportPayList", produces = {"application/json"})
    public ResponseResult<Object> getHistoryReportPayList(@RequestParam String reportNo, @RequestParam Integer caseTimes) {
        try {
            return ResponseResult.success(restartCaseService.getHistoryReportPayList(reportNo, caseTimes));
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
    }
    /**
     * 本案历史赔付信息
     */
    @ApiOperation("测试更新资料齐全时间")
    @GetMapping(value = "/updateDocumentFullDate")
    public ResponseResult<Object> updateDocumentFullDate(@RequestParam String reportNo, @RequestParam Integer caseTimes) {
        claimUpdateDocumentFullDateService.updateDocFullDate(reportNo,caseTimes,null);
        return ResponseResult.success();
    }
}
