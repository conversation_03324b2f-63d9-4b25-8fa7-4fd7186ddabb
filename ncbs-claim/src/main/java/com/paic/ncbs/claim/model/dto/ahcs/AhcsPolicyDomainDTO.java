package com.paic.ncbs.claim.model.dto.ahcs;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.paic.ncbs.claim.dao.entity.ahcs.*;
import com.paic.ncbs.claim.model.dto.ocas.PlyApplyFreeze;
import com.paic.ncbs.claim.model.dto.pay.PayInfoDTO;
import com.paic.ncbs.claim.model.dto.riskppt.ReportRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.secondunderwriting.ClmsPolicyHistoryUwInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.BeneficaryDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyBaseInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyInfoExDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPlanDutySumDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.RiskObjectDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.RiskPackageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 保单列表详情
 */
@Data
public class AhcsPolicyDomainDTO extends PolicyBaseInfoDTO {

    private String validPolicy;

    private String validType;

    private BigDecimal applyNum;

    private String policySystem;

    /**
     * 产品大类:01车,02个财,03个意
     */
    private String productClass;

    private String technicProductCode;

    /**
     * 是否在途 Y:在途  N:不在途
     */
    private String isProcess;

    /**
     * 批改号
     */
    private String endorseApplyNo;

    /**
     * 录入时间
     */
    private Date inputDate;

    /**
     * 批改场景
     */
    private String scenceList;

    // 支付方式- 区分支付给谁 ，废弃
    private String paymentMode ;

    public String getPaymentCompanyMode() {
        return paymentCompanyMode;
    }

    public void setPaymentCompanyMode(String paymentCompanyMode) {
        this.paymentCompanyMode = paymentCompanyMode;
    }

    //支付方式- 区分支付给谁
    private String paymentCompanyMode;

    /**
     * 保单基本信息
     */
    private AhcsPolicyInfoEntity ahcsPolicyInfo;

    private List<AhcsInsuredPresonDTO> ahcsInsuredPresonDTOs = new ArrayList<>();
    /**
     * 共保信息
     */
    private List<AhcsCoinsureEntity> ahcsCoinsure = new ArrayList<>();
    /**
     * 险种、责任列表
     */
    private List<AhcsPolicyPlanDTO> ahcsPolicyPlanDTOs = new ArrayList<AhcsPolicyPlanDTO>();
    /**
     * 投保人列表
     */
    private List<AhcsPolicyHolderEntity> ahcsPolicyHolder = new ArrayList<AhcsPolicyHolderEntity>();
    /**
     * 特约列表
     */
    private List<AhcsSpecialPromiseEntity> ahcsSpecialPromise = new ArrayList<AhcsSpecialPromiseEntity>();
    /**
     * 险种责任列表
     */
    private List<PolicyPlanDutySumDTO> policyPlanDutySumDTOs = new ArrayList<PolicyPlanDutySumDTO>();
    /**
     * 受益人基本信息列表
     */
    private List<BeneficaryDTO> beneficaryDTOs = new ArrayList<BeneficaryDTO>();
    /**
     * 保单相关
     */
    private PolicyInfoExDTO policyInfoExDTO;
    /**
     * 套餐
     */
    private List<RiskPackageDTO> riskPackageDTOs = new ArrayList<RiskPackageDTO>();
    /**
     * 支付信息列表
     */
    private List<PayInfoDTO> payInfoList = new ArrayList<PayInfoDTO>();

    private String isMergePolicyFlg;
    /**
     *风险类列表
     */
    private List<RiskObjectDTO> riskObjectList = new ArrayList<RiskObjectDTO>();

    private String userId;

    private AhcsPolicyDutyEntity batchCloseDuty;

    private List<PlyApplyFreeze> plyApplyFreezes;

    private String isFamily;
    private String onlineOrderNo;

    /**
     * 调用用途，01-查询
     */
    private String invokePurpose;

    public String getProfitCenter() {
        return profitCenter;
    }

    public void setProfitCenter(String profitCenter) {
        this.profitCenter = profitCenter;
    }

    /**
     * 利润中心
     */
    @ApiModelProperty(value = "利润中心")
    private String profitCenter;

    public String getIsTransferInsure() {
        return isTransferInsure;
    }

    public void setIsTransferInsure(String isTransferInsure) {
        this.isTransferInsure = isTransferInsure;
    }

    /**
     * 是否转保 0:否,1:是
     */
    private String isTransferInsure;

    /**
     * 转保保单
     */
    private String transferInsurancePolicyNo;

    /**
     * 转保止期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date transferInsuranceEndDate;

    /**
     * 转保产品名称
     */
    private String transferInsuranceProductName;

    public String getTransferInsurancePolicyNo() {
        return transferInsurancePolicyNo;
    }

    public void setTransferInsurancePolicyNo(String transferInsurancePolicyNo) {
        this.transferInsurancePolicyNo = transferInsurancePolicyNo;
    }

    public Date getTransferInsuranceEndDate() {
        return transferInsuranceEndDate;
    }

    public void setTransferInsuranceEndDate(Date transferInsuranceEndDate) {
        this.transferInsuranceEndDate = transferInsuranceEndDate;
    }

    public String getTransferInsuranceProductName() {
        return transferInsuranceProductName;
    }

    public void setTransferInsuranceProductName(String transferInsuranceProductName) {
        this.transferInsuranceProductName = transferInsuranceProductName;
    }

    /**
     * 抄单核保信息
     */
    private List<ClmsPolicyHistoryUwInfoDTO> policyHistoryUwInfoDTOList;
    private List<ReportRiskPropertyDTO> reportRiskPropertyList;

    public List<RiskObjectDTO> getRiskObjectList() {
        return riskObjectList;
    }

    public void setRiskObjectList(List<RiskObjectDTO> riskObjectList) {
        this.riskObjectList = riskObjectList;
    }

    public String getIsMergePolicyFlg() {
        return isMergePolicyFlg;
    }

    public void setIsMergePolicyFlg(String isMergePolicyFlg) {
        this.isMergePolicyFlg = isMergePolicyFlg;
    }

    public String getPolicySystem() {
        return policySystem;
    }

    public void setPolicySystem(String policySystem) {
        this.policySystem = policySystem;
    }

    public BigDecimal getApplyNum() {
        return applyNum;
    }

    public void setApplyNum(BigDecimal applyNum) {
        this.applyNum = applyNum;
    }

    public String getValidPolicy() {
        return validPolicy;
    }

    public void setValidPolicy(String validPolicy) {
        this.validPolicy = validPolicy;
    }

    public String getValidType() {
        return validType;
    }

    public void setValidType(String validType) {
        this.validType = validType;
    }

    public AhcsPolicyInfoEntity getAhcsPolicyInfo() {
        return ahcsPolicyInfo;
    }

    public void setAhcsPolicyInfo(AhcsPolicyInfoEntity ahcsPolicyInfo) {
        this.ahcsPolicyInfo = ahcsPolicyInfo;
    }

    public List<AhcsInsuredPresonDTO> getAhcsInsuredPresonDTOs() {
        return ahcsInsuredPresonDTOs;
    }

    public void setAhcsInsuredPresonDTOs(List<AhcsInsuredPresonDTO> ahcsInsuredPresonDTOs) {
        this.ahcsInsuredPresonDTOs = ahcsInsuredPresonDTOs;
    }

    public List<AhcsCoinsureEntity> getAhcsCoinsure() {
        return ahcsCoinsure;
    }

    public void setAhcsCoinsure(List<AhcsCoinsureEntity> ahcsCoinsure) {
        this.ahcsCoinsure = ahcsCoinsure;
    }

    public List<AhcsPolicyPlanDTO> getAhcsPolicyPlanDTOs() {
        return ahcsPolicyPlanDTOs;
    }

    public void setAhcsPolicyPlanDTOs(List<AhcsPolicyPlanDTO> ahcsPolicyPlanDTOs) {
        this.ahcsPolicyPlanDTOs = ahcsPolicyPlanDTOs;
    }

    public List<AhcsPolicyHolderEntity> getAhcsPolicyHolder() {
        return ahcsPolicyHolder;
    }

    public void setAhcsPolicyHolder(List<AhcsPolicyHolderEntity> ahcsPolicyHolder) {
        this.ahcsPolicyHolder = ahcsPolicyHolder;
    }

    public List<AhcsSpecialPromiseEntity> getAhcsSpecialPromise() {
        return ahcsSpecialPromise;
    }

    public void setAhcsSpecialPromise(List<AhcsSpecialPromiseEntity> ahcsSpecialPromise) {
        this.ahcsSpecialPromise = ahcsSpecialPromise;
    }

    public List<PolicyPlanDutySumDTO> getPolicyPlanDutySumDTOs() {
        return policyPlanDutySumDTOs;
    }

    public void setPolicyPlanDutySumDTOs(List<PolicyPlanDutySumDTO> policyPlanDutySumDTOs) {
        this.policyPlanDutySumDTOs = policyPlanDutySumDTOs;
    }

    public PolicyInfoExDTO getPolicyInfoExDTO() {
        return policyInfoExDTO;
    }

    public void setPolicyInfoExDTO(PolicyInfoExDTO policyInfoExDTO) {
        this.policyInfoExDTO = policyInfoExDTO;
    }

    public List<BeneficaryDTO> getBeneficaryDTOs() {
        return beneficaryDTOs;
    }

    public void setBeneficaryDTOs(List<BeneficaryDTO> beneficaryDTOs) {
        this.beneficaryDTOs = beneficaryDTOs;
    }

    public String getProductClass() {
        return productClass;
    }

    public void setProductClass(String productClass) {
        this.productClass = productClass;
    }

    public List<RiskPackageDTO> getRiskPackageDTOs() {
        return riskPackageDTOs;
    }

    public void setRiskPackageDTOs(List<RiskPackageDTO> riskPackageDTOs) {
        this.riskPackageDTOs = riskPackageDTOs;
    }

    public String getTechnicProductCode() {
        return technicProductCode;
    }

    public void setTechnicProductCode(String technicProductCode) {
        this.technicProductCode = technicProductCode;
    }

    public List<PayInfoDTO> getPayInfoList() {
        return payInfoList;
    }

    public void setPayInfoList(List<PayInfoDTO> payInfoList) {
        this.payInfoList = payInfoList;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public AhcsPolicyDutyEntity getBatchCloseDuty() {
        return batchCloseDuty;
    }

    public void setBatchCloseDuty(AhcsPolicyDutyEntity batchCloseDuty) {
        this.batchCloseDuty = batchCloseDuty;
    }

    public List<PlyApplyFreeze> getPlyApplyFreezes() {
        return plyApplyFreezes;
    }

    public void setPlyApplyFreezes(List<PlyApplyFreeze> plyApplyFreezes) {
        this.plyApplyFreezes = plyApplyFreezes;
    }

    public String getIsProcess() {
        return isProcess;
    }

    public void setIsProcess(String isProcess) {
        this.isProcess = isProcess;
    }

    public String getEndorseApplyNo() {
        return endorseApplyNo;
    }

    public void setEndorseApplyNo(String endorseApplyNo) {
        this.endorseApplyNo = endorseApplyNo;
    }

    public Date getInputDate() {
        return inputDate;
    }

    public void setInputDate(Date inputDate) {
        this.inputDate = inputDate;
    }

    public String getScenceList() {
        return scenceList;
    }

    public void setScenceList(String scenceList) {
        this.scenceList = scenceList;
    }

    public String getPaymentMode() {
        return paymentMode;
    }

    public void setPaymentMode(String paymentMode) {
        this.paymentMode = paymentMode;
    }

    public List<ClmsPolicyHistoryUwInfoDTO> getPolicyHistoryUwInfoDTOList() {
        return policyHistoryUwInfoDTOList;
    }

    public void setPolicyHistoryUwInfoDTOList(List<ClmsPolicyHistoryUwInfoDTO> policyHistoryUwInfoDTOList) {
        this.policyHistoryUwInfoDTOList = policyHistoryUwInfoDTOList;
    }

    public List<ReportRiskPropertyDTO> getReportRiskPropertyList() {
        return reportRiskPropertyList;
    }

    public void setReportRiskPropertyList(List<ReportRiskPropertyDTO> reportRiskPropertyList) {
        this.reportRiskPropertyList = reportRiskPropertyList;
    }

    public String getIsFamily() {
        return isFamily;
    }

    public void setIsFamily(String isFamily) {
        this.isFamily = isFamily;
    }

    public String getonlineOrderNo() {
        return onlineOrderNo;
    }

    public void setOnlineOrderNo(String onlineOrderNo) {
        this.onlineOrderNo = onlineOrderNo;
    }

    public String getIsShowEPolicy() {
        return isShowEPolicy;
    }

    public void setIsShowEPolicy(String isShowEPolicy) {
        this.isShowEPolicy = isShowEPolicy;
    }

    private String isShowEPolicy;

}
