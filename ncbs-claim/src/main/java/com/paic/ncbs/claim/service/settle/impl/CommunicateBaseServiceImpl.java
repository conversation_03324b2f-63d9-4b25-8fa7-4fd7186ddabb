package com.paic.ncbs.claim.service.settle.impl;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.constant.communicate.CommunicateConsts;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.enums.SyncCaseStatusEnum;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity;
import com.paic.ncbs.claim.dao.mapper.communicate.CommunicateBaseMapper;
import com.paic.ncbs.claim.dao.mapper.communicate.CommunicateDetailMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.feign.TPAFeign;
import com.paic.ncbs.claim.model.dto.communicate.CommunicateBaseDTO;
import com.paic.ncbs.claim.model.dto.communicate.CommunicateDetailDTO;
import com.paic.ncbs.claim.model.dto.mq.syncstatus.SyncCaseStatusDto;
import com.paic.ncbs.claim.model.dto.openapi.TpaCommunicateResponseDto;
import com.paic.ncbs.claim.model.dto.other.CommonParameterTinyDTO;
import com.paic.ncbs.claim.model.dto.problem.ProblemCaseRequestDTO;
import com.paic.ncbs.claim.model.dto.problem.ProblemCaseResponseDTO;
import com.paic.ncbs.claim.model.dto.problem.RequestData;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.communicate.CommunicateBaseVO;
import com.paic.ncbs.claim.model.vo.communicate.CommunicateDetailVO;
import com.paic.ncbs.claim.mq.producer.MqProducerSyncCaseStatusService;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.other.CommonParameterService;
import com.paic.ncbs.claim.service.report.ReportInfoExService;
import com.paic.ncbs.claim.service.secondunderwriting.ClmsSecondUnderwritingService;
import com.paic.ncbs.claim.service.settle.CommunicateBaseService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.util.*;

import static com.paic.ncbs.claim.common.constant.communicate.CommunicateConsts.UW_NEGOTIATE;

@Service("communicateBaseService")
public class CommunicateBaseServiceImpl implements CommunicateBaseService {

    @Autowired
    private CommunicateBaseMapper communicateBaseMapper;
    @Autowired
    private CommunicateDetailMapper communicateDetailDao;
    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private CommonParameterService commonParameterService;

    @Autowired
    private BpmService bpmService ;

    @Autowired
    private CaseProcessService caseProcessService;
    @Autowired
    private TaskInfoService taskInfoService;
    @Autowired
    private ClmsSecondUnderwritingService clmsSecondUnderwritingService;
    @Autowired
    private ReportInfoExService reportInfoExService;
    @Autowired
    private TPAFeign tpaFeign;
    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Autowired
    private MqProducerSyncCaseStatusService mqProducerSyncCaseStatusService;

    @Autowired
    private IOperationRecordService operationRecordService;

    @Override
    public CommunicateBaseVO getHistoryCommunicateBaseList(String reportNo, int caseTimes) throws GlobalBusinessException {
        CommunicateBaseVO communicateBaseVOResult = new CommunicateBaseVO();
        List<CommunicateBaseDTO> communicateBaseDTOList = communicateBaseMapper.getHistoryCommunicateBaseList(reportNo,caseTimes);
        List<CommunicateBaseVO> historyCommunicateBaseVOList = listConversionDtoToVO(communicateBaseDTOList);
        communicateBaseVOResult.setHistoryCommunicateBaseVOList(historyCommunicateBaseVOList);
        communicateBaseVOResult.setReportNo(reportNo);
        communicateBaseVOResult.setCaseTimes(caseTimes);
        getCommonParameterMap(communicateBaseVOResult);
        return communicateBaseVOResult;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void finishCommunicate(CommunicateBaseVO communicateBaseVO) throws GlobalBusinessException {
        Date pageDate =  new Date();
        String userId = communicateBaseVO.getUserId();
        String idAhcsCommunicateBase = communicateBaseVO.getIdAhcsCommunicateBase();
        String reportNo = communicateBaseVO.getReportNo();
        Integer caseTimes = communicateBaseVO.getCaseTimes();
        String taskId = taskInfoService.getTaskId(reportNo,caseTimes,BpmConstants.OC_COMMUNICATE);

        LogUtil.audit("页面发送时间={},UserId={},沟通主键ID={}", pageDate, userId, idAhcsCommunicateBase);
        checkCommunicateInfo(idAhcsCommunicateBase, pageDate);
        List<CommunicateDetailDTO> communicateDetailDTOList = communicateDetailDao.getCommunicateDetailDTOListById(idAhcsCommunicateBase);
        if (ListUtils.isNotEmpty(communicateDetailDTOList)) {
            List<CommunicateDetailDTO> updateCommunicateDetailDTOList = new ArrayList<>();
            for (CommunicateDetailDTO communicateDetailDTO : communicateDetailDTOList) {
                if (CommunicateConsts.COMMUNICATE_TASK_STATUS_PENDING.equals(communicateDetailDTO.getTaskStatus())) {
                    communicateDetailDTO.setUpdatedBy(userId);
                    communicateDetailDTO.setTaskStatus(CommunicateConsts.COMMUNICATE_TASK_STATUS_FINISH);
                    communicateDetailDTO.setDealUm(userId);
                    communicateDetailDTO.setDealDate(new Date());
                    if (CommunicateConsts.COMMUNICATE_ROLE_COMMUNICATER.equals(communicateDetailDTO.getRole())) {
                        communicateDetailDTO.setCommunicateContent(communicateBaseVO.getCommunicateContent());
                        communicateDetailDTO.setPayAmount(communicateBaseVO.getPayAmount());
                        updateCommunicateDetailDTOList.add(communicateDetailDTO);
                    }
                }

            }
            if (ListUtils.isNotEmpty(updateCommunicateDetailDTOList)) {
                communicateDetailDao.batchUpdateCommunicateDetailDao(updateCommunicateDetailDTOList);
            }
        }
        updateCommunicateBaseDTO(idAhcsCommunicateBase, CommunicateConsts.COMMUNICATE_STATUS_FINISH, userId);
        // 完成沟通任务 释放原先的任务状态 并更新案件状态到原先任务状态
        bpmService.completeTask_oc(reportNo, caseTimes, BpmConstants.OC_COMMUNICATE);
        // zjtang 最新逻辑已无挂起状态，且主流程只会有一个流程未处理的情况，故调整查询逻辑为查询挂起的主流程改为查询未处理的主流程
        TaskInfoDTO taskInfoDTO = taskInfoService.ownNewSuspendProcess(reportNo, caseTimes, BpmConstants.SUPEND_USE, BpmConstants.TASK_STATUS_PENDING);

        if (taskInfoDTO==null){
            throw new GlobalBusinessException("沟通发起环节异常");
        }
        String taskDefinitionBpmKey = taskInfoDTO.getTaskDefinitionBpmKey();
        bpmService.suspendOrActiveTask_oc(reportNo,caseTimes, taskDefinitionBpmKey,false);
        caseProcessService.updateCaseProcess(reportNo, caseTimes, BpmConstants.TASK_PROCESS_MAP.get(taskDefinitionBpmKey));
        //操作记录
        operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_COMMUNICATE, "回复", null, userId);
        //TPA全流程案件调用TPA答复接口
        List<ReportInfoExEntity>  reportInfos = reportInfoExService.getReportInfoEx(reportNo);
        ReportInfoExEntity reportInfo = reportInfos.get(0);
        if("1".equals(reportInfo.getClaimDealWay())
                && !"channel".equals(reportInfo.getCompanyId())
                && 1 == caseTimes) {
            ProblemCaseRequestDTO problemCaseRequestDTO = new ProblemCaseRequestDTO();
            problemCaseRequestDTO.setCompanyId(reportInfo.getCompanyId());
            RequestData requestData = new RequestData();
            requestData.setRegistNo(reportNo);
            requestData.setProblemNo(taskId);
            requestData.setProblemType("02");
            try {
                requestData.setReplyTime(DateUtils.parseToFormatString(new Date(), DateUtils.DATE_FORMAT_YYYYMMDDHHMMSS));
            } catch (ParseException e) {
                LogUtil.error("TPA问题件答复接口日期转换错误：{}",e.getMessage());
            }
            requestData.setRemark(communicateBaseVO.getCommunicateContent());
            //TPA问题件答复接口增加沟通人
            requestData.setDealUser(WebServletContext.getUserName()+"-"+userId);
            requestData.setPayAmount(communicateBaseVO.getPayAmount());
            problemCaseRequestDTO.setRequestData(requestData);
            LogUtil.info("TPA全流程案件，报案号：{},问题件答复，答复参数：{}",reportNo, JSON.toJSONString(problemCaseRequestDTO));
            ProblemCaseResponseDTO response = tpaFeign.response(problemCaseRequestDTO);
            LogUtil.info("TPA全流程案件，报案号：{},问题件答复，答复返回：{}",reportNo, JSON.toJSONString(response));
        }
        //沟通完成通知渠道
        SyncCaseStatusDto dto = new SyncCaseStatusDto();
        dto.setReportNo(reportNo);
        dto.setCaseTimes(caseTimes);
        dto.setCaseStatus(SyncCaseStatusEnum.ENDCOMMUNICATE);
        mqProducerSyncCaseStatusService.syncCaseStatus(dto);

        LogUtil.audit("沟通主键ID={},沟通完成成功!", idAhcsCommunicateBase);

    }

    @Override
    public CommunicateBaseVO getCommunicateDetailByCommunicateBaseId(String idAhcsCommunicateBase) throws GlobalBusinessException {
        LogUtil.audit("根据ID查询沟通信息 ID={}", idAhcsCommunicateBase);
        CommunicateBaseDTO communicateBaseDTO = communicateBaseMapper.getCommunicateBaseById(idAhcsCommunicateBase);
        CommunicateBaseVO communicateBaseVO = conversionDtoToVO(communicateBaseDTO);
        LogUtil.audit("根据ID查询沟通信息 ID={}完成,沟通信息为={}", idAhcsCommunicateBase, communicateBaseVO);
        return communicateBaseVO;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @Override
    public void sendCommunicateBaseInfo(CommunicateBaseVO communicateBaseVO) throws GlobalBusinessException {

        String reportNo = communicateBaseVO.getReportNo();
        Integer caseTimes = communicateBaseVO.getCaseTimes();
        String userId = communicateBaseVO.getUserId();
        Date initiatDate = new Date();
        /*  delete by zjtang 取消旧校验逻辑
        //校验当前案件是否存在除过主任务外其他未完成的流程,若存在，就不能发起沟通
        TaskInfoDTO taskInfoDTOCondition = new TaskInfoDTO();
        taskInfoDTOCondition.setCaseTimes(caseTimes);
        taskInfoDTOCondition.setReportNo(reportNo);
        List<TaskInfoVO> taskInfoVOList = taskInfoMapper.getUndoTaskInfoList(taskInfoDTOCondition);
         if(ListUtils.isNotEmpty(taskInfoVOList) && taskInfoVOList.size() > 0) {
            throw new GlobalBusinessException("当前案件存在其他未处理任务，不能发起沟通！");
        }
         */
        //校验当前流程是否有冲突 沟通 发起
        bpmService.processCheck(reportNo,BpmConstants.OC_COMMUNICATE,BpmConstants.OPERATION_INITIATE);
        List<String> dealUmList = communicateBaseVO.getAssignerList();
        if (CollectionUtils.isEmpty(dealUmList)){
            throw new GlobalBusinessException("处理人不能为空");
        }
        if (dealUmList.get(0).equals(userId)){
            throw new GlobalBusinessException("沟通人不能是自己");
        }
        if (dealUmList.get(0).equals(userId)){
            throw new GlobalBusinessException(ErrorCode.CommunicateBase.INITIATOR_UM_IS_DEAL_UM);
        }
        LogUtil.audit("报案号={},发送沟通去掉工作承接页面选择的处理人信息为={}", reportNo, dealUmList);

        List<CommunicateDetailDTO> communicateDetailDTOList = new ArrayList<>();
        // 校验必填项
        checkCommunicateBaseFieldEmpty(communicateBaseVO);
        TaskInfoDTO taskInfoDTO = taskInfoService.ownNewSuspendProcess(reportNo, caseTimes, BpmConstants.SUPEND_USE, BpmConstants.TASK_STATUS_PENDING);
        if (taskInfoDTO==null){
            throw new GlobalBusinessException("沟通发起环节异常");
        }
        CommunicateBaseDTO communicateBaseDTO = saveCommunicateBaseDTO(communicateBaseVO, userId, initiatDate,taskInfoDTO.getTaskDefinitionBpmKey());
        String newIdAhcsCommunicateBase = communicateBaseDTO.getIdAhcsCommunicateBase();
        LogUtil.audit("沟通首次发送.保存沟通主键信息成功,沟通主键ID为={}", newIdAhcsCommunicateBase);
        saveCommunicateDetailDTO(communicateBaseVO, userId, initiatDate, dealUmList, communicateDetailDTOList, newIdAhcsCommunicateBase);

        //操作记录
        operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_COMMUNICATE, "发起", null, userId);
        // 创建沟通任务
        bpmService.startProcessOc(communicateBaseVO.getReportNo(), caseTimes, BpmConstants.OC_COMMUNICATE,newIdAhcsCommunicateBase,dealUmList.get(0),communicateBaseVO.getDepartmentCode());
        // 挂起原先的任务 并更新案件状态到沟通待回复
        caseProcessService.updateCaseProcess(reportNo, caseTimes, CaseProcessStatus.COMMUNICATE_PENDING_RESPONDED.getCode());

        bpmService.suspendOrActiveTask_oc(reportNo,caseTimes, taskInfoDTO.getTaskDefinitionBpmKey(),true);


        //沟通完成通知渠道
        SyncCaseStatusDto dto = new SyncCaseStatusDto();
        dto.setReportNo(reportNo);
        dto.setCaseTimes(caseTimes);
        dto.setCaseStatus(SyncCaseStatusEnum.COMMUNICATE);
        mqProducerSyncCaseStatusService.syncCaseStatus(dto);

    }


    @Override
    public CommunicateBaseVO initCommunicateBaseInfo(CommunicateBaseVO communicateBaseVO) throws GlobalBusinessException {

        String reportNo = communicateBaseVO.getReportNo();
        Integer caseTimes = communicateBaseVO.getCaseTimes();
        String idAhcsCommunicateBase = communicateBaseVO.getIdAhcsCommunicateBase();
        String userId = communicateBaseVO.getUserId();
        LogUtil.audit("调用初始化方法开始,报案号={},赔付次数={},沟通主键ID={},用户ID={}", reportNo, caseTimes, idAhcsCommunicateBase, userId);
        CommunicateBaseVO communicateBaseVOResult = new CommunicateBaseVO();
        if (StringUtils.isNotEmpty(idAhcsCommunicateBase)) {
            String role = null;
            String dealPersonTaskStatus = CommunicateConsts.COMMUNICATE_TASK_STATUS_FINISH;
            CommunicateBaseDTO communicateBaseDTO = communicateBaseMapper.getCommunicateBaseById(idAhcsCommunicateBase);
            if (null == communicateBaseDTO) {
                return communicateBaseVOResult;
            }
            BeanUtils.copyProperties(communicateBaseDTO, communicateBaseVOResult);
            List<CommunicateDetailDTO> communicateDetailDTOList = communicateBaseDTO.getCommunicateDetailDTOList();
            String initiatorUm = communicateBaseDTO.getInitiatorUm();
            if (ListUtils.isNotEmpty(communicateDetailDTOList)) {
                Set<String> assignerList = new LinkedHashSet<>();
                for (CommunicateDetailDTO communicateDetailDTO : communicateDetailDTOList) {
                    String tempRole = communicateDetailDTO.getRole();
                    String dealUm = communicateDetailDTO.getDealUm();
                    if (CommunicateConsts.COMMUNICATE_ROLE_COMMUNICATER.equals(tempRole)) {
                        String userNameAndUserId = getUserNameAndUserId(dealUm);
                        assignerList.add(userNameAndUserId);
                    }
                    if (StringUtils.isEmptyStr(role)) {
                        if (CommunicateConsts.COMMUNICATE_ROLE_INITIATOR.equals(tempRole) && userId.equals(dealUm)) {
                            role = CommunicateConsts.COMMUNICATE_ROLE_INITIATOR;
                        } else if (CommunicateConsts.COMMUNICATE_ROLE_COMMUNICATER.equals(tempRole) && userId.equals(dealUm)) {
                            role = CommunicateConsts.COMMUNICATE_ROLE_COMMUNICATER;
                        }
                    }
                    if (CommunicateConsts.COMMUNICATE_ROLE_COMMUNICATER.equals(role) && userId.equals(dealUm)
                            && CommunicateConsts.COMMUNICATE_TASK_STATUS_PENDING.equals(communicateDetailDTO.getTaskStatus())) {
                        dealPersonTaskStatus = communicateDetailDTO.getTaskStatus();
                    }
                }

                List<String> result = new ArrayList<>(assignerList);
                communicateBaseVOResult.setAssignerList(result);
                List<CommunicateDetailVO> communicateDetailVOList = conversionCommunicateDetailDTOToVO(communicateDetailDTOList);
                communicateBaseVOResult.setCommunicateDetailVOList(communicateDetailVOList);
            }
            communicateBaseVOResult.setInitiatorUm(getUserNameAndUserId(initiatorUm));
            communicateBaseVOResult.setRole(role);
            communicateBaseVOResult.setDealPersonTaskStatus(dealPersonTaskStatus);

        }
        communicateBaseVOResult.setReportNo(reportNo);
        communicateBaseVOResult.setCaseTimes(caseTimes);
        getCommonParameterMap(communicateBaseVOResult);
        communicateBaseVOResult.setReceiveVoucherUm("");
        communicateBaseVOResult.setPageDate(new Date());
        LogUtil.audit("初始化方法完成,报案号={},赔付次数={}", reportNo, caseTimes);
        return communicateBaseVOResult;
    }


    private String getUserNameAndUserId(String userId) {
        String userName = userInfoService.getUserNameById(userId);
        if (StringUtils.isNotEmpty(userName)) {
            return userName + "-" + userId;
        }
        return userId;
    }

    private void checkCommunicateInfo(String idAhcsCommunicateBase, Date pageDate) throws GlobalBusinessException {
        String maxDate = communicateDetailDao.getMaxCommunicateDetailById(idAhcsCommunicateBase);
        if (StringUtils.isNotEmpty(maxDate)) {
            try {
                if (DateUtils.compareTimeBetweenDate(DateUtils.parseToFormatString(pageDate, DateUtils.FULL_DATE_STR), maxDate)) {
                    throw new GlobalBusinessException(ErrorCode.CommunicateBase.NEW_MSG_NEED_RELOAD, "");
                }
            } catch (ParseException e) {
                LogUtil.info("沟通主键ID={},有最新的消息.请刷新页面.", idAhcsCommunicateBase);
                throw new GlobalBusinessException(ErrorCode.CommunicateBase.SEND_DATE_ERROR, e);
            }
        }
        CommunicateBaseDTO communicateBaseDTO = communicateBaseMapper.getCommunicateBaseById(idAhcsCommunicateBase);

        if (null != communicateBaseDTO && CommunicateConsts.COMMUNICATE_STATUS_FINISH.equals(communicateBaseDTO.getCommunicateStatus())) {
            throw new GlobalBusinessException(ErrorCode.CommunicateBase.COMMUNICATE_TASK_FINISH, "");
        }
    }


    private void updateCommunicateBaseDTO(String idAhcsCommunicateBase, String communicateStatus, String userId) throws GlobalBusinessException {
        CommunicateBaseDTO communicateBaseDTO = new CommunicateBaseDTO();
        communicateBaseDTO.setUpdatedBy(userId);
        communicateBaseDTO.setIdAhcsCommunicateBase(idAhcsCommunicateBase);
        communicateBaseDTO.setCommunicateStatus(communicateStatus);
        if (CommunicateConsts.COMMUNICATE_STATUS_FINISH.equals(communicateStatus)) {
            communicateBaseDTO.setFinishDate(new Date());
        }
        communicateBaseMapper.updateCommunicateBaseInfo(communicateBaseDTO);
    }


    private CommunicateBaseDTO saveCommunicateBaseDTO(CommunicateBaseVO communicateBaseVO, String userId, Date initiatDate, String taskDefinitionBpmKey) throws GlobalBusinessException {
        CommunicateBaseDTO communicateBaseDTO = new CommunicateBaseDTO();
        BeanUtils.copyProperties(communicateBaseVO, communicateBaseDTO);
        communicateBaseDTO.setCommunicateStatus(CommunicateConsts.COMMUNICATE_STATUS_PENDING);
        communicateBaseDTO.setInitiatorUm(userId);
        communicateBaseDTO.setInitiatDate(initiatDate);
        communicateBaseDTO.setCreatedBy(userId);
        communicateBaseDTO.setUpdatedBy(userId);
        communicateBaseDTO.setCommunicateFrom(taskDefinitionBpmKey);
        communicateBaseMapper.insertCommunicateBaseDTO(communicateBaseDTO);
        return communicateBaseDTO;
    }


    private void saveCommunicateDetailDTO(CommunicateBaseVO communicateBaseVO, String userId, Date initiatDate, List<String> dealUmList, List<CommunicateDetailDTO> communicateDetailDTOList,
                                          String newIdAhcsCommunicateBase) throws GlobalBusinessException {
        // 发起人
        CommunicateDetailDTO saveCcommunicateDetailDTO = getCommunicateDetailDTO(userId, initiatDate, userId, newIdAhcsCommunicateBase);
        saveCcommunicateDetailDTO.setRole(CommunicateConsts.COMMUNICATE_ROLE_INITIATOR);
        saveCcommunicateDetailDTO.setTaskStatus(CommunicateConsts.COMMUNICATE_TASK_STATUS_FINISH);
        saveCcommunicateDetailDTO.setDealDate(new Date());
        saveCcommunicateDetailDTO.setCommunicateContent(communicateBaseVO.getCommunicateContent());
        communicateDetailDTOList.add(saveCcommunicateDetailDTO);

        // 沟通人，现在只会有一个人
        CommunicateDetailDTO pendingCommunicateDetailDTO = null;
        for (String dealUm : dealUmList) {
            pendingCommunicateDetailDTO = getCommunicateDetailDTO(userId, initiatDate, dealUm, newIdAhcsCommunicateBase);
            pendingCommunicateDetailDTO.setRole(CommunicateConsts.COMMUNICATE_ROLE_COMMUNICATER);
            pendingCommunicateDetailDTO.setTaskStatus(CommunicateConsts.COMMUNICATE_TASK_STATUS_PENDING);
            communicateDetailDTOList.add(pendingCommunicateDetailDTO);
        }
        communicateDetailDao.batchInsertCommunicateDetailDTO(communicateDetailDTOList);
    }


    private List<CommunicateBaseVO> listConversionDtoToVO(List<CommunicateBaseDTO> communicateBaseDTOList) throws GlobalBusinessException {
        List<CommunicateBaseVO> communicateBaseVOList = new ArrayList<>();
        if (ListUtils.isEmptyList(communicateBaseDTOList)) {
            return communicateBaseVOList;
        }

        for (CommunicateBaseDTO communicateBaseDTO : communicateBaseDTOList) {
            CommunicateBaseVO communicateBaseVO = new CommunicateBaseVO();
            BeanUtils.copyProperties(communicateBaseDTO, communicateBaseVO);
            communicateBaseVO.setInitiatorUm(getUserNameAndUserId(communicateBaseDTO.getInitiatorUm()));
            communicateBaseVOList.add(communicateBaseVO);
        }

        return communicateBaseVOList;
    }


    private CommunicateBaseVO conversionDtoToVO(CommunicateBaseDTO communicateBaseDTO) throws GlobalBusinessException {

        CommunicateBaseVO communicateBaseVO = new CommunicateBaseVO();
        if (null == communicateBaseDTO) {
            return communicateBaseVO;
        }
        BeanUtils.copyProperties(communicateBaseDTO, communicateBaseVO);
        communicateBaseVO.setInitiatorUm(getUserNameAndUserId(communicateBaseDTO.getInitiatorUm()));

        List<CommunicateDetailDTO> communicateDetailDTOs = communicateBaseDTO.getCommunicateDetailDTOList();
        List<CommunicateDetailVO> communicateDetailVOList = conversionCommunicateDetailDTOToVO(communicateDetailDTOs);
        communicateBaseVO.setCommunicateDetailVOList(communicateDetailVOList);
        return communicateBaseVO;
    }


    private List<CommunicateDetailVO> conversionCommunicateDetailDTOToVO(List<CommunicateDetailDTO> communicateDetailDTOList) {
        List<CommunicateDetailVO> communicateDetailVOList = new ArrayList<CommunicateDetailVO>();
        if (ListUtils.isEmptyList(communicateDetailDTOList)) {
            return communicateDetailVOList;
        }

        CommunicateDetailVO communicateDetailVO = null;
        for (CommunicateDetailDTO communicateDetailDTO : communicateDetailDTOList) {

            if (StringUtils.isEmptyStr(communicateDetailDTO.getCommunicateContent())) {
                continue;
            }
            communicateDetailVO = new CommunicateDetailVO();
            BeanUtils.copyProperties(communicateDetailDTO, communicateDetailVO);
            communicateDetailVO.setInitiatorUm(getUserNameAndUserId(communicateDetailDTO.getInitiatorUm()));
            communicateDetailVO.setDealUm(getUserNameAndUserId(communicateDetailDTO.getDealUm()));
            communicateDetailVOList.add(communicateDetailVO);
        }
        return communicateDetailVOList;
    }

    private void getCommonParameterMap(CommunicateBaseVO communicateBaseVO) throws GlobalBusinessException {

        String[] collectionCodeList = {CommunicateConsts.AHCS_GTHJ, CommunicateConsts.AHCS_GTZT, CommunicateConsts.AHCS_GT_TYPE};
        List<CommonParameterTinyDTO> commonParameterTinys = commonParameterService.getCommonParameterList(collectionCodeList);

        if (ListUtils.isEmptyList(commonParameterTinys)) {
            return;
        }
        Map<String, String> communicateLinkMap = new HashMap<>();
        Map<String, String> communicateTitleMap = new HashMap<>();
        Map<String, String> documenCommunicateTypeMap = new HashMap<>();

        for (CommonParameterTinyDTO commonParameterTinyDTO : commonParameterTinys) {
            if (CommunicateConsts.AHCS_GTHJ.equals(commonParameterTinyDTO.getCollectionCode())) {
                communicateLinkMap.put(commonParameterTinyDTO.getValueCode(), commonParameterTinyDTO.getValueChineseName());
            }
            if (CommunicateConsts.AHCS_GTZT.equals(commonParameterTinyDTO.getCollectionCode())) {
                communicateTitleMap.put(commonParameterTinyDTO.getValueCode(), commonParameterTinyDTO.getValueChineseName());
            }
            if (CommunicateConsts.AHCS_GT_TYPE.equals(commonParameterTinyDTO.getCollectionCode())) {
                documenCommunicateTypeMap.put(commonParameterTinyDTO.getValueCode(), commonParameterTinyDTO.getValueChineseName());
            }
        }
        Integer uwCount = clmsSecondUnderwritingService.getUnBackUWCount(communicateBaseVO.getReportNo(), communicateBaseVO.getCaseTimes());
        if (uwCount>0){
            communicateTitleMap.put(UW_NEGOTIATE,"理赔二核协谈");
        }
        if (communicateLinkMap.size() > 0) {
            communicateBaseVO.setCommunicateLinkMap(communicateLinkMap);
        }
        if (communicateTitleMap.size() > 0) {
            communicateBaseVO.setCommunicateTitleMap(communicateTitleMap);
        }
        if (documenCommunicateTypeMap.size() > 0) {
            communicateBaseVO.setDocumenCommunicateTypeMap(documenCommunicateTypeMap);
        }
    }


    private void checkCommunicateBaseFieldEmpty(CommunicateBaseVO communicateBaseVO) throws GlobalBusinessException {

        String communicateLink = communicateBaseVO.getCommunicateLink();
        String communicateTitle = communicateBaseVO.getCommunicateTitle();
        /*if (StringUtils.isEmptyStr(communicateLink)) {
            throw new GlobalBusinessException(ErrorCode.CommunicateBase.COMMUNICATE_LINK_NOT_NULL);
        }*/
        if (StringUtils.isEmptyStr(communicateTitle)) {
            throw new GlobalBusinessException(ErrorCode.CommunicateBase.COMMUNICATE_TITLE_NOT_NULL);
        }
        if (StringUtils.isEmptyStr(communicateBaseVO.getCommunicateContent())) {
            throw new GlobalBusinessException(ErrorCode.CommunicateBase.COMMUNICATE_CONTENT_NOT_NULL);
        }

        if (ListUtils.isEmptyList(communicateBaseVO.getAssignerList())) {
            throw new GlobalBusinessException(ErrorCode.CommunicateBase.ASSIGNER_LIST_NOT_NULL);
        }

    }


    private CommunicateDetailDTO getCommunicateDetailDTO(String initiatorUm, Date initiatDate, String dealUm, String idAhcsCommunicateBase) {
        CommunicateDetailDTO communicateDetailDTO = new CommunicateDetailDTO();
        communicateDetailDTO.setCreatedBy(initiatorUm);
        communicateDetailDTO.setUpdatedBy(initiatorUm);
        communicateDetailDTO.setInitiatorUm(initiatorUm);
        communicateDetailDTO.setInitiatDate(initiatDate);
        communicateDetailDTO.setDealUm(dealUm);
        communicateDetailDTO.setIdAhcsCommunicateBase(idAhcsCommunicateBase);
        communicateDetailDTO.setIdAhcsCommunicateDetail(UuidUtil.getUUID());
        return communicateDetailDTO;
    }


    @Override
    public Integer getCommunicateBaseTimes(String reportNo, int caseTimes) {
        List<CommunicateBaseDTO> historyCommunicateBaseList = communicateBaseMapper.getHistoryCommunicateBaseList(reportNo, caseTimes);
        return historyCommunicateBaseList.size();
    }


    @Override
    public CommunicateBaseVO getNotCommunicateTask(CommunicateBaseVO communicateBaseVO) throws GlobalBusinessException {
        CommunicateBaseDTO communicateBaseDTO = communicateBaseMapper.getNotCommunicateBaseDTO(communicateBaseVO);
        if (null == communicateBaseDTO) {
            return null;
        }
        return conversionDtoToVO(communicateBaseDTO);
    }

    /**
     * TPA发起沟通接口
     * @param communicateBaseVO
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @Override
    public TpaCommunicateResponseDto sendCommunicateBaseInfoForTPA(CommunicateBaseVO communicateBaseVO) throws GlobalBusinessException {
        TpaCommunicateResponseDto tpaCommunicateResponseDto = new TpaCommunicateResponseDto();
        String reportNo = communicateBaseVO.getReportNo();
        Integer caseTimes = communicateBaseVO.getCaseTimes();
        String userId = communicateBaseVO.getUserId();
        String assigner = communicateBaseVO.getAssigner();
        try {
            UserInfoDTO userInfo = userInfoService.getUserInfoDTO(communicateBaseVO.getAssigner());
        } catch (Exception e) {
            if("查询用户失败".equals(e.getMessage())
                    && StringUtils.isNotEmpty(communicateBaseVO.getDefaultUserId())) {
                assigner = communicateBaseVO.getDefaultUserId();
                LogUtil.error("报案号：{}于TPA发起沟通，沟通人：{}失效，替换为低码配置沟通人：{}", reportNo, communicateBaseVO.getAssigner(), assigner);
            }else {
                LogUtil.error("报案号：{}于TPA发起沟通，沟通人：{}失效，且未配置沟通人，联系业务在低码配置！", reportNo, assigner);
                throw new GlobalBusinessException(ErrorCode.CALL_INTERACTED_SYSTEM_ERROR,"无效沟通人");
            }
        }

        List<String> dealUmList = new ArrayList<>();
        if(!StringUtils.isEmptyStr(assigner)){
            dealUmList.add(assigner);
        }

        Date initiatDate = new Date();

        if (CollectionUtils.isEmpty(dealUmList)){
            throw new GlobalBusinessException("处理人不能为空");
        }
        if (dealUmList.get(0).equals(userId)){
            throw new GlobalBusinessException("沟通人不能是自己");
        }
        if (dealUmList.get(0).equals(userId)){
            throw new GlobalBusinessException(ErrorCode.CommunicateBase.INITIATOR_UM_IS_DEAL_UM);
        }
        bpmService.processCheck(reportNo,BpmConstants.OC_COMMUNICATE,BpmConstants.OPERATION_INITIATE);
        LogUtil.audit("报案号={},发送沟通去掉工作承接页面选择的处理人信息为={}", reportNo, dealUmList);
        communicateBaseVO.setAssignerList(dealUmList);
        List<CommunicateDetailDTO> communicateDetailDTOList = new ArrayList<>();
        // 校验必填项
        checkCommunicateBaseFieldEmpty(communicateBaseVO);
        TaskInfoDTO taskInfoDTO = taskInfoService.ownNewSuspendProcess(reportNo, caseTimes, BpmConstants.SUPEND_USE, BpmConstants.TASK_STATUS_PENDING);
        if (taskInfoDTO==null){
            throw new GlobalBusinessException("沟通发起环节异常");
        }
        //当前案件节点
        if(BpmConstants.OC_CHECK_DUTY.equals(taskInfoDTO.getTaskDefinitionBpmKey())){
            communicateBaseVO.setCommunicateLink(BpmConstants.CHECK_DUTY);
        }else if(BpmConstants.OC_REPORT_TRACK.equals(taskInfoDTO.getTaskDefinitionBpmKey())){
            communicateBaseVO.setCommunicateLink(BpmConstants.REPORT_TRACK);
        }else if(BpmConstants.OC_MANUAL_SETTLE.equals(taskInfoDTO.getTaskDefinitionBpmKey())){
            communicateBaseVO.setCommunicateLink(BpmConstants.MANUAL_SETTLE);
        }else if(BpmConstants.OC_SETTLE_REVIEW.equals(taskInfoDTO.getTaskDefinitionBpmKey())){
            communicateBaseVO.setCommunicateLink(BpmConstants.SETTLE_REVIEW);
        }
        CommunicateBaseDTO communicateBaseDTO = saveCommunicateBaseDTO(communicateBaseVO, userId, initiatDate,BpmConstants.OC_COMMUNICATE);
        String newIdAhcsCommunicateBase = communicateBaseDTO.getIdAhcsCommunicateBase();
        LogUtil.audit("沟通首次发送.保存沟通主键信息成功,沟通主键ID为={}", newIdAhcsCommunicateBase);
        saveCommunicateDetailDTO(communicateBaseVO, userId, initiatDate, dealUmList, communicateDetailDTOList, newIdAhcsCommunicateBase);
        //操作记录
        operationRecordService.insertOperationRecord(reportNo, BpmConstants.OC_COMMUNICATE, "发起", null, dealUmList.get(0));
        // 创建沟通任务
        bpmService.startProcessOc(communicateBaseVO.getReportNo(), caseTimes, BpmConstants.OC_COMMUNICATE,newIdAhcsCommunicateBase,dealUmList.get(0),communicateBaseVO.getDepartmentCode());
        // 挂起原先的任务 并更新案件状态到沟通待回复
        caseProcessService.updateCaseProcess(reportNo, caseTimes, CaseProcessStatus.COMMUNICATE_PENDING_RESPONDED.getCode());

        bpmService.suspendOrActiveTask_oc(reportNo,caseTimes, taskInfoDTO.getTaskDefinitionBpmKey(),true);
        tpaCommunicateResponseDto.setProblemNo(newIdAhcsCommunicateBase);
        return tpaCommunicateResponseDto;
    }

}
