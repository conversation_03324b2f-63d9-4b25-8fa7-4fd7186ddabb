package com.paic.ncbs.claim.service.report.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.page.PageMethod;
import com.github.pagehelper.util.StringUtil;
import com.paic.ncbs.base.util.MeshSendUtils;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.enums.CertificateTypeEnum;
import com.paic.ncbs.claim.common.enums.PolicyFilesTypeEnum;
import com.paic.ncbs.claim.common.enums.PolicyStatusEnum;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.HttpClientUtil;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.RapeCheckUtil;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonObjectMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.report.PolicyMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.feign.mesh.NbsRequest;
import com.paic.ncbs.claim.feign.mesh.OcasRequest;
import com.paic.ncbs.claim.model.dto.fileupload.FileRealDownLoadAddressInfoDTO;
import com.paic.ncbs.claim.model.dto.ocas.OcasInsuredDTO;
import com.paic.ncbs.claim.model.dto.ocas.OcasPolicyDTO;
import com.paic.ncbs.claim.model.dto.ocas.PolicyFilesInfoVO;
import com.paic.ncbs.claim.model.dto.report.PageDTO;
import com.paic.ncbs.claim.model.dto.report.PolicyNoNbsQueryDTO;
import com.paic.ncbs.claim.model.dto.report.PolicyRiskSubPropDTO;
import com.paic.ncbs.claim.model.dto.report.RatingQueryVO;
import com.paic.ncbs.claim.model.dto.riskppt.RiskPropertyParamDTO;
import com.paic.ncbs.claim.model.vo.nbs.MiniOrderInfo;
import com.paic.ncbs.claim.model.vo.nbs.PageQueryMiniOrderVO;
import com.paic.ncbs.claim.model.vo.report.PolicyQueryVO;
import com.paic.ncbs.claim.model.vo.report.PolicyRiskQueryVO;
import com.paic.ncbs.claim.sao.CustomerInfoStoreSAO;
import com.paic.ncbs.claim.service.fileupload.FileUploadService;
import com.paic.ncbs.claim.service.other.impl.TaskListService;
import com.paic.ncbs.claim.service.report.PolicyService;
import com.paic.ncbs.claim.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
@RefreshScope
public class PolicyServiceImpl implements PolicyService{
    @Value("${es.baseUrl:http://ncbs-policy-query.lb.ssdev.com:48047/query-policy/es/queryClaimTaskInfoByParam}")
    private String esBaseUrl;
    @Value("${switch.mesh}")
    private Boolean switchMesh;
    @Autowired
    private TaskListService taskListService;
    @Autowired
    private FileUploadService fileUploadService;
    @Autowired
    private OcasRequest ocasRequest;
    @Autowired
    private PolicyMapper policyMapper;
    @Autowired
    private CustomerInfoStoreSAO customerInfoStoreSAO;
    @Autowired
    private OcasMapper ocasMapper;
    @Autowired
    private PersonObjectMapper personObjectMapper;
    @Autowired
    private NbsRequest nbsRequest;

    private List<String> getSubDepartmentCodes(String departmentCode) {
        List<String> departmentCodes = null;
        if (StringUtil.isNotEmpty(departmentCode))
            departmentCodes = taskListService.getAllDepartmentCodesByCode(departmentCode);
        return CollectionUtil.distinct(departmentCodes);
    }

    @Override
    public PageDTO<OcasPolicyDTO> getPolicyList(String departmentCode, PolicyQueryVO queryVO) {


        queryVO.setDepartmentCodes(getSubDepartmentCodes(departmentCode));

        PageHelper.startPage(queryVO.getPager().getPageIndex(), queryVO.getPager().getPageRows(), true);
        //美团订单号查询，调用承保接口查询 20250529
        if(StringUtils.isNotEmpty(queryVO.getPolicyNo()) && queryVO.getPolicyNo().startsWith("S")) {
            PolicyNoNbsQueryDTO policyNoNbsQueryDTO = new PolicyNoNbsQueryDTO();
            policyNoNbsQueryDTO.setMiniOrderNo(queryVO.getPolicyNo());
            String result = nbsRequest.getMiniOrderInfo(policyNoNbsQueryDTO);
            Map resultMap = JSON.parseObject(result, Map.class);
            if("000000".equals(resultMap.get("responseCode")) && Objects.nonNull(resultMap.get("data"))) {
                MiniOrderInfo miniOrderInfo = JSON.parseObject(resultMap.get("data").toString(), MiniOrderInfo.class);
                queryVO.setPolicyNo(miniOrderInfo.getPolicyNo());
            } else {
                throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),"美团订单号不存在");
            }
        }
        //查询承保表
        List<OcasPolicyDTO> policyListQuery = policyMapper.getPolicyList(queryVO);
        List<OcasPolicyDTO> policyList = new ArrayList<>();
        for (OcasPolicyDTO ocasPolicyDTO : policyListQuery) {
            if(!PolicyStatusEnum.COMPANY_TERMINATE.getType().equals(ocasPolicyDTO.getStatus())){
                policyList.add(ocasPolicyDTO);
                continue;
            }
            Date date = policyMapper.querySurrender(queryVO.getPolicyNo());
            if(null == date){
                policyList.add(ocasPolicyDTO);
                continue;
            }
            if(new Date().before(date)){
                policyList.add(ocasPolicyDTO);
            }
        }
        //因需要考虑追溯期和报告期，将出险日期在保期内的判断移到Java层 20250213
        //追溯期、报告期:保单起期-追溯期<=事故日期<=保单止期+报告期
        Date acciDate = queryVO.getAccidentDate();
        if(Objects.nonNull(acciDate) && CollectionUtils.isNotEmpty(policyList)) {
            //承保/已承保、退保（需要看保单仍有有效保期）、保单中止 null,B5,P1,01,10  需考虑追溯期和报告期
            List<String> policyStatusList = Arrays.asList(PolicyStatusEnum.B5.getType(),PolicyStatusEnum.P1.getType(),PolicyStatusEnum.STOP.getType());
            List<OcasPolicyDTO> filterPolicyList = new ArrayList<>();
            for(OcasPolicyDTO policy: policyList){
                String policyStatus = policy.getStatus();
                Date insuranceBeginDate = policy.getInsuranceBeginDate();
                Date insuranceEndDate = policy.getInsuranceEndDate();
                if(policyStatusList.contains(policyStatus)
                        || (PolicyStatusEnum.ONE.getType().equals(policyStatus) && !insuranceBeginDate.equals(insuranceEndDate))) {
                    int prosecutionPeriod = Objects.nonNull(policy.getProsecutionPeriod()) ? policy.getProsecutionPeriod() : 0;
                    int extendReportDate = Objects.nonNull(policy.getExtendReportDate()) ? policy.getExtendReportDate() : 0;
                    if((DateUtils.addDate(DateUtils.beginOfDay(insuranceBeginDate),-prosecutionPeriod).before(acciDate) && DateUtils.addDate(DateUtils.endOfDay(insuranceEndDate),extendReportDate).after(acciDate))
                    || DateUtils.addDate(DateUtils.beginOfDay(insuranceBeginDate),-prosecutionPeriod).equals(acciDate) || DateUtils.addDate(DateUtils.endOfDay(insuranceEndDate),extendReportDate).equals(acciDate)) {
                        filterPolicyList.add(policy);
                    }
                }else {
                    if((insuranceBeginDate.before(acciDate) && insuranceEndDate.after(acciDate))
                    || insuranceBeginDate.equals(acciDate) || insuranceEndDate.equals(acciDate)) {
                        filterPolicyList.add(policy);
                    }
                }
            }
            policyList = filterPolicyList;
        }

        PageDTO<OcasPolicyDTO> pageDTO = PageDTO.build(policyList, queryVO.getPager());
        PageMethod.clearPage();
        
        if(policyList != null){ 
            for(OcasPolicyDTO policy : policyList){
                policy.setStatusName(PolicyStatusEnum.getName(policy.getStatus()));
                Date now = DateUtils.now();
                boolean selectedBegin = false;
                boolean selectedEnd = false;
                if(policy.getInsuranceEndDate() != null){
                    //当前日期与保单截止日期比较，当前日期>截止日期说明保单失效
                    if(DateUtils.compareTimeBetweenDate(policy.getInsuranceEndDate(),now)){
                        policy.setActive(ConstValues.NO);
                    }
                    //往后一年
                    Date endDate = DateUtils.addMonth(now,12);
                    //往后一年日期 > 保单截止日期 + 保单截止日期 > 今天
                    selectedEnd = DateUtils.compareTimeBetweenDate(policy.getInsuranceEndDate(),endDate) && DateUtils.compareTimeBetweenDate(now,policy.getInsuranceEndDate());
                }

                if(policy.getInsuranceBeginDate() != null){
                    //往前一年
                    Date beginDate = DateUtils.addMonth(now,-12);
                    //往前一年日期<保单生效日 + 保单生效日 < 今天
                    selectedBegin = DateUtils.compareTimeBetweenDate(beginDate,policy.getInsuranceBeginDate()) && DateUtils.compareTimeBetweenDate(policy.getInsuranceBeginDate(),now);
                }
                // 和产品沟通 改成 或
                if(selectedBegin || selectedEnd){
                    //默认勾选“生效日为当前年月日往前1年，终止日为当前年月日往后1年”内保单
                    policy.setSelected(true);
                }
//                if (StringUtils.isNotEmpty(policy.getPerformanceAttributionCode())) {
//                    policy.setDepartmentCode(policy.getPerformanceAttributionCode());
//                    policy.setDepartmentName(policy.getPerformanceAttributionName());
//                }
            }
        }  
        return pageDTO;
    }

    @Override
    public List<PolicyFilesInfoVO> getPolicyFileList(PolicyQueryVO queryVO) {
        Map<String,String> param = new HashMap<>();
        param.put("policyNo", queryVO.getPolicyNo());
        param.put("certificateNo", queryVO.getCertificateNo());
        param.put("certificateType", queryVO.getCertificateType());
        String res = ocasRequest.getPolicyInfoByPolicyNo(param);
        Map resultMap = JSON.parseObject(res, Map.class);
        Map<String, Object> contractDTO = (Map) resultMap.get("contractDTO");
        List attachmentGroupList = (List) contractDTO.get("attachmentGroupList");
        List<PolicyFilesInfoVO> resultList = new ArrayList<>();
        if (RapeCheckUtil.isNotEmpty(attachmentGroupList)) {
            int size = 100;
            if (attachmentGroupList.size() < size) {
                size = attachmentGroupList.size();
            }
            for (int i = 0; i < size; i++) {
                Map<String, Object> attachmentGroup = (Map<String, Object>) attachmentGroupList.get(i);
                List documentInfoList = (List) attachmentGroup.get("documentInfoList");
                if (RapeCheckUtil.isNotEmpty(documentInfoList)) {
                    for (int j = 0, length = documentInfoList.size(); j < length; j++) {
                        PolicyFilesInfoVO policyFilesInfoVO = new PolicyFilesInfoVO();
                        Map<String, Object> document = (Map<String, Object>) documentInfoList.get(j);
                        Integer serialNo = j + 1;
                        policyFilesInfoVO.setSerialNo(serialNo);
                        String fileClass = (String) document.get("documentClass");
                        policyFilesInfoVO.setFileClassCode(fileClass);
                        policyFilesInfoVO.setFileClass(PolicyFilesTypeEnum.getName(fileClass));
                        policyFilesInfoVO.setFileName((String) document.get("documentName"));
                        policyFilesInfoVO.setUploadDate((String) document.get("uploadDate"));
                        String url = (String) document.get("url");
                        FileRealDownLoadAddressInfoDTO fileRealDownLoadAddressInfoDTO = fileUploadService.getDocumentsRealAddress(url);
                        policyFilesInfoVO.setUrl(fileRealDownLoadAddressInfoDTO.getFileRealAddress());
                        resultList.add(policyFilesInfoVO);
                    }
                }
            }
        }
        return resultList;
    }

    @Override
    public List<PolicyFilesInfoVO> getPolicyFileListNew(PolicyQueryVO queryVO) {
        List<PolicyFilesInfoVO> resultList = new ArrayList<>();
        Integer serialNo = 0;
        //查询保单附件
        Map<String,String> param = new HashMap<>();
        param.put("policyNo", queryVO.getPolicyNo());
        param.put("name", queryVO.getInsuredName());
        param.put("certificateNo", queryVO.getCertificateNo());
        param.put("certificateType", queryVO.getCertificateType());
        String res = ocasRequest.getDocumentByPolicyNo(param);
        Map resultMap = JSON.parseObject(res, Map.class);
        List documentInfoList = (List) resultMap.get("documentInfoList");
        if (RapeCheckUtil.isNotEmpty(documentInfoList)) {
            for (int j = 0, length = documentInfoList.size(); j < length; j++) {
                PolicyFilesInfoVO policyFilesInfoVO = new PolicyFilesInfoVO();
                Map<String, Object> document = (Map<String, Object>) documentInfoList.get(j);
                serialNo = j + 1;
                policyFilesInfoVO.setSerialNo(serialNo);
                String fileClass = (String) document.get("documentClass");
                policyFilesInfoVO.setFileClassCode(fileClass);
                policyFilesInfoVO.setFileClass((String) document.get("documentClassName"));
                policyFilesInfoVO.setFileName((String) document.get("documentName"));
                policyFilesInfoVO.setFileType((String) document.get("documentType"));
                policyFilesInfoVO.setUploadDate((String) document.get("createdDate"));
                policyFilesInfoVO.setFileSource(queryVO.getPolicyNo());
                String url = "";
                String documentId = (String) document.get("documentId");
                if (!documentId.contains("cos")){
                    url = (String) document.get("documentDesc");
                } else {
                    url = documentId;
                }
                FileRealDownLoadAddressInfoDTO fileRealDownLoadAddressInfoDTO = fileUploadService.getDocumentsRealAddress(url);
                policyFilesInfoVO.setUrl(fileRealDownLoadAddressInfoDTO.getFileRealAddress());
                resultList.add(policyFilesInfoVO);
            }
        }
        //查询批单附件
        Map<String,Object> paramEPolicy = new HashMap<>();
        paramEPolicy.put("policyNo", queryVO.getPolicyNo());
        paramEPolicy.put("endorseApplyNo", "");
        paramEPolicy.put("applyType","7");
        String resEPolicy = ocasRequest.queryEPolicy(paramEPolicy);
        Map ePolicyResultMap = JSON.parseObject(resEPolicy, Map.class);
        List endorseList = (List) ePolicyResultMap.get("data");
        if (RapeCheckUtil.isNotEmpty(endorseList)) {
            for (int j = 0, length = endorseList.size(); j < length; j++) {
                PolicyFilesInfoVO policyFilesInfoVO = new PolicyFilesInfoVO();
                Map<String, Object> document = (Map<String, Object>) endorseList.get(j);
                serialNo = serialNo + 1;
                policyFilesInfoVO.setSerialNo(serialNo);
                policyFilesInfoVO.setFileClassCode((String) document.get("documentClass"));
                policyFilesInfoVO.setFileClass((String) document.get("documentClassName"));
                policyFilesInfoVO.setFileName((String) document.get("documentName"));
                policyFilesInfoVO.setUploadDate((String) document.get("uploadDate"));
                policyFilesInfoVO.setFileSource((String) document.get("endorseNo"));
                policyFilesInfoVO.setUrl((String) document.get("pdfUrl"));
                resultList.add(policyFilesInfoVO);
            }
        }
        return resultList;
    }
    @Override
    public BigDecimal getPremiumByPolicyNo(PolicyQueryVO queryVO) {
        BigDecimal totalAgreePremium = BigDecimal.ZERO;
        //查询保单附件
        Map<String,String> param = new HashMap<>();
        param.put("policyNo", queryVO.getPolicyNo());
        String res = ocasRequest.getPremiumByPolicyNo(param);
        log.info("getPremiumByPolicyNo: {}", res);
        Map resultMap = JSON.parseObject(res, Map.class);
        Map data = (Map) resultMap.get("data");
        totalAgreePremium = (BigDecimal) data.get("totalAgreePremium");
        return totalAgreePremium;
    }

    @Override
    public PageDTO<OcasInsuredDTO> getPolicyInsuredList(String departmentCode, PolicyQueryVO queryVO) {

        queryVO.setDepartmentCodes(getSubDepartmentCodes(departmentCode));

        PageHelper.startPage(queryVO.getPager().getPageIndex(), queryVO.getPager().getPageRows(), true);

        // 查询被保人表
        List<OcasInsuredDTO> insuredList = policyMapper.getPolicyInsuredList(queryVO);

        PageDTO<OcasInsuredDTO> pageDTO = PageDTO.build(insuredList, queryVO.getPager());
        PageMethod.clearPage();
        
        if(insuredList != null){
            List<RatingQueryVO> ratingQueryList = new ArrayList<>();
            for(OcasInsuredDTO insured : insuredList){
                insured.setCertificateName(CertificateTypeEnum.getName(insured.getCertificateType()));
                RatingQueryVO ratingVO = new RatingQueryVO(insured.getInsuredName());
                ratingVO.setPhoneNumber(insured.getMobileTelephone());
                if(insured.getCertificateType() != null && CertificateTypeEnum.ID_CARD.getType().equals(insured.getCertificateType())){
                    ratingVO.setIdentificationNumber(insured.getCertificateNo());
                }
                ratingQueryList.add(ratingVO);
            }
            try {
                List<String> ratingList = customerInfoStoreSAO.queryCustomerRating(ratingQueryList);
                if(ratingList != null){
                    for(int i=0;i<insuredList.size();i++){
                        insuredList.get(i).setCustomerType(ratingList.get(i));
                    }
                }
            }catch (Exception e){
            }
        }
        return pageDTO;
    }

    @Override
    public PageDTO<PolicyRiskSubPropDTO> getPoliyRiskSubPropList(String departmentCode, PolicyQueryVO queryVO) {
        PageHelper.startPage(queryVO.getPager().getPageIndex(), queryVO.getPager().getPageRows(), true);

        // 查询标的表
        List<PolicyRiskSubPropDTO> riskSubPropList = policyMapper.getPolicyRiskSubPropList(queryVO);
        PageDTO<PolicyRiskSubPropDTO> pageDTO = PageDTO.build(riskSubPropList, queryVO.getPager());
        PageMethod.clearPage();
        return pageDTO;
    }

    /**
     * ES获取保单标的信息，（暂时不支持根据出险时间回溯查询时点数据）
     *
     * @param riskPropQueryVO
     */
    @Override
    public List<PolicyRiskSubPropDTO> getEsPlyRiskSubPropList(PolicyRiskQueryVO riskPropQueryVO) throws Exception {
        if(StringUtils.isEmpty(riskPropQueryVO.getPolicyNo())){
            throw new GlobalBusinessException("保单号和出险时间不能为空");
        }
        Map<String, Object> params = new HashMap<>();
        Map<String, Object> paramMap = riskPropQueryVO.getParamMap();
        params.put("policyNo", riskPropQueryVO.getPolicyNo());
        params.put("effectiveDate", riskPropQueryVO.getAccidentDate());
        if(StringUtils.isNotEmpty(riskPropQueryVO.getRiskGroupType())){
            params.put("trackPlyRiskProperties.riskGroupType", riskPropQueryVO.getRiskGroupType());
        }
        if(StringUtils.isNotEmpty(riskPropQueryVO.getRiskGroupNo())){
            params.put("trackPlyRiskProperties.riskGroupNo", riskPropQueryVO.getRiskGroupNo());
        }
        Set<String> queryKeySet = paramMap.keySet();
        if(CollectionUtils.isNotEmpty(queryKeySet)){
            String prefix;
            if(BaseConstant.TARGET_TYPE_EMPLOYER.equals(riskPropQueryVO.getRiskGroupType())) {
                prefix = "trackPlyRiskPropsubGroups.";
            }else {
                prefix = "trackPlyRiskProperties.riskDetailMap.";
            }
            for (String key : queryKeySet) {
                Object value = paramMap.get(key);
                if(Objects.isNull(value) || "-".equals(value)){
                    continue;
                }
                if(value instanceof CharSequence && (StringUtils.isEmpty((String) value) || StringUtils.isEmpty(((String) value).trim()))){
                    continue;
                }
                if (value instanceof Object[]) {
                    Object[] array = (Object[]) value;
                    value = Arrays.toString(array);
                } else if (value instanceof Collection<?>) {
                    Collection<?> collection = (Collection<?>) value;
                    value = collection.toString();
                }
                params.put(prefix + key, value);
            }
        }

        String requserUrl = esBaseUrl + "/getRiskPropertyInfoByParam";
        log.info("理赔调用ES,标的查询URL: {}", requserUrl);
        log.info("理赔调用ES,标的查询params: {}", params);


        String result = null;
        List<PolicyRiskSubPropDTO> riskSubPropList = new ArrayList<>();
        if (switchMesh) {
//            Map<String, String> headers = new HashMap<>();
//            headers.put("Content-Type", "application/json;charset:UTF-8");
            result = MeshSendUtils.post(requserUrl, JsonUtils.toJsonString(params));
        } else {
//            Map<String, String> headers = new HashMap<>();
//            headers.put("Content-Type", "application/json;charset=UTF-8");
            result = HttpClientUtil.doPost(requserUrl, JsonUtils.toJsonString(params));
        }

        if (StringUtils.isEmpty(result)) {
            throw new GlobalBusinessException("理赔调用ES标的查询异常!");

        }
        log.info("理赔调用ES, result: {}", result);
        JSONObject resultJson = JSON.parseObject(result);
        if ("000000".equals(resultJson.getString("responseCode"))) {
            JSONObject data = resultJson.getJSONObject("data");
            if(null == data){
                log.info("理赔调用ES, result: {}", JSON.toJSON(riskSubPropList));
                return riskSubPropList;
            }
            if(BaseConstant.TARGET_TYPE_EMPLOYER.equals(riskPropQueryVO.getRiskGroupType())){
                JSONArray dataArr = data.getJSONArray("trackPlyRiskPropsubGroups");
                if(null == dataArr){
                    log.info("理赔调用ES, result: {}", JSON.toJSON(riskSubPropList));
                    return riskSubPropList;
                }
                for (int i = 0; i < dataArr.size() ; i++) {
                    JSONObject dataObject = dataArr.getJSONObject(i);
                    PolicyRiskSubPropDTO policyRiskSubPropDTO = new PolicyRiskSubPropDTO();
                    policyRiskSubPropDTO.setPolicyNo(riskPropQueryVO.getPolicyNo());
                    policyRiskSubPropDTO.setRiskGroupNo(riskPropQueryVO.getRiskGroupNo());
                    policyRiskSubPropDTO.setRiskGroupType(riskPropQueryVO.getRiskGroupType());
                    policyRiskSubPropDTO.setRiskDetail(dataObject.getString("subjectDetail"));
                    Map<String, Object> subjectDetailMap = dataObject.getJSONObject("subjectDetailMap").getInnerMap().entrySet().stream()
                            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                    subjectDetailMap.put("idPlyRiskProperty", dataObject.getString("idPlyRiskProperty"));
                    String insuranceBeginDate = dataObject.getString("insuranceBeginDate");
                    int index = insuranceBeginDate.indexOf("T");
                    if(index != -1){
                        subjectDetailMap.put("insuranceBeginDate", insuranceBeginDate.substring(0, index));
                    }
                    Set<String> keySet = subjectDetailMap.keySet();
                    for (String key : keySet) {
                        Object value = subjectDetailMap.get(key);
                        if(value instanceof Boolean){
                            value = value.toString();
                            subjectDetailMap.put(key, value);
                        }
                    }
                    policyRiskSubPropDTO.setRiskPropertyMap(subjectDetailMap);
                    riskSubPropList.add(policyRiskSubPropDTO);
                }
            }else {
                JSONArray dataArr = data.getJSONArray("trackPlyRiskProperties");
                if(null == dataArr){
                    log.info("理赔调用ES, result: {}", JSON.toJSON(riskSubPropList));
                    return riskSubPropList;
                }
                for (int i = 0; i < dataArr.size() ; i++) {
                    JSONObject dataObject = dataArr.getJSONObject(i);
                    String riskGroupType = dataObject.getString("riskGroupType");
                    if(!riskGroupType.equals(riskPropQueryVO.getRiskGroupType())){
                        continue;
                    }
                    PolicyRiskSubPropDTO policyRiskSubPropDTO = new PolicyRiskSubPropDTO();
                    policyRiskSubPropDTO.setPolicyNo(riskPropQueryVO.getPolicyNo());
//                policyRiskSubPropDTO.setRiskGroupId(dataObject.getString("riskGroupId"));
//                policyRiskSubPropDTO.setIdPlyRiskGroup(dataObject.getString("riskGroupId"));
//                policyRiskSubPropDTO.setRiskGroupCode(dataObject.getString("riskGroupCode"));
                    policyRiskSubPropDTO.setRiskGroupNo(riskPropQueryVO.getRiskGroupNo());
//                policyRiskSubPropDTO.setRiskGroupName(dataObject.getString("riskGroupName"));
                    policyRiskSubPropDTO.setRiskGroupType(riskPropQueryVO.getRiskGroupType());
                    policyRiskSubPropDTO.setRiskDetail(dataObject.getString("riskDetail"));
                    Map<String, Object> riskDetailMap = dataObject.getJSONObject("riskDetailMap").getInnerMap();
                    Set<String> keySet = riskDetailMap.keySet();
                    for (String key : keySet) {
                        Object value = riskDetailMap.get(key);
                        if(value instanceof Boolean){
                            value = value.toString();
                            riskDetailMap.put(key, value);
                        }
                    }
                    policyRiskSubPropDTO.setRiskPropertyMap(riskDetailMap);
                    riskSubPropList.add(policyRiskSubPropDTO);

//                    if(BaseConstant.TARGET_TYPE_EMPLOYER.equals(policyRiskSubPropDTO.getRiskGroupType())){
//                        // 雇主责任险目前需要自己查询封装
//                        PolicyQueryVO queryVO = new PolicyQueryVO();
//                        queryVO.setPolicyNo(policyRiskSubPropDTO.getPolicyNo());
//                        // 查询标的表 雇主这里应该只有一条应该不会多次查询
//                        List<PolicyRiskSubPropDTO> tempRiskSubPropList = policyMapper.getPolicyRiskSubPropList(queryVO);
//                        if(CollectionUtils.isNotEmpty(tempRiskSubPropList)){
//                            for (PolicyRiskSubPropDTO riskSubPropDTO : tempRiskSubPropList) {
//                                riskSubPropDTO.setRiskGroupId(policyRiskSubPropDTO.getRiskGroupId());
//                                riskSubPropDTO.setIdPlyRiskGroup(policyRiskSubPropDTO.getIdPlyRiskGroup());
//                                riskSubPropDTO.setRiskGroupNo(policyRiskSubPropDTO.getRiskGroupNo());
//                                riskSubPropDTO.setRiskGroupCode(policyRiskSubPropDTO.getRiskGroupCode());
//                                riskSubPropDTO.setRiskGroupName(policyRiskSubPropDTO.getRiskGroupName());
//                                riskSubPropDTO.setRiskGroupType(policyRiskSubPropDTO.getRiskGroupType());
//                                Map<String,Object> riskPropMap = new HashMap<>();
//                                riskPropMap.put("name",riskSubPropDTO.getName());
//                                riskPropMap.put("certificateType",riskSubPropDTO.getCertificateType());
//                                riskPropMap.put("certificateNo",riskSubPropDTO.getCertificateNo());
//                                riskPropMap.put("birthday",riskSubPropDTO.getBirthday());
//                                riskPropMap.put("sex",riskSubPropDTO.getSex());
//                                riskPropMap.put("age",riskSubPropDTO.getAge());
//                                riskSubPropDTO.setRiskPropertyMap(riskPropMap);
//                                riskSubPropDTO.setRiskDetail(JSON.toJSONString(riskPropMap));
//                            }
//                            riskSubPropList.addAll(tempRiskSubPropList);
//                        }
//
//                    } else {
//                        policyRiskSubPropDTO.setRiskDetail(dataObject.getString("riskDetail"));
//                        policyRiskSubPropDTO.setRiskPropertyMap(dataObject.getJSONObject("riskPropertyMap").getInnerMap());
//                        riskSubPropList.add(policyRiskSubPropDTO);
//                    }
                }
            }

        }
//        if(StringUtils.isNotBlank(riskPropQueryVO.getRiskGroupNo())){
//            // 存在标的方案号时进行过滤
//            riskSubPropList = riskSubPropList.stream().filter(item -> Objects.equals(item.getRiskGroupNo(), riskPropQueryVO.getRiskGroupNo())).collect(Collectors.toList());
//
//        }
//        log.info("理赔调用ES, 方案号进行过滤结果: {}", JSON.toJSON(riskSubPropList));
//
//        if(!CollectionUtil.isEmpty(paramMap)){
//            Set<String> keySet = paramMap.keySet();
//            for (String key : keySet) {
//                Object valueObj = paramMap.get(key);
//                if(Objects.nonNull(valueObj)) {
//                    String value = valueObj.toString();
//                    if (!"".equals(value)) {
//                        riskSubPropList = filterListByField(riskSubPropList, key, value);
//                    }
//                }
//            }
//        }

        log.info("理赔调用ES, result: {}", JSON.toJSON(riskSubPropList));
        return riskSubPropList;
    }

    private List<PolicyRiskSubPropDTO> filterListByField(List<PolicyRiskSubPropDTO> riskSubPropList, String fieldName, String value) {
        List<PolicyRiskSubPropDTO> result = new ArrayList<>();
        try {
            for (PolicyRiskSubPropDTO object : riskSubPropList) {
                Object fieldValue = object.getRiskPropertyMap().get(fieldName);
                if(Objects.isNull(fieldValue)) {
                    continue;
                }
                String fieldValueStr = fieldValue.toString();
                if ("".equals(fieldValueStr)) {
                    continue;
                }
                if (isMatch(fieldValueStr, value)) {
                    result.add(object);
                }
            }
        } catch (Exception e) {
            log.error("查询标的es结果过滤异常,field:{},value:{}", fieldName, value,e);
        }
        return result;
    }

    private static boolean isMatch(String fieldValue, String value) {
        return fieldValue.contains(value);
    }

    @Override
    public List<RiskPropertyParamDTO> getRiskPropertyParamByPolicyNo(String policyNo) {
        return ocasMapper.getRiskPropertyParamByPolicyNo(policyNo);
    }

    @Override
    public List<MiniOrderInfo> getPoliyMinOrderList(PolicyRiskQueryVO riskPropQueryVO) {
        String policyNo = riskPropQueryVO.getPolicyNo();
        PolicyNoNbsQueryDTO queryDTO = new PolicyNoNbsQueryDTO();
        queryDTO.setPolicyNo(policyNo);
        if(MapUtils.isNotEmpty(riskPropQueryVO.getParamMap())) {
            queryDTO.setMiniOrderNo((String) riskPropQueryVO.getParamMap().get("miniOrderNo"));
            queryDTO.setServiceName((String) riskPropQueryVO.getParamMap().get("insuranceName"));
        }

        if(riskPropQueryVO.getParamMap().get("searchType") != null && "all".equals(riskPropQueryVO.getParamMap().get("searchType"))) {
            // 抄单画面个人凭证 分页
            queryDTO.setPageSize(riskPropQueryVO.getPager().getPageRows());
            queryDTO.setPageNum(riskPropQueryVO.getPager().getPageIndex());
        }else {
            queryDTO.setPageSize(9999999);
            queryDTO.setPageNum(1);
        }


        String result = nbsRequest.getPoliyMinOrderList(queryDTO);
        Map resultMap = JSON.parseObject(result, Map.class);
        if ("000000".equals(resultMap.get("responseCode")) && Objects.nonNull(resultMap.get("data"))) {
            PageQueryMiniOrderVO pageQueryMiniOrderVO = JSON.parseObject(resultMap.get("data").toString(), PageQueryMiniOrderVO.class);
            if (ListUtils.isNotEmpty(pageQueryMiniOrderVO.getRecords())) {
                return pageQueryMiniOrderVO.getRecords();
            } else {
                return ListUtil.empty();
            }
        }
        return ListUtil.empty();
    }

    @Override
    public String getRiskGroupType(String policyNo) {
        return ocasMapper.getTargetType(policyNo);
    }
}
