package com.paic.ncbs.claim.service.report.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.CommonConstant;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.constant.ReportConstant;
import com.paic.ncbs.claim.common.enums.*;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.RapeCheckUtil;
import com.paic.ncbs.claim.common.util.RapeStringUtils;
import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.ahcs.AdressSearchDto;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.report.*;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.checkloss.PersonAccidentMapper;
import com.paic.ncbs.claim.dao.mapper.report.BatchReportTempEntityMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.duty.PersonAccidentDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemDTO;
import com.paic.ncbs.claim.model.dto.report.*;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO;
import com.paic.ncbs.claim.model.vo.report.ReportInfoForSX;
import com.paic.ncbs.claim.model.vo.report.ReportQueryVO;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.endcase.WholeCaseBaseService;
import com.paic.ncbs.claim.service.other.CommonParameterService;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import com.paic.ncbs.claim.service.report.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

import static com.paic.ncbs.claim.model.dto.duty.DutyPayDTO.nvl;

@Service
public class ReportInfoServiceImpl extends BaseServiceImpl<ReportInfoEntity> implements ReportInfoService {

    @Autowired
    private WholeCaseBaseService wholeCaseBaseService;

    @Autowired
    private ReportAccidentService reportAccidentService;

    @Autowired
    private CaseProcessService caseProcessService;

    @Autowired
    private PaymentItemService paymentItemService;

    @Autowired
    private ReportAccidentExService reportAccidentExService;

    @Autowired
    private ReportInfoExService reportInfoExService;

    @Autowired
    private ReportInfoMapper reportInfoMapper;
    @Autowired
    private PersonAccidentMapper personAccidentMapper;

    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;

    @Autowired
    private LinkManService linkManService;

    @Autowired
    private ReportCustomerInfoService reportCustomerInfoService;

    @Autowired
    private BatchReportTempEntityMapper batchReportTempEntityMapper;

    @Autowired
    private CommonParameterService commonParameterService;

    @Override
    public BaseDao<ReportInfoEntity> getDao() {
        return reportInfoMapper;
    }

    @Override
    public ReportInfoEntity getReportInfo(String reportNo) {
        return reportInfoMapper.getReportInfo(reportNo);
    }

    @Override
    public ReportBaseInfoResData requestReportBaseInfo(String reportNo) {
        ReportAhcsBaseInfoDTO dto = this.getReportBaseInfo(reportNo);
        //参数转换
        ReportBaseInfoResData data = new ReportBaseInfoResData();
        BeanUtils.copyProperties(dto, data);

        //名称变化
        data.setOverseasOccur(dto.getOverseasOccur());
        data.setAccidentProvince(dto.getProvinceCode());
        data.setAccidentCity(dto.getAccidentCityCode());
        data.setAccidentCounty(dto.getAccidentCountyCode());

        if (dto.getBirthday()!=null){
            data.setBirthday(DateUtils.dateFormat(dto.getBirthday(),DateUtils.SIMPLE_DATE_STR));
        }
        return data;
    }

    @Override
    public ReportAhcsBaseInfoDTO getReportBaseInfo(String reportNo) {
        ReportAhcsBaseInfoDTO dto = new ReportAhcsBaseInfoDTO();
        //获取 联系人 列表
        List<LinkManEntity> linkMans = linkManService.getLinkMans(reportNo,null);
        //获取 报案信息扩展 列表
        List<ReportInfoExEntity> reportInfoEx = reportInfoExService.getReportInfoEx(reportNo);
        //查询报案信息表
        ReportInfoEntity reportInfo = this.getReportInfo(reportNo);
        if(Objects.isNull(reportInfo)){
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, null, "报案号传值错误");
        }
        //报案二级来源
        String reportSubModeName = ReportSubModeEnum.getName(reportInfo.getReportSubMode());
        //报案方式
        reportInfo.setReportMode(ReportModeEnum.getName(StringUtils.isEmpty(reportInfo.getReportMode()) ? "2" :reportInfo.getReportMode()));
        //查询 事故信息表
        ReportAccidentEntity reportAccident = reportAccidentService.getReportAccident(reportNo);
        //查询 意键险报案信息扩展表
        ReportAccidentExEntity reportAccidentEx = reportAccidentExService.getReportAccidentEx(reportNo);
        //查询 整案信息表
        List<WholeCaseBaseEntity> wholeCaseBaseEx = wholeCaseBaseService.getWholeCaseBase(reportNo);
        //查询 报案客户信息表
        ReportCustomerInfoEntity customerInfo = reportCustomerInfoService.getReportCustomerInfoByReportNo(reportNo);
        dto.setRiskFlag(reportInfo.getRiskFlag());
        if(Objects.equals("Y",reportInfo.getRiskFlag())){
            dto.setRiskRemark(CaseRiskTypeEnums.getName(reportInfo.getRiskRemark()));
        }
        dto.setRemarks(new ArrayList<>());
        if (customerInfo != null) {
            dto.setName(customerInfo.getName());
            dto.setCertificateNo(customerInfo.getCertificateNo());
            dto.setCertificateType(customerInfo.getCertificateType());
            dto.setBirthday(customerInfo.getBirthday());
            dto.setPersonnelAttribute(customerInfo.getClientCluster());
        }

        if (RapeCheckUtil.isListNotEmpty(reportInfoEx)) {
            String reportRemark = reportInfoEx.get(CommonConstant.ZERO).getReportRemark();
            String extendFiled = reportInfoEx.get(CommonConstant.ZERO).getReportExtend();
            Map map;
            try{
                if(StringUtils.isNotEmpty(extendFiled)){
                    map = JSON.parseObject(extendFiled);
                    map.get("appendRemark");
                    if(map != null && StringUtils.isNotEmpty((String) map.get("appendRemark"))){
                        reportRemark = reportRemark + "<br>" + map.get("appendRemark");
                    }
                }
            } catch (Exception e){
                LogUtil.error("拼接系统备注字段异常Exception：{}",e);
            }

            if(reportRemark == null){
                reportRemark = "";
            }

            dto.setReportRemark(reportRemark);
            dto.setRelationWithReport(reportInfoEx.get(CommonConstant.ZERO).getRelationWithReporter());
            dto.setPartnerCode(reportInfoEx.get(CommonConstant.ZERO).getPartnerCode());
            dto.setRelationWithReportName(ApplicantTypeEnum.getName(dto.getRelationWithReport()));
            dto.setIsRepeatReport(reportInfoEx.get(CommonConstant.ZERO).getIsRepeatReport());
            dto.setLossClass(reportInfoEx.get(CommonConstant.ZERO).getCaseClass());
        }
        if (RapeCheckUtil.isListNotEmpty(linkMans)) {
            List<LinkManDTO> linkManDTOs = dto.getLinkManList();
            for (LinkManEntity l : linkMans) {
                LinkManDTO linkman = new LinkManDTO();
                linkman.setLinkManName(l.getLinkManName());
                linkman.setLinkManRelation(l.getLinkManRelation());
                linkman.setLinkManRelationName(RelationType.getName(l.getLinkManRelation()));
                linkman.setApplicantPerson(l.getApplicantPerson());
                linkman.setApplicantType(l.getApplicantType());
                linkman.setCertificateType(l.getCertificateType());
                linkman.setCertificateNo(l.getCertificateNo());
                linkman.setLinkManTelephone(l.getLinkManTelephone());
                linkman.setSendMessage(l.getSendMessage());
                linkman.setIsReport(l.getIsReport());
                linkManDTOs.add(linkman);
            }
            dto.setLinkManList(linkManDTOs);
        }
        dto.setReportNo(reportNo);
        dto.setReporterName(reportInfo.getReporterName());
        dto.setReportDate(reportInfo.getReportDate());
        dto.setReporterRegisterTel(reportInfo.getReporterRegisterTel());
        dto.setReporterCallNo(reportInfo.getReporterCallNo());

        String reportMode = reportInfo.getReportMode();
        dto.setReportMode(reportMode);

        dto.setReportModeName(StringUtils.isEmpty(reportSubModeName) || reportMode.equals(reportSubModeName) ? reportMode : reportMode+"-"+reportSubModeName);
        if(RapeCheckUtil.isListNotEmpty(reportInfoEx)&& BaseConstant.UPPER_CASE_Y.equals(reportInfoEx.get(CommonConstant.ZERO).getIsSpecialReport())){
            dto.setReportModeName("柜面-特殊");
        }
        dto.setReportregisterUm(reportInfo.getReportRegisterUm());
        dto.setRemark(reportInfo.getRemark());
        dto.setReportType(reportInfo.getReportType());
        if (reportAccident != null) {
            dto.setAccidentDate(reportAccident.getAccidentDate());
            dto.setAccidentDetail(reportAccident.getAccidentDetail());
            dto.setAccidentPlace(reportAccident.getAccidentPlace());
            //事故详细地址
            dto.setProvinceCode(reportAccident.getProvinceCode());
            dto.setAccidentCityCode(reportAccident.getAccidentCityCode());
            dto.setAccidentCountyCode(reportAccident.getAccidentCountyCode());
            //境外
            dto.setOverseasOccur(reportAccident.getOverseasOccur());
            dto.setAccidentArea(reportAccident.getAccidentArea());
            dto.setAccidentNation(reportAccident.getOverseaNationCode());

            //设置出险原因大类和细类
            dto.setAccidentCauseLevel1(AccidentReasonTypeEnum.getName(reportAccident.getAccidentCauseLevel1()));
            //出险原因细类
            dto.setAccidentCauseLevel2(AccidentReasonDetailTypeEnum.getName(reportAccident.getAccidentCauseLevel2()));
            dto.setAccidentCauseLevel1Code(reportAccident.getAccidentCauseLevel1());
            dto.setAccidentCauseLevel2Code(reportAccident.getAccidentCauseLevel2());
        }
        if (reportAccidentEx != null) {
            String insuredApplyStatusCode = RapeCheckUtil.isBlank(reportAccidentEx.getInsuredApplyStatus()) ? "" : reportAccidentEx.getInsuredApplyStatus();
            String MedicalStatus = TreatCondition.getName(reportAccidentEx.getMedicalStatus());
            String DiedStatus = DiedStatusEnum.getName(reportAccidentEx.getDiedStatus());
            String accidentType = InsuredApplyStatus.getName(insuredApplyStatusCode);

            String accidentExtendInfo = reportAccidentEx.getAccidentExtendInfo();
            if(StringUtils.isNotEmpty(accidentExtendInfo)){
                JSONObject accidentExtendInfoJson = Optional.ofNullable(JSON.parseObject(accidentExtendInfo)).orElse(new JSONObject());
                String isDirectCompensation = accidentExtendInfoJson.getString("isDirectCompensation");
                if(StringUtils.isNotEmpty(isDirectCompensation)){
                    dto.setIsDirectCompensation(isDirectCompensation);
                }else{
                    dto.setIsDirectCompensation("N");
                }
            }

            if (RapeStringUtils.isNotEmpty(accidentType)) {
                if ((ReportConstant.INSURED_APPLY_STATUS_MEDICAL_TREATMENT.equals(insuredApplyStatusCode)
                        || ReportConstant.INSURED_APPLY_STATUS_CRITICAL_DISEASES.equals(insuredApplyStatusCode))) {
                    if (RapeStringUtils.isNotEmpty(MedicalStatus)) {
                        dto.setAccidentTypeName(accidentType + "-" + MedicalStatus);
                    } else {
                        dto.setAccidentTypeName("人伤"+"-"+accidentType);
                    }
                }else if (ReportConstant.INSURED_APPLY_STATUS_DIED.equals(insuredApplyStatusCode)) {
                    if (RapeStringUtils.isNotEmpty(DiedStatus)) {
                        dto.setAccidentTypeName(accidentType + "-" + DiedStatus);
                    } else {
                        dto.setAccidentTypeName("人伤"+"-"+accidentType);
                    }
                }else if (ReportConstant.INSURED_APPLY_STATUS_DISAPPEAR.equals(insuredApplyStatusCode)){
                    dto.setAccidentTypeName("人伤"+"-"+accidentType);
                }else{
                    dto.setAccidentTypeName("人伤"+"-"+accidentType);
                }
            }


            if (RapeCheckUtil.isListNotEmpty(reportInfoEx)
                    && ReportConstant.NOT_HURT.equals(reportInfoEx.get(CommonConstant.ZERO).getCaseClass())) {
                if (RapeStringUtils.isNotEmpty(reportAccidentEx.getAccidentStatusDetails())) {
                    dto.setAccidentTypeName(reportAccidentEx.getAccidentStatusDetails());
                } else {
                    String[] a = insuredApplyStatusCode.split("\\|");
                    accidentType = InsuredApplyStatus.getName(a[0]);
                    for(int i = 1 ; i<a.length ; i++){
                        accidentType = accidentType+";"+InsuredApplyStatus.getName(a[i]);
                    }
                    dto.setAccidentTypeName(accidentType);
                }
            }
            if(StringUtils.isEmpty(dto.getAccidentTypeName())){
                dto.setAccidentTypeName(reportAccidentEx.getAccidentStatusDetails());
            }
        }
        if (RapeCheckUtil.isNotEmpty(wholeCaseBaseEx)) {
            String casType = wholeCaseBaseEx.get(0).getCaseType();
            if ("05".equals(casType)) {
                dto.setIsSelfHelp(CommonConstant.YES);
            } else if ("03".equals(casType)) {

                dto.setIsStandard(CommonConstant.YES);
            } else {
                dto.setIsSelfHelp(CommonConstant.NO);
            }
        }

        if (ReportConstant.REPORT_MODE_COUNTER.equals(dto.getReportMode())
                && checkIsBatchReport(dto.getReportNo())) {
            dto.setShowRepeatInfo(false);
        } else {
            dto.setShowRepeatInfo(true);
            List<String> repeatReportNoList = null;
            repeatReportNoList = selectRepeatReportNoListNew(dto.getCertificateNo(),dto.getName(),dto.getReportNo(), dto.getReportDate(), dto.getAccidentDate());
            dto.setRepeatReportNoList(repeatReportNoList);
        }
        return dto;
    }
    private List<String> selectRepeatReportNoListNew(String certificateNo,String name, String reportNo, Date reportDate, Date accidentDate) {

        List<String> reportNoList = reportInfoMapper.getHistoryCasebyClientNoBetweenDateNew(certificateNo, name,reportNo, reportDate, accidentDate);
        if (RapeCheckUtil.isNotEmpty(reportNoList)) {
            reportNoList.remove(reportNo);
        }
        return reportNoList;
    }

    private boolean checkIsBatchReport(String reportNo) {
        boolean isBatchReport = false;
        BatchReportTempEntity tempEntity = batchReportTempEntityMapper.selectByReportNo(reportNo);
        if (!RapeCheckUtil.isEmpty(tempEntity)) {
            isBatchReport = true;
        }
        return isBatchReport;
    }


    @Override
    public List<HistoryCaseDTO> getHistoryCaseByReportNo(String reportNo, String specialCaseType) {
        return reportInfoMapper.getHistoryCaseByReportNo(reportNo, specialCaseType);
    }

    @Override
    public List<HistoryCaseDTO> getHistoryCaseByReportNoFilter(String reportNo, String specialCaseType) {
        return reportInfoMapper.getHistoryCaseByReportNoFilter(reportNo, specialCaseType);
    }

    @Override
    public List<HistoryCaseDTO> getHistoryCaseByCertificateNo(String certificateNo, String name) {
        return reportInfoMapper.getHistoryCaseByCertificateNo(certificateNo,name);
    }

    @Override
    public List<HistoryCaseDTO> getHistoryCaseBetweenTime(String certificateNo, String name,String certificateType, String beginTime, String endTime) {
        return reportInfoMapper.getHistoryCaseBetweenTime(certificateNo, name,certificateType, beginTime, endTime);
    }

    @Override
    public List<HistoryCaseDTO> getHistoryReportByCertificateNo(String certificateNo, String specialCaseType) {
        return reportInfoMapper.getHistoryReportByCertificateNo(certificateNo, specialCaseType);
    }

    @Override
    public List<HistoryCaseDTO> getHistoryReportByPolicyNoAndName(String policyNo, String name, String specialCaseType) {
        return reportInfoMapper.getHistoryReportByPolicyNoAndName(policyNo, name, specialCaseType);
    }

    @Override
    public List<HistoryCaseDTO> getHistoryReportByBirthdayAndName(String birthday, String name) {
        return reportInfoMapper.getHistoryReportByBirthdayAndName(birthday, name);
    }

    @Override
    public List<HistoryCaseDTO> getHistoryReportByTelephoneNo(String telephoneNo) {
        return reportInfoMapper.getHistoryReportByTelephoneNo(telephoneNo);
    }

    @Override
    public List<HistoryCaseDTO> getHistoryReportByReportDateAndName(HistoryReportAgrsDTO dto) {
        return reportInfoMapper.getHistoryReportByReportDateAndName(dto);
    }

    @Override
    public List<HistoryCaseDTO> getHistoryReportByDateAndDepartmentCode(HistoryReportAgrsDTO dto) {
        return reportInfoMapper.getHistoryReportByDateAndDepartmentCode(dto);
    }

    @Override
    public List<HistoryCaseDTO> getHistoryCaseByReportBatchNo(String reportBatchNo) {
        return reportInfoMapper.getHistoryCaseByReportBatchNo(reportBatchNo);
    }

    @Override
    public List<HistoryCaseDTO> getHistoryReportByElectronicNoNew(String electronicNo) {
        return reportInfoMapper.getHistoryReportByElectronicNoNew(electronicNo);
    }

    @Override
    public List<HistoryCaseDTO> getHistoryReportFilter(HistoryReportAgrsDTO dto) {
        return reportInfoMapper.getHistoryReportFilter(dto);
    }

    @Override
    public List<HistoryCaseDTO> getHistoryReport(HistoryReportAgrsDTO dto) {
        return reportInfoMapper.getHistoryReport(dto);
    }

    @Override
    public List<HistoryCaseDTO> getHistoryByPolicyNos(List<ReportCustomerInfoEntity> customerInfo, String policyNo) {
        return reportInfoMapper.getHistoryByPolicyNos(customerInfo,policyNo);
    }

    @Override
    public List<HistoryCaseDTO> getHistoryCaseNew(WholeCaseVO wholeCase) {
        return reportInfoMapper.getHistoryCaseNew(wholeCase);
    }

    @Override
    public List<HistoryCaseDTO> getHistoryCaseNewByCopy(WholeCaseVO wholeCase) {
        return reportInfoMapper.getHistoryCaseNewByCopy(wholeCase);
    }

    @Override
    public List<ReportInfoForSX> getReportNoInfoForSX(String reportNo) {
        List<ReportInfoForSX> reportInfoForSXES = new ArrayList<>();

        List<String> policyNoByReportNo = ahcsPolicyInfoMapper.getPolicyNoByReportNo(reportNo);
        List<CaseProcessDTO> caseByReportNo = caseProcessService.getCaseByReportNo(reportNo);
        BigDecimal lastPay = BigDecimal.ZERO;
        for (int i = 0; i < caseByReportNo.size(); i++) {
            CaseProcessDTO caseProcessDTO = caseByReportNo.get(i);
            ReportInfoForSX reportInfoForSX = new ReportInfoForSX();
            ReportAhcsBaseInfoDTO reportBaseInfo = this.getReportBaseInfo(reportNo);
            reportInfoForSX.setReportDate(reportBaseInfo.getReportDate());
            reportInfoForSX.setReporterName(reportBaseInfo.getReporterName());
            reportInfoForSX.setAccidentDate(reportBaseInfo.getAccidentDate());
            reportInfoForSX.setReportStatus(CaseProcessStatus.getName(caseProcessDTO.getProcessStatus()));
            reportInfoForSX.setPolicys(String.join(",",policyNoByReportNo));
            Integer caseTimes = caseProcessDTO.getCaseTimes();
            reportInfoForSX.setReportNo(reportNo+"_"+caseTimes);
            PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
            paymentItemDTO.setReportNo(reportNo);
            paymentItemDTO.setCaseTimes(caseTimes);
            List<PaymentItemDTO> paymentItem = paymentItemService.getPaymentItemByReportNoAndPayType(paymentItemDTO);
            if (caseTimes>1){
                if (CollectionUtils.isEmpty(paymentItem)){
                    continue;
                }
                boolean allPay = paymentItem.stream().anyMatch(itemDTO -> !"80".equals(itemDTO.getPaymentItemStatus()));
                if (allPay){
                    continue;
                }
                BigDecimal sumPay = paymentItem.stream().map(p -> nvl(p.getPaymentAmount(), 0)).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal subtract = sumPay.subtract(lastPay);
                lastPay = sumPay;
                if (subtract.compareTo(BigDecimal.ZERO)>0){
                    reportInfoForSX.setPayStatus("已支付");
                    reportInfoForSX.setPayAmount(String.valueOf(subtract));
                    reportInfoForSX.setPayDate(paymentItem.get(0).getPayDate());
                } else {
                    continue;
                }
            } else {
                if (CollectionUtils.isEmpty(paymentItem)){
                    reportInfoForSX.setPayStatus("未支付");
                    reportInfoForSX.setPayAmount(null);
                    reportInfoForSX.setPayDate(null);
                }else {
                    boolean allPay = paymentItem.stream().anyMatch(dto -> !"80".equals(dto.getPaymentItemStatus()));
                    BigDecimal sumPay = paymentItem.stream().map(p -> nvl(p.getPaymentAmount(), 0)).reduce(BigDecimal.ZERO, BigDecimal::add);
                    lastPay = sumPay;
                    if (allPay){
                        reportInfoForSX.setPayStatus("未支付");
                        reportInfoForSX.setPayAmount(null);
                        reportInfoForSX.setPayDate(null);
                    }else {
                        reportInfoForSX.setPayStatus("已支付");
                        reportInfoForSX.setPayAmount(String.valueOf(sumPay));
                        reportInfoForSX.setPayDate(paymentItem.get(0).getPayDate());
                    }
                }
            }
            PersonAccidentDTO accidentDTO = personAccidentMapper.getPersonAccidentByReportNo(reportNo, null, caseTimes);
            if (accidentDTO != null) {
                String accidentPlace = getAccidentPlace(accidentDTO);
                reportInfoForSX.setAccidentPlace(accidentPlace);
            }
            reportInfoForSXES.add(reportInfoForSX);
        }
        return reportInfoForSXES;
    }

    @Override
    public String getAccidentPlace(PersonAccidentDTO accidentDTO) {
        String accidentPlace;
        AdressSearchDto adressSearchDto  = new AdressSearchDto();
        if ("0".equals(accidentDTO.getOverseasOccur())){
            adressSearchDto.setOverseasOccur(accidentDTO.getOverseasOccur()).setAccidentProvinceCode(accidentDTO.getProvinceCode())
                    .setAccidentCountyCode(accidentDTO.getAccidentCountyCode()).setAccidentCityCode(accidentDTO.getAccidentCityCode());
        }else {
            adressSearchDto.setOverseasOccur(accidentDTO.getOverseasOccur()).setAccidentAreaCode(accidentDTO.getAccidentArea())
                    .setAccidentNationCode(accidentDTO.getAccidentNation());
        }
        AdressSearchDto detailAdressFormCode = commonParameterService.getDetailAdressFormCode(adressSearchDto);

        if ("0".equals(accidentDTO.getOverseasOccur())){
            accidentPlace = detailAdressFormCode.getAccidentProvinceName() + detailAdressFormCode.getAccidentCityName()
                    + detailAdressFormCode.getAccidentCountyName() +accidentDTO.getAccidentPlace();
        }else{
            accidentPlace = detailAdressFormCode.getAccidentAreaName() + detailAdressFormCode.getAccidentNationName() +accidentDTO.getAccidentPlace();
        }
        return accidentPlace;
    }

	@Override
	public ReportStatDTO getReportStat(ReportQueryVO queryVO) {
		List<String> reportNoList = reportInfoMapper.getReportNoList(queryVO);
		return ReportStatDTO.builder().accidentDate(queryVO.getAccidentDate())
				.certificateType(queryVO.getCertificateType()).certificateNo(queryVO.getCertificateNo())
				.policyNo(queryVO.getPolicyNo()).reportNoList(reportNoList).build();
	}
}
