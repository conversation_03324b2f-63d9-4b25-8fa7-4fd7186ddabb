package com.paic.ncbs.claim.controller.who.investigate;

import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskAuditDTO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateAuditVO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateTaskAuditVO;
import com.paic.ncbs.claim.service.investigate.InvestigateTaskAuditService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@Api(tags = "任务审核")
@RestController
@RequestMapping(value = "/who/app/investigateTaskAuditAction")
public class InvestigateTaskAuditController extends BaseController {

    @Autowired
    private InvestigateTaskAuditService investigateTaskAuditService;


    @ApiOperation(value = "获取审核通过的任务信息")
    @GetMapping(value = "/getApproveInfoByIdAhcsInvestigateTask/{idAhcsInvestigateTask}")
    public ResponseResult<InvestigateAuditVO> getApproveInfoByIdAhcsInvestigateTask(@ApiParam("调查任务id") @PathVariable("idAhcsInvestigateTask") String idAhcsInvestigateTask) {
        return  ResponseResult.success(investigateTaskAuditService.getApproveInfoByIdAhcsInvestigateTask(idAhcsInvestigateTask));
    }


    @ApiOperation(value = "完成调查任务审核")
    @PostMapping(value = "/finishTaskAudit")
    public ResponseResult<Object> finishTaskAudit(@RequestBody InvestigateTaskAuditDTO taskAuditDTO) {
        try {
            LogUtil.audit("#调查·完成任务审核#入参#idAhcsInvestigateTaskAudit:{},IdAhcsInvestigateTask:{}", taskAuditDTO.getIdAhcsInvestigateTaskAudit(),taskAuditDTO.getIdAhcsInvestigateTask());
            investigateTaskAuditService.finishTaskAudit(taskAuditDTO, WebServletContext.getUserId());
            return ResponseResult.success();
        }catch (Exception e){
            return ResponseResult.fail(ErrorCode.SYSTEM_RUNNING_ERROR,"调查任务审批异常，请联系理赔系统开发人员");
        }

    }

    @ApiOperation(value = "获取主调查任务审核信息")
    @GetMapping(value = "/getInvestigateTaskAuditForReportByInvestigateId/{idAhcsInvestigate}")
    public ResponseResult<InvestigateTaskAuditVO> getInvestigateTaskAuditForReportByInvestigateId(@ApiParam("调查任务id")@PathVariable("idAhcsInvestigate") String idAhcsInvestigate) throws GlobalBusinessException {
        LogUtil.audit("#调查·根据调查id查最后一个主调查任务的审核信息#入参#idAhcsInvestigate="+ idAhcsInvestigate);
        return ResponseResult.success(investigateTaskAuditService.getInvestigateTaskAuditForReportByInvestigateId(idAhcsInvestigate));
    }
}