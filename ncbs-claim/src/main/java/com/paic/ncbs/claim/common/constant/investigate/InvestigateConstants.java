package com.paic.ncbs.claim.common.constant.investigate;

import java.util.HashMap;
import java.util.Map;


public class InvestigateConstants {


	private InvestigateConstants() {
		throw new IllegalStateException("Utility class");
	}
	

	public static final String VALIDATE_FLAG_YES = "Y";


	public static final String VALIDATE_FLAG_NO = "N";
	
	
   

	public static final String AHCS_INVESTIGATE_REPLY_ASSIST_TYPE = "OT_0101";


	public static final String AHCS_INVESTIGATE_GOON_DISPATCH_TYPE = "OT_0102";


	public static final String AHCS_INVESTIGATE_TASK_CHANGE_TYPE = "OT_0103";


	public static final String AHCS_INVESTIGATE_OFFSITE_ASSIST_TYPE = "OT_0104";

	



	public static final Integer AHCS_INVESTIGATE_STATUS_APPROVAL = 1;
	

	public static final Integer AHCS_INVESTIGATE_STATUS_PROCESSING = 2;
	

	public static final Integer AHCS_INVESTIGATE_STATUS_AUDIT = 3;
	

	public static final Integer AHCS_INVESTIGATE_STATUS_FINISH = 4;
	

	public static final Integer AHCS_INVESTIGATE_STATUS_BACK = 5;
  

	public static final Integer AHCS_INVESTIGATE_STATUS_EVALUATE = 6;
  
	


	public static final String AHCS_INVESTIGATE_TASK_STATUS_PROCESSING = "1";
	

	public static final String AHCS_INVESTIGATE_TASK_STATUS_APPROVAL = "2";
	

	public static final String AHCS_INVESTIGATE_TASK_STATUS_ASSIST = "3";
	

	public static final String AHCS_INVESTIGATE_TASK_STATUS_AUDIT = "4";
  

	public static final String AHCS_INVESTIGATE_TASK_STATUS_FINISH = "5";
	
	
	


	public static final String AHCS_INVESTIGATE_AUDIT_TYPE_OFFSITE = "01";
	

	public static final String AHCS_INVESTIGATE_AUDIT_TYPE_NORMAL = "02";
	
	


	public static final String AHCS_INVESTIGATE_AUDIT_OPINION_PASS = "1";
	

	public static final String AHCS_INVESTIGATE_AUDIT_OPINION_REJECT = "2";



	public static final String ID_AHCS_INVESTIGATE = "idAhcsInvestigate";
	public static final String DEPARTMENT_CODE = "departmentCode";
	public static final String INVESTIGATOR_UM = "investigatorUm";
	public static final String INVESTIGATOR_LIST = "invertigates";
	public static final String ID_AHCS_INVESTIGATE_TASK = "idAhcsInvestigateTask";
	public static final String IS_APPROVE = "isApprove";
	public static final String IS_EVALUATE = "isEvaluate";
	public static final String IS_OLD_INVESTIGATE = "isOldInvestigate";
	public static final String DISPATCH_UM = "dispatchUm";

	public static final String ID_AHCS_INVESTIGATE_AUDIT = "idAhcsInvestigateAudit";
	public static final String PASS_REVIEW = "passReview";
	public static final String IS_ACTIVE = "isActive";
	public static final String BPM_BOOLEAN_FALSE = "false";
	public static final String BPM_BOOLEAN_TRUE = "true";
	public static final String INVESTIGATE_AUDIT_OPINION_REJECT = "调查退回";
	public static final String INVESTIGATE_AUDIT_OPINION_OK = "分配调查";
	
	public static final String INVESTIGATE_INIT_MODE = "investigateType";
	public static final String INVESTIGATE_ASSIST_TYPE = "assistType";
	public static final String INVESTIGATE_NODE = "initiateInvestigateNode";
	

	public static final String INVESTIGATE_TYPE_MAJOR = "主调查";
	public static final String INVESTIGATE_TYPE_ASSIST = "辅助调查";
	public static final String INVESTIGATE_TYPE_OFFSITE = "异地调查";
	public static final String INVESTIGATE_TYPE_AUTO = "自动提调";
	public static final String INVESTIGATE_TYPE_PERSON = "人工提调";
	public static final String INVESTIGATE_TYPE_WHO_OFFSITE = "整案异地调查";
	
	


	public static final String AHCS_INVESTIGATE_INIT_MODE_AUTO = "01";
	

	public static final String AHCS_INVESTIGATE_INIT_MODE_PERSON = "02";

	public static final String OVERSEAS_OCCUR = "0";


	public static final String AHCS_INVESTIGATE_INIT_MODE_RISK = "03";
	
	
	
	

	

	public static final String INVESTIGATE_QUALITATIVE_NORMAL = "IQ_0501";
	

	public static final String INVESTIGATE_QUALITATIVE_REFERENCE = "IQ_0502";
	

	public static final String INVESTIGATE_QUALITATIVE_ABNORMAL = "IQ_0503";
	
	

	

	public static final float INVESTIGATE_AGING_SCORE = 10;
	

	

	public static final String AHCS_EVALUATE_STATUS_PASS = "1";
	

	public static final String AHCS_EVALUATE_STATUS_BACK = "2";
	

	public static final String AHCS_EVALUATE_STATUS_NONE = "3";
		

	public static final Map<String, String> INVESTIGATE_QUALITATIVE = new HashMap<>();
	static {
		INVESTIGATE_QUALITATIVE.put("正常件", INVESTIGATE_QUALITATIVE_NORMAL);
		INVESTIGATE_QUALITATIVE.put("参考件", INVESTIGATE_QUALITATIVE_REFERENCE);
		INVESTIGATE_QUALITATIVE.put("异常件", INVESTIGATE_QUALITATIVE_ABNORMAL);
	}

}