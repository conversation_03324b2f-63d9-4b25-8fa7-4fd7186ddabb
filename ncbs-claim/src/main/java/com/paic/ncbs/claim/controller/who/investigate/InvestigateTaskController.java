package com.paic.ncbs.claim.controller.who.investigate;

import com.paic.ncbs.base.exception.NcbsException;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.investigate.InvestigateConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateAuditMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskDTO;
import com.paic.ncbs.claim.model.vo.investigate.*;
import com.paic.ncbs.claim.service.investigate.InvestigateService;
import com.paic.ncbs.claim.service.investigate.InvestigateTaskService;
import com.paic.ncbs.claim.service.settle.CustomerService;
import com.paic.ncbs.claim.service.taskdeal.TaskPoolService;
import com.paic.ncbs.claim.service.user.UserInfoService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Api(tags = "调查任务")
@RestController
@RequestMapping(value = "/who/app/investigateTaskAction")
public class InvestigateTaskController extends BaseController {

    @Autowired
    private InvestigateTaskService investigateTaskService;

    @Autowired
    private InvestigateService investigateService;

    @Autowired
    private CustomerService customerService ;

    @Autowired
    private UserInfoService userInfoService ;

    @Autowired
    private DepartmentDefineMapper departmentDefineMapper ;

    @Autowired
    private TaskPoolService taskPoolService;

    @Autowired
    InvestigateAuditMapper investigateAuditMapper;
    @ApiOperation(value = "完成调查任务")
    @PostMapping(value = "/finishInvestigateTask")
    public ResponseResult<Object> finishInvestigateTask(@RequestBody InvestigateTaskDTO investigateTask) throws GlobalBusinessException {

        LogUtil.audit("#调查·完成调查任务#入参#investigateTask=" + investigateTask);

        String isStorage = investigateTask.getIsStorage();
        if ("N".equals(investigateTask.getIsHasAdjustingFee())){
            investigateTask.setCommonEstimateFee(null);
        }
        investigateService.modifyInvestigateForFee(investigateTask.getIdAhcsInvestigate(), investigateTask.getIsHasAdjustingFee(),investigateTask.getCommonEstimateFee());
        investigateTask.setInvestigatorUm(WebServletContext.getUserId());
        investigateTask.setInvestigatorUmName(WebServletContext.getUserName());
        investigateTask.setInvestigateDepartment(WebServletContext.getDepartmentCode());
        investigateTask.setInvestigateDepartmentName(departmentDefineMapper.queryDepartmentNameByDeptCode(WebServletContext.getDepartmentCode()));
        investigateTask.setUpdatedBy(WebServletContext.getUserId()); 
        InvestigateVO investigate = investigateService.getInvestigateById(investigateTask.getIdAhcsInvestigate());
        if(BaseConstant.STRING_02.equals(investigate.getInitMode())){
        	investigateTask.setTaskType(BaseConstant.STRING_2);
        }
        if (InvestigateConstants.VALIDATE_FLAG_YES.equals(isStorage)) {
              investigateTaskService.modifyInvestigateTask(investigateTask);
        } else {
            investigateTaskService.finishInvestigateTask(investigateTask, WebServletContext.getUserId());
        }
        return ResponseResult.success();
    }

    @ApiOperation(value = "任务流转信息查询")
    @GetMapping(value = "/getInvestigateTaskByInvestigateId/{idAhcsInvestigate}")
    public ResponseResult<List<InvestigateTaskVO>> getInvestigateTaskByInvestigateId(@ApiParam("调查ID") @PathVariable("idAhcsInvestigate") String idAhcsInvestigate) throws GlobalBusinessException {

        LogUtil.audit("#调查·根据调查表id查询任务流转信息#入参#idAhcsInvestigate=" + idAhcsInvestigate);

        return ResponseResult.success(investigateTaskService.getInvestigateTaskByInvestigateId(idAhcsInvestigate));
    }

    @ApiOperation(value = "任务信息查询")
    @GetMapping(value = "/getInvestigateTaskLinkedByTaskId/{idAhcsInvestigateTask}/{idAhcsInvestigate}")
    public ResponseResult<InvestigateTaskVO> getInvestigateTaskLinkedByTaskId(@ApiParam("调查任务ID") @PathVariable("idAhcsInvestigateTask") String idAhcsInvestigateTask,@ApiParam("调查id") @PathVariable("idAhcsInvestigate") String idAhcsInvestigate) {


        String majorTaskId = investigateTaskService.getMajorTaskIdByInvestigateId(idAhcsInvestigateTask);
        if (StringUtils.isNotEmpty(majorTaskId)) {
            idAhcsInvestigateTask = majorTaskId;
        }

        LogUtil.audit("#调查·根据任务id查任务信息#入参#idAhcsInvestigateTask=" + idAhcsInvestigateTask);
        InvestigateTaskVO vo = investigateTaskService.getInvestigateTaskLinkedByTaskId(idAhcsInvestigateTask);

        if (vo != null && vo.getInvestigateAssistVOs() != null && vo.getInvestigateAssistVOs().size() > 0) {
            for (InvestigateAssistVO assist : vo.getInvestigateAssistVOs()) {
                if (assist.getCreatedDate().equals(assist.getUpdatedDate())) {
                    assist.setUpdatedDate(null);
                }
            }
        }
        if(null != vo){
            InvestigateVO investigateById = investigateService.getInvestigateById(idAhcsInvestigate);
            if(null != investigateById ){
                vo.setCommonEstimateFee(investigateById.getCommonEstimateFee());
                vo.setIsHasAdjustingFee(investigateById.getIsHasAdjustingFee());
                //增加公估公司
                TpaServerInfoListVO tpaServerInfoList = (TpaServerInfoListVO) investigateService.getServerInfoList().getData();
                List<ServerInfoVO> serverInfoList = tpaServerInfoList.getServerInfoList();

                if(ListUtils.isNotEmpty(serverInfoList)) {
                    Map<String,String> serverInfoMap = serverInfoList.stream().collect(Collectors.toMap(ServerInfoVO::getServerCode, ServerInfoVO::getServerName));
                    if(StringUtils.isNotEmpty(investigateById.getServerCode())) {
                        vo.setCompanyName(serverInfoMap.get(investigateById.getServerCode()));
                    }
                }
            }
        }

        if (vo != null && vo.getInvestigateTaskAuditVOs() != null && vo.getInvestigateTaskAuditVOs().size() > 0) {
            List<InvestigateTaskAuditVO> temp = new ArrayList<>();
            for (InvestigateTaskAuditVO audit : vo.getInvestigateTaskAuditVOs()) {
                audit.setReviewUserName(StringUtils.isEmptyStr(audit.getReviewUserUm()) ? "":userInfoService.getUserNameById(audit.getReviewUserUm()));
                if (InvestigateConstants.AHCS_INVESTIGATE_AUDIT_OPINION_REJECT.equals(audit.getReviewOpinion())) {
                    temp.add(audit);
                }
                audit.setInitiatorName(userInfoService.getUserNameById(audit.getInitiatorUm()));
            }
            vo.setInvestigateTaskAuditVOs(temp);
        }
        ReportCustomerInfoEntity customerInfo = customerService.getReportCustomerInfoByReportNo(vo.getReportNo());
        vo.setCustomerName(customerInfo == null ? "":customerInfo.getName());
//        vo.setInvestigatorName( StringUtils.isEmptyStr(vo.getInvestigatorUm()) ? "":userInfoService.getUserNameById(vo.getInvestigatorUm()));
        vo.setInvestigatorName(vo.getInvestigatorUmName());
        return ResponseResult.success(vo);
    }

    @ApiOperation(value = "调查用户列表获取")
    @PostMapping(value = "/getInvestigateUserListByDepartment")
    public ResponseResult<List<UserInfoDTO>> getInvestigateUserListByDepartment(@RequestBody InvestigateTaskDTO investigateTask) throws GlobalBusinessException, NcbsException {
        TpaServerInfoListVO tpaServerInfoList = (TpaServerInfoListVO) investigateService.getServerInfoList().getData();
        List<ServerInfoVO> serverInfoList = tpaServerInfoList.getServerInfoList();
        List<String> serverCodeList = new ArrayList<>();
        if(ListUtils.isNotEmpty(serverInfoList)) {
            serverCodeList = serverInfoList.stream().map(ServerInfoVO::getServerCode).collect(Collectors.toList());
        }
        if (StringUtils.isEmptyStr(investigateTask.getInvestigateDepartment()) || serverCodeList.contains(investigateTask.getInvestigateDepartment())){
            investigateTask.setInvestigateDepartment(WebServletContext.getDepartmentCode());
        }

        //机构为总部1时，取所有机构用户
        List<String> childCodeList = new ArrayList<String>();
        childCodeList.add(investigateTask.getInvestigateDepartment());
        if("1".equals(investigateTask.getInvestigateDepartment())) {
            List<String> parentCodeList = new ArrayList<String>();
            parentCodeList.add("775");
            childCodeList.addAll(departmentDefineMapper.getChildCodeList(parentCodeList));

        }
        List<UserInfoDTO> userInfoDTO = new ArrayList<UserInfoDTO>();
        for(String investigateDepartment:childCodeList) {
            LogUtil.audit("#调查用户列表获取,机构=" + investigateDepartment);
            String departmentName = departmentDefineMapper.queryDepartmentNameByDeptCode(investigateDepartment);
            List<UserInfoDTO> departmentUserInfoDTO = taskPoolService.searchTaskDealUser(investigateDepartment, BpmConstants.OC_MAJOR_INVESTIGATE);
            departmentUserInfoDTO.forEach(e->{
                e.setComCode(investigateDepartment);
                e.setComName(departmentName);
            });
            userInfoDTO.addAll(departmentUserInfoDTO);
        }
//        {
//            "accountCode": null,
//                "userCode": "ficoxu",
//                "userName": "徐飞",
//                "comCode": "1",
//                "comName": "三星财险",
//                "comLevel": null,
//                "makeCom": null,
//                "makeComName": null,
//                "loginSystem": null,
//                "mobile": "***********",
//                "phone": "021-********",
//                "userType": "1",
//                "validStatus": "0",
//                "email": "<EMAIL>"
//        }
//        UserInfoDTO userInfoDTO1 = new UserInfoDTO();
//        userInfoDTO1.setUserName("徐飞");
//        userInfoDTO1.setUserCode("ficoxu");
//        userInfoDTO1.setComCode("1");
//        userInfoDTO1.setComName("三星财险");
//        userInfoDTO.add(userInfoDTO1);
//        LogUtil.audit("#调查用户列表获取,机构=" + investigateTask.getInvestigateDepartment());
//        String departmentName = departmentDefineMapper.queryDepartmentNameByDeptCode(investigateTask.getInvestigateDepartment());
//       //传的是权限机构，返回用户和归属机构
//        List<UserInfoDTO> userInfoDTO = taskPoolService.searchTaskDealUser(investigateTask.getInvestigateDepartment(), BpmConstants.OC_MAJOR_INVESTIGATE);
//        userInfoDTO.forEach(e->{
//            e.setComCode(investigateTask.getInvestigateDepartment());
//            e.setComName(departmentName);
//        });
        return ResponseResult.success(userInfoDTO);
    }



    @ApiOperation(value = "主调查任务查询")
    @GetMapping(value = "/getMajorInvestigateTaskLinkedByInvestigateId/{idAhcsInvestigate}")
    public ResponseResult<InvestigateTaskVOForReport> getMajorInvestigateTaskLinkedByInvestigateId(@ApiParam("调查ID") @PathVariable("idAhcsInvestigate") String idAhcsInvestigate) {

        LogUtil.audit("#调查·通过调查信息表id查询主调查任务#入参#idAhcsInvestigate=" + idAhcsInvestigate);

        return ResponseResult.success(investigateTaskService.getMajorInvestigateTaskLinkedByInvestigateId(idAhcsInvestigate));
    }

    @ApiOperation(value = "提调退回信息")
    @GetMapping(value = "/getInvestigateAuditLinkedByInvestigateId/{idAhcsInvestigate}")
    public ResponseResult<InvestigateTaskVO> getInvestigateAuditLinkedByInvestigateId(@ApiParam("调查id") @PathVariable("idAhcsInvestigate") String idAhcsInvestigate) {
        InvestigateTaskVO investigateTaskVO = new InvestigateTaskVO();
        InvestigateAuditVO investigateAuditVO = investigateAuditMapper.getInvestigateMajorAuditByInvestigateId(idAhcsInvestigate);
        if (null != investigateAuditVO) {
            investigateTaskVO.setInvestigateAuditVO(investigateAuditVO);
            investigateTaskVO.setCreatedDate(investigateAuditVO.getUpdatedDate());
            investigateTaskVO.setDispatchName(userInfoService.getUserNameById(investigateAuditVO.getAuditorUm()));
            investigateTaskVO.setDispatchUm(investigateAuditVO.getAuditorUm());
        }
        investigateTaskVO.setIdAhcsInvestigate(idAhcsInvestigate);
        return ResponseResult.success(investigateTaskVO);
    }
    
    
    @ApiOperation(value = "生成调查委托书链接")
    @GetMapping(value = "/generateInvestigatePdf/{idAhcsInvestigate}")
    public ResponseResult<Map<String,String>> generateInvestigatePdf(@ApiParam("调查ID") @PathVariable("idAhcsInvestigate") String idAhcsInvestigate) throws GlobalBusinessException {
        return ResponseResult.success(investigateTaskService.generateInvestigatePdf(idAhcsInvestigate));
    }
}