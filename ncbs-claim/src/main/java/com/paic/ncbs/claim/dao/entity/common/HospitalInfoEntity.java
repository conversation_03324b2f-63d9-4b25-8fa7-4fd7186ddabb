package com.paic.ncbs.claim.dao.entity.common;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 医院表
 */
@Data
@TableName(value = "hospital_info")
public class HospitalInfoEntity implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "ID_HOSPITAL_INFO")
    private String idHospitalInfo;

    /**
     * 医院电话
     */
    @TableField(value = "HOSPITAL_PHONE")
    private String hospitalPhone;

    /**
     * 所在国家编码
     */
    @TableField(value = "COUNTRY_CODE")
    private String countryCode;

    /**
     *  省级行政区（省/直辖市）代码
     */
    @TableField(value = "PROVINCE_CODE")
    private String provinceCode;

    /**
     * 地级市代码
     */
    @TableField(value = "PREFECTURE_LEVEL_CODE")
    private String prefectureLevelCode;

    /**
     * 县级行政区（区/县）代码
     */
    @TableField(value = "DISTRICT_CODE")
    private String districtCode;

    /**
     * 详细地址
     */
    @TableField(value = "ADDRESS_DESC")
    private String addressDesc;

    /**
     * 医院邮编
     */
    @TableField(value = "POST_CODE")
    private String postCode;

    /**
     * 传值号码
     */
    @TableField(value = "FAX_NUMBER")
    private String faxNumber;

    /**
     * 经营性质(Y-营利性，N-非营利性)
     */
    @TableField(value = "IS_PROFIT_MAKING")
    private String isProfitMaking;

    /**
     * 医院等级
     */
    @TableField(value = "HOSPITAL_GRADE")
    private String hospitalGrade;

    /**
     * 医院级别
     */
    @TableField(value = "HOSPITAL_LEVEL")
    private String hospitalLevel;

    /**
     * 医院类型
     */
    @TableField(value = "HOSPITAL_TYPE")
    private String hospitalType;

    /**
     * 是否医保定点医院
     */
    @TableField(value = "IS_MEDICARE_DESIGNATED")
    private String isMedicareDesignated;

    /**
     * 是否意健险定点医院
     */
    @TableField(value = "IS_HEALTH_DESIGNATED")
    private String isHealthDesignated;

    /**
     * 是否合作(Y-是 N-否)
     */
    @TableField(value = "IS_COOPERATE")
    private String isCooperate;

    /**
     * 开始合作时间
     */
    @TableField(value = "START_COOPERATE_TIME")
    private Date startCooperateTime;

    /**
     * 机构联系人
     */
    @TableField(value = "DEPARTMENT_LINK_MAN")
    private String departmentLinkMan;

    /**
     * 是否对接(Y-是 N-否)
     */
    @TableField(value = "IS_EXCHANGE")
    private String isExchange;

    /**
     * 外部系统医院ID
     */
    @TableField(value = "EXTERNAL_SYSTEM_HPL_ID")
    private String externalSystemHplId;

    /**
     * 医保方案
     */
    @TableField(value = "MEDICINE_PROJECT")
    private String medicineProject;

    /**
     * 是否有效,Y-有效,N-无效
     */
    @TableField(value = "IS_VALID")
    private String isValid;

    /**
     * 维护人
     */
    @TableField(value = "MAINTAIN_USER")
    private String maintainUser;

    /**
     * 维护时间
     */
    @TableField(value = "MAINTAIN_TIME")
    private Date maintainTime;

    /**
     * 对接平台
     */
    @TableField(value = "COOPERATION_PLATFORM")
    private String cooperationPlatform;

    /**
     * 是否开通直付Y/N
     */
    @TableField(value = "IS_SUPPORT_DIRECT_PAY")
    private String isSupportDirectPay;

    /**
     * 作战医院(Y-是 N-否)
     */
    @TableField(value = "IS_CAMPAIGN")
    private String isCampaign;

    /**
     * 医院的纬度,如:22.531633
     */
    @TableField(value = "LATIUDE_CODE")
    private String latiudeCode;

    /**
     * 医院的经度,如:114.058228
     */
    @TableField(value = "LONGITUDE_CODE")
    private String longitudeCode;

    /**
     * 是否爽快赔上线医院(Y-是 N-否)
     */
    @TableField(value = "IS_COOLPAY")
    private String isCoolpay;

    /**
     * 医院联系人姓名
     */
    @TableField(value = "CONTACTOR_NAME")
    private String contactorName;

    /**
     * 医院联系人电话
     */
    @TableField(value = "CONTACTOR_PHONE")
    private String contactorPhone;

    /**
     * 三级机构
     */
    @TableField(value = "DEPRTMENT_LEV_THREE")
    private String deprtmentLevThree;

    /**
     * 合作结束时间
     */
    @TableField(value = "END_COOPERATE_TIME")
    private Date endCooperateTime;

    /**
     * 是否为限价医院（Y-是;N-否）
     */
    @TableField(value = "IS_LIMITED_PRICE")
    private String isLimitedPrice;

    /**
     * 是否已划定网格区域（Y-是;N-否）
     */
    @TableField(value = "IS_DELIMITED_GRID_AREA")
    private String isDelimitedGridArea;

    /**
     * 依护对接标识（Y-是;N-否）
     */
    @TableField(value = "IS_YIHU_COOPERATED")
    private String isYihuCooperated;

    /**
     * 医护自动下单（Y-是;N-否）
     */
    @TableField(value = "IS_AUTOMATIC_ORDER")
    private String isAutomaticOrder;

    /**
     * 探视单位
     */
    @TableField(value = "VISIT_CORPORATION")
    private String visitCorporation;

    /**
     * 自动下单单位
     */
    @TableField(value = "AUTO_ORDER_CORPORATION")
    private String autoOrderCorporation;

    /**
     * 创建人员
     */
    @TableField(value = "CREATED_BY")
    private String createdBy;

    /**
     * 机构
     */
    @TableField(value = "DEPARTMENT_CODE")
    private String departmentCode;

    /**
     * 医院ID
     */
    @TableField(value = "HOSPITAL_CODE")
    private String hospitalCode;

    /**
     * 卫生许可证号
     */
    @TableField(value = "HYGIENE_LICENSE_NUMBER")
    private String hygieneLicenseNumber;

    /**
     * 医院名称
     */
    @TableField(value = "HOSPITAL_NAME")
    private String hospitalName;

    /**
     * 编码机构类型
     */
    @TableField(value = "org_type")
    private String orgType;


    /**
     * 创建时间
     */
    @TableField(value = "CREATED_DATE")
    private Date createdDate;

    /**
     * 修改人员
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "UPDATED_DATE")
    private Date updatedDate;

    private static final long serialVersionUID = 1L;
}