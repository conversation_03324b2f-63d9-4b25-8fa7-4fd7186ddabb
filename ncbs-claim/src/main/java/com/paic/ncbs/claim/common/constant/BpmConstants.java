package com.paic.ncbs.claim.common.constant;


import com.paic.ncbs.claim.common.enums.CaseProcessStatus;

import java.util.*;


public class BpmConstants {

    private BpmConstants() {

    }

    /**
     * OC_REPORT,"报案"
     */
    public static final String OC_REPORT = "OC_REPORT";
    /**
     * OC_REPORT_TRACK,"报案跟踪"
     */
    public static final String OC_REPORT_TRACK = "OC_REPORT_TRACK";
    /**
     * OC_CHECK_DUTY,"收单"
     */
    public static final String OC_CHECK_DUTY = "OC_CHECK_DUTY";
    /**
     * OC_MANUAL_SETTLE,"理算"
     */
    public static final String OC_MANUAL_SETTLE = "OC_MANUAL_SETTLE";
    /**
     * OC_SETTLE_REVIEW,"核赔"
     */
    public static final String OC_SETTLE_REVIEW = "OC_SETTLE_REVIEW";
    /**
     * OC_ZERO_CANCEL_DEPT_AUDIT,"零注审批"
     */
    public static final String OC_ZERO_CANCEL_DEPT_AUDIT = "OC_ZERO_CANCEL_DEPT_AUDIT";
    /**
     * OC_COMMUNICATE,"沟通"
     */
    public static final String OC_COMMUNICATE = "OC_COMMUNICATE";
    /**
     * OC_MAJOR_INVESTIGATE,"调查"
     */
    public static final String OC_MAJOR_INVESTIGATE = "OC_MAJOR_INVESTIGATE";
    /**
     * OC_INVESTIGATE_APPROVAL,"提调审批"
     */
    public static final String OC_INVESTIGATE_APPROVAL = "OC_INVESTIGATE_APPROVAL";
    /**
     * OC_INVESTIGATE_REVIEW,"调查审批"
     */
    public static final String OC_INVESTIGATE_REVIEW = "OC_INVESTIGATE_REVIEW";
    /**
     * OC_REGISTER_REVIEW,"立案审批"
     */
    public static final String OC_REGISTER_REVIEW = "OC_REGISTER_REVIEW";
    /**
     * OC_PREPAY_REVIEW,"预赔审批"
     */
    public static final String OC_PREPAY_REVIEW = "OC_PREPAY_REVIEW";
    /**
     * 支付退回 OC_PAY_BACK_MODIFY,"支付修改"
     */
    public static final String OC_PAY_BACK_MODIFY = "OC_PAY_BACK_MODIFY";
    /**
     * OC_PAY_BACK_MODIFY_REVIEW,"支付修改审批"
     */
    public static final String OC_PAY_BACK_MODIFY_REVIEW = "OC_PAY_BACK_MODIFY_REVIEW";
    /**
     * OC_REJECT_REVIEW,"拒赔审批"
     */
    public static final String OC_REJECT_REVIEW = "OC_REJECT_REVIEW";
    /**
     * 客户补材
     */
    public static final String OC_WAIT_CUSTOMER_SUPPLEMENTS = "OC_WAIT_CUSTOMER_SUPPLEMENTS";
    /**
     * 理赔二核
     */
    public static final String OC_CLAIM_SECOND_UNDERWRITING = "OC_CLAIM_SECOND_UNDERWRITING";

    /**
     * OC_RESTART_CASE_APPROVAL,"重开审批"
     */
    public static final String OC_RESTART_CASE_APPROVAL = "OC_RESTART_CASE_APPROVAL";

    /**
     * OC_RESTART_CASE_MODIFY,"重开修改"
     */
    public static final String OC_RESTART_CASE_MODIFY = "OC_RESTART_CASE_MODIFY";

    /**
     * OC_FEE_INVOICE_MODIFY,费用发票修改
     */
    public static final String OC_FEE_INVOICE_MODIFY = "OC_FEE_INVOICE_MODIFY";

    /**
     * OC_ESTIMATE_CHANGE_REVIEW,"未决修正审批"
     */
    public static final String OC_ESTIMATE_CHANGE_REVIEW = "OC_ESTIMATE_CHANGE_REVIEW";
    /**
     * OC_RECOVERY,追偿
     */
    public static final String OC_REPLEVY = "OC_REPLEVY";
    /**
     * OC_RECOVERY_REVIEW,追偿审批
     */
    public static final String OC_REPLEVY_REVIEW = "OC_REPLEVY_REVIEW";
    /**
     * OC_RECOVERY_FEE_REVIEW,追偿费用审批
     */
    public static final String OC_REPLEVY_FEE_REVIEW = "OC_REPLEVY_FEE_REVIEW";

    /**
     * 黑名单
     */
    public static final String OC_BLACK_LIST = "OC_BLACK_LIST";

    /**
     * OC_NO_POLICY_REPORT,"无保单报案"
     */
    public static final String OC_NO_POLICY_REPORT = "OC_NO_POLICY_REPORT";

    public static final Map<String,String> TASK_MAP = new HashMap<>();
    public static final Map<String,String> TASK_ID_MAP = new HashMap<>();
    public static final List<String> SUPEND_USE = new ArrayList<>();
    public static final List<String> SURVER = new ArrayList<>();
    public static final List<String> APPROVETASK_CONTROL = new ArrayList<>();

    public static final String REPORT_TRACK = "reportTrack";
    public static final String CHECK_DUTY = "checkDuty";
    public static final String PERSON_TRACE = "personTrace";
    public static final String MANUAL_SETTLE = "manualSettle";
    public static final String SETTLE_REVIEW = "settleReview";
    public static final String ZERO_CANCEL_APPLY = "zeroCancelApply";
    public static final String ZERO_CANCEL_AUDIT = "zeroCancelAudit";
    public static final String INVESTIGATE= "investigate";
    public static final String COMMUNICATE = "communicate";
    /**
     * 人伤跟踪
     */
    public static final String OC_HUMAN_INJURY_TRACKING = "OC_HUMAN_INJURY_TRACKING";

    static {
        TASK_MAP.put(OC_SETTLE_REVIEW,"核赔");
        TASK_MAP.put(OC_REGISTER_REVIEW,"立案审批");
        TASK_MAP.put(OC_REJECT_REVIEW,"拒赔审批");
        TASK_MAP.put(OC_ZERO_CANCEL_DEPT_AUDIT,"零注审批");
        TASK_MAP.put(OC_INVESTIGATE_APPROVAL,"提调审批");
        TASK_MAP.put(OC_INVESTIGATE_REVIEW,"调查审批");
        TASK_MAP.put(OC_PAY_BACK_MODIFY_REVIEW,"支付修改审批");
        TASK_MAP.put(OC_REPORT_TRACK,"报案跟踪");
        TASK_MAP.put(OC_CHECK_DUTY,"收单");
        TASK_MAP.put(OC_MANUAL_SETTLE,"理算");
        TASK_MAP.put(OC_MAJOR_INVESTIGATE,"调查");
        TASK_MAP.put(OC_COMMUNICATE,"沟通");
        TASK_MAP.put(OC_PAY_BACK_MODIFY,"支付修改");
        TASK_MAP.put(OC_PREPAY_REVIEW,"预赔审批");
        TASK_MAP.put(OC_RESTART_CASE_APPROVAL,"重开审批");
        TASK_MAP.put(OC_RESTART_CASE_MODIFY,"重开修改");
        TASK_MAP.put(OC_WAIT_CUSTOMER_SUPPLEMENTS,"客户补材");
        TASK_MAP.put(OC_FEE_INVOICE_MODIFY,"费用修改");
        TASK_MAP.put(OC_ESTIMATE_CHANGE_REVIEW,"未决修正审批");
        TASK_MAP.put(OC_CLAIM_SECOND_UNDERWRITING,"二核");
        TASK_MAP.put(OC_REPLEVY,"追偿");
        TASK_MAP.put(OC_REPLEVY_REVIEW,"追偿审批");
        TASK_MAP.put(OC_REPLEVY_FEE_REVIEW,"追偿费用审批");
        TASK_MAP.put(OC_HUMAN_INJURY_TRACKING,"人伤跟踪");
        TASK_MAP.put(OC_BLACK_LIST,"风控审批");
        TASK_MAP.put(OC_NO_POLICY_REPORT,"无保单报案");
    }

    static {
        TASK_ID_MAP.put(OC_REPORT_TRACK,REPORT_TRACK);
        TASK_ID_MAP.put(OC_CHECK_DUTY,CHECK_DUTY);
        TASK_ID_MAP.put(OC_MANUAL_SETTLE,MANUAL_SETTLE);
    }

    public static final Set<String> GRAY_TASK = new HashSet<>();
    static {
        GRAY_TASK.add(OC_REPORT_TRACK);
        SUPEND_USE.add(OC_REPORT_TRACK);
        SUPEND_USE.add(OC_REGISTER_REVIEW);
        SUPEND_USE.add(OC_CHECK_DUTY);
        SUPEND_USE.add(OC_MANUAL_SETTLE);
        SUPEND_USE.add(OC_SETTLE_REVIEW);

        SURVER.add(OC_REPORT_TRACK);
        SURVER.add(OC_CHECK_DUTY);
        SURVER.add(OC_MANUAL_SETTLE);

        APPROVETASK_CONTROL.add(OC_REGISTER_REVIEW);
        APPROVETASK_CONTROL.add(OC_SETTLE_REVIEW);
        APPROVETASK_CONTROL.add(OC_REJECT_REVIEW);
        APPROVETASK_CONTROL.add(OC_ZERO_CANCEL_DEPT_AUDIT);
        APPROVETASK_CONTROL.add(OC_INVESTIGATE_APPROVAL);
        APPROVETASK_CONTROL.add(OC_INVESTIGATE_REVIEW);
        APPROVETASK_CONTROL.add(OC_COMMUNICATE);
        APPROVETASK_CONTROL.add(OC_PREPAY_REVIEW);
        APPROVETASK_CONTROL.add(OC_PAY_BACK_MODIFY_REVIEW);
        APPROVETASK_CONTROL.add(OC_RESTART_CASE_APPROVAL);
        APPROVETASK_CONTROL.add(OC_RESTART_CASE_MODIFY);
        APPROVETASK_CONTROL.add(OC_ESTIMATE_CHANGE_REVIEW);
        APPROVETASK_CONTROL.add(OC_REPLEVY_FEE_REVIEW);
        APPROVETASK_CONTROL.add(OC_REPLEVY_REVIEW);
        APPROVETASK_CONTROL.add(OC_BLACK_LIST);
    }

    public static final Map<String,String> MAIN_TASK_MAP = new HashMap<>();

    static {
        MAIN_TASK_MAP.put(OC_REPORT_TRACK,"报案跟踪");
        MAIN_TASK_MAP.put(OC_CHECK_DUTY,"收单");
        MAIN_TASK_MAP.put(OC_MANUAL_SETTLE,"理算");
        MAIN_TASK_MAP.put(OC_SETTLE_REVIEW,"核赔");
    }

    public static final Map<String,String> TASK_URL_MAP = new HashMap<>();

    static {
        TASK_URL_MAP.put(OC_REPORT_TRACK,REPORT_TRACK);
        TASK_URL_MAP.put(OC_CHECK_DUTY,"details");
        TASK_URL_MAP.put(OC_MANUAL_SETTLE,"settles");
        TASK_URL_MAP.put(OC_SETTLE_REVIEW,SETTLE_REVIEW);
        TASK_URL_MAP.put(OC_ZERO_CANCEL_DEPT_AUDIT,"applyCheck");
        TASK_URL_MAP.put(OC_REGISTER_REVIEW,"registerAudit");
        TASK_URL_MAP.put(OC_PREPAY_REVIEW,"prePayAudit");
    }

    public static final List<String> OCHCS_BPM_ORDER = new ArrayList<>();

    static {
        OCHCS_BPM_ORDER.add(OC_REPORT_TRACK);
        OCHCS_BPM_ORDER.add(OC_REGISTER_REVIEW);
        OCHCS_BPM_ORDER.add(OC_CHECK_DUTY);
        OCHCS_BPM_ORDER.add(OC_MANUAL_SETTLE);
        OCHCS_BPM_ORDER.add(OC_SETTLE_REVIEW);
        OCHCS_BPM_ORDER.add(OC_ZERO_CANCEL_DEPT_AUDIT);
        OCHCS_BPM_ORDER.add(OC_COMMUNICATE);
        OCHCS_BPM_ORDER.add(OC_MAJOR_INVESTIGATE);
        OCHCS_BPM_ORDER.add(OC_INVESTIGATE_APPROVAL);
        OCHCS_BPM_ORDER.add(OC_INVESTIGATE_REVIEW);
        OCHCS_BPM_ORDER.add(OC_PREPAY_REVIEW);
        //加入拒赔审批,个人工作台回显
        OCHCS_BPM_ORDER.add(OC_REJECT_REVIEW);
        OCHCS_BPM_ORDER.add(OC_BLACK_LIST);
    }

    /**
     * 任务状态：0-待处理，1-已完成，2-挂起
     * TASK_STATUS_PENDING =0-待处理
     * TASK_STATUS_COMPLETED=1-已完成
     * TASK_STATUS_SUSPEND=2-挂起
     * TASK_STATUS_PARALLEL_PENDING=3 并行任务待处理
     */
    public static final String TASK_STATUS_PENDING = BaseConstant.STRING_0;
    public static final String TASK_STATUS_COMPLETED = BaseConstant.STRING_1;
    public static final String TASK_STATUS_SUSPEND = BaseConstant.STRING_2;
    public static final String TASK_STATUS_PARALLEL_PENDING = BaseConstant.STRING_3;

    public static final Map<String,String> TASK_PROCESS_MAP = new HashMap<>();
    static {
        TASK_PROCESS_MAP.put(OC_REPORT_TRACK, CaseProcessStatus.PENDING_REGISTER.getCode());
        TASK_PROCESS_MAP.put(OC_CHECK_DUTY,CaseProcessStatus.PENDING_ACCEPT.getCode());
        TASK_PROCESS_MAP.put(OC_MANUAL_SETTLE,CaseProcessStatus.PENDING_SETTLE.getCode());
        TASK_PROCESS_MAP.put(OC_SETTLE_REVIEW,CaseProcessStatus.WAIT_VERIFICATION.getCode());

    }

    /**
     * 调查中台自定义code
     */
    public static final String INVESTIGATE_PLATFORM = "INVESTIGATE_PLATFORM";
    public static final String INVESTIGATE_PLATFORM_NAME = "调查中台";
    /**
     * 消息提醒大类自定义code
     * NOTICE_CLASS_TASK 任务调度
     * NOTICE_CLASS_OC_BACK 审批退回
     * NOTICE_CLASS_PAY_FALL 支付失败
     */
    public static final String NOTICE_CLASS_OC_BACK = BaseConstant.STRING_4;
    public static final String NOTICE_CLASS_TASK = BaseConstant.STRING_5;
    public static final String NOTICE_CLASS_PAY_FALL = BaseConstant.STRING_6;
    /**
     * 人伤跟踪消息提醒大类
     */
    public static final String NOTICE_CLASS_PERSON_TRACE = BaseConstant.STRING_7;
    /**
     * 消息提醒小类自定义code
     */
    /**
    * 未决审批退回
    * */
    public static final String NSC_PENDING = "401";
    /**
    * 核赔退回
    */
    public static final String NSC_SETTLE_REVIEW = "402";
    /**
    * 零注审批退回
    */
    public static final String NSC_ZERO_CANCEL_DEPT_AUDIT = "403";

    /**
     * 提调审批退回
     */
    public static final String NSC_INVESTIGATE_APPROVAL = "404";

    /**
     * 调查审批退回
     */
    public static final String NSC_INVESTIGATE_REVIEW = "405";

    /**
     * 拒赔审批退回
     */
    public static final String NSC_EREJECT_RVIEW = "406";

    /**
     * 支付修改审批退回
     */
    public static final String NSC_PAY_BACK_MODIFY = "407";

    /**
     * 重开审批退回
     */
    public static final String NSC_RESTART_CASE_APPROVAL = "408";

    /**
     * 预赔审批退回
     */
    public static final String NSC_PREPAY_REVIEW = "409";

    /**
     * 公估委托审批退回
     */
    public static final String NSC_APPRAISAL_COMMISSION = "451";

    /**
     * 估损审批退回
     */
    public static final String NSC_ESTIMATED_LOSS = "452";

    /**
     * 零注拒（重开）审批退回
     */
    public static final String NSC_ZERO_EREJECT = "453";

    /**
     * 再保分摊通知审批退回
     */
    public static final String NSC_REINSURANCE_SHARING = "454";

    /**
     * 反欺诈审批退回
     */
    public static final String NSC_ANTI_FRAUD = "455";

    /**
     * 追偿提交审批退回
     */
    public static final String NSC_RECOVERY = "456";


    public static final Map<String,String> NOTICE_SUB_CALSS_MAP = new HashMap<>();

    static {
        NOTICE_SUB_CALSS_MAP.put(NSC_PENDING,"未决审批");
        NOTICE_SUB_CALSS_MAP.put(NSC_SETTLE_REVIEW,"核赔");
        NOTICE_SUB_CALSS_MAP.put(NSC_ZERO_CANCEL_DEPT_AUDIT,"零注审批");
        NOTICE_SUB_CALSS_MAP.put(NSC_INVESTIGATE_APPROVAL,"提调审批");
        NOTICE_SUB_CALSS_MAP.put(NSC_INVESTIGATE_REVIEW,"调查审批");
        NOTICE_SUB_CALSS_MAP.put(NSC_EREJECT_RVIEW ,"拒赔审批");
        NOTICE_SUB_CALSS_MAP.put(NSC_PAY_BACK_MODIFY,"支付修改审批");
        NOTICE_SUB_CALSS_MAP.put(NSC_RESTART_CASE_APPROVAL,"重开审批");
        NOTICE_SUB_CALSS_MAP.put(NSC_PREPAY_REVIEW,"预赔审批");
        NOTICE_SUB_CALSS_MAP.put(NSC_APPRAISAL_COMMISSION,"公估委托审批");
        NOTICE_SUB_CALSS_MAP.put(NSC_ESTIMATED_LOSS,"估损审批");
        NOTICE_SUB_CALSS_MAP.put(NSC_ZERO_EREJECT,"零注拒（重开）审批");
        NOTICE_SUB_CALSS_MAP.put(NSC_REINSURANCE_SHARING,"再保分摊通知审批");
        NOTICE_SUB_CALSS_MAP.put(NSC_ANTI_FRAUD,"反欺诈审批");
        NOTICE_SUB_CALSS_MAP.put(NSC_RECOVERY,"追偿提交审批");
    }
    /**
     * 消息源系统
     * SOURCE_SYSTEM_GLOBAL global理赔
     * SOURCE_SYSTEM_OC OC理赔
     */
    public static final String SOURCE_SYSTEM_GLOBAL = BaseConstant.STRING_1;
    public static final String SOURCE_SYSTEM_OC = BaseConstant.STRING_2;

    /**
     * 发起操作状态自定义code
     * 0-发起，1-提交，3-退回
     * */
    public static final String OPERATION_INITIATE = "0";
    public static final String OPERATION_SUBMIT = "1";
    public static final String OPERATION_BACK = "3";

}