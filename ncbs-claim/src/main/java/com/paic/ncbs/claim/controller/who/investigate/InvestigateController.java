package com.paic.ncbs.claim.controller.who.investigate;

import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.constant.InvestigateConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.accident.AccidentSceneDto;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateAuditDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateDTO;
import com.paic.ncbs.claim.model.dto.investigate.TpaGlobalAgentDTO;
import com.paic.ncbs.claim.model.dto.print.PrintEntrustDTO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateCooperationCompanyVO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateVO;
import com.paic.ncbs.claim.model.vo.investigate.OffSiteInvestigateVO;
import com.paic.ncbs.claim.service.common.ClaimSendTpaMqInfoService;
import com.paic.ncbs.claim.service.doc.PrintCoreService;
import com.paic.ncbs.claim.service.investigate.InvestigateAuditService;
import com.paic.ncbs.claim.service.investigate.InvestigateService;
import com.paic.ncbs.claim.service.taskdeal.TaskPoolService;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import io.swagger.annotations.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Api(tags = "调查")
@RestController
@RequestMapping(value = "/who/app/investigateAction")
public class InvestigateController extends BaseController {

    @Autowired
    private InvestigateService investigateService;

    @Autowired
    private InvestigateAuditService investigateAuditService;

    @Autowired
    private TaskPoolService taskPoolService ;

    @Autowired
    private ClaimSendTpaMqInfoService claimSendTpaMqInfoService;

    @Autowired
    InvestigateMapper investigateDao;

    @Autowired
    private PrintCoreService printCoreService;


    @ApiOperation("根据调查信息主键查询企业信息")
    @GetMapping(value = "/getCompanyByIdAhcsInvestigate/{idAhcsInvestigate}")
    public ResponseResult<List<InvestigateCooperationCompanyVO>> getCompanyByIdAhcsInvestigate(@ApiParam("调查ID") @PathVariable("idAhcsInvestigate") String idAhcsInvestigate) {
        return  ResponseResult.success(investigateService.getCompanyByIdAhcsInvestigate(idAhcsInvestigate));
    }

    @ApiOperation("根据调查任务主键查询企业信息")
    @GetMapping(value = "/getCompanyByIdAhcsInvestigateTask/{idAhcsInvestigateTask}")
    public ResponseResult<List<InvestigateCooperationCompanyVO>> getCompanyByIdAhcsInvestigateTask(@ApiParam("调查任务ID") @PathVariable("idAhcsInvestigateTask") String idAhcsInvestigateTask) {
        return  ResponseResult.success(investigateService.getCompanyByIdAhcsInvestigateTask(idAhcsInvestigateTask));
    }

    @ApiOperation("查询草稿调查")
    @GetMapping(value = "/getNoCommitData/{reportNo}/{caseTimes}")
    public ResponseResult<InvestigateDTO> getNoCommitData(@PathVariable("reportNo") String reportNo,@PathVariable("caseTimes") Integer caseTimes) {
        return  ResponseResult.success(investigateService.getNoCommitData(reportNo,caseTimes));
    }

    @ApiOperation("调查次数")
    @GetMapping(value = "/getIvvesigateCount/{reportNo}/{caseTimes}")
    public ResponseResult<Integer> getIvvesigateCount(@PathVariable("reportNo") String reportNo,@PathVariable("caseTimes") Integer caseTimes) {
        return  ResponseResult.success(investigateService.getIvvesigateCount(reportNo,null));
    }


    @ApiOperation("查询案件的历史调查信息")
    @GetMapping(value = "/getHistoryInvestigateByReportNo/{reportNo}")
    public ResponseResult<List<InvestigateVO>> getHistoryInvestigateByReportNo(@PathVariable("reportNo") String reportNo) {

        LogUtil.audit("#调查·查询案件的历史调查信息#入参#reportNo=%s", reportNo);

        return ResponseResult.success(investigateService.getHistoryInvestigate(reportNo, null));
    }

    @ApiOperation("查询案件的历史调查信息-外部调查")
    @PostMapping(value = "/getHistoryOutInvestigateByReportNo")
    public ResponseResult<List<InvestigateVO>> getHistoryOutInvestigateByReportNo(@RequestBody InvestigateDTO investigate) {

        LogUtil.audit("#调查·查询案件的外部历史调查信息#入参#reportNo=%s", investigate.getReportNo());
        List<InvestigateVO> investigateVOList = investigateService.getHistoryOutInvestigate(investigate);
        return ResponseResult.success(investigateVOList);
    }



    @ApiOperation("提起调查")
    @PostMapping(value = "/addInvestigate")
    public ResponseResult<Object> addInvestigate(@RequestBody InvestigateDTO investigate) throws GlobalBusinessException {
        LogUtil.audit("#调查·提起调查#入参#reportNo=" + investigate);
        UserInfoDTO u = WebServletContext.getUser();

        // o暂存 1提交
        if (investigate.getOperate() == 0){
            investigateService.initInvestigate(investigate, u) ;
        }else {
            if (!investigateService.checkIsCanSendForMultiClaim(investigate.getReportNo(), investigate.getCaseTimes())) {
                throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG,
                        "结案后，重开申请发送之前，不能提起调查");
            }
            investigateService.initInvestigate(investigate, u) ;
        }
        //提调需要传入操作节点，核心通知TPA时需要判断
        if(Objects.equals(BpmConstants.OC_REPORT_TRACK,investigate.getTaskCode())){
            claimSendTpaMqInfoService.sendTpaMq(investigate.getReportNo(),investigate.getCaseTimes(), CaseProcessStatus.WAIT_INVESTIGATION_APPROVING.getCode());
        }
        return ResponseResult.success();
    }

    @ApiOperation("根据id查询调查信息")
    @GetMapping(value = "/getInvestigateById/{idAhcsInvestigate}")
    public ResponseResult<InvestigateVO> getInvestigateById(@ApiParam("调查ID") @PathVariable("idAhcsInvestigate") String idAhcsInvestigate) {
        LogUtil.audit("#调查·根据id查询调查信息#入参#idAhcsInvestigate=" + idAhcsInvestigate);
        return ResponseResult.success(investigateService.getInvestigateById(idAhcsInvestigate));
    }

    @ApiOperation("查询事故场景")
    @GetMapping(value = "/getAccidentSceneData/{collectionCode}")
    public ResponseResult<List<AccidentSceneDto>> getAccidentSceneData(@ApiParam("参数类型码") @PathVariable("collectionCode") String collectionCode) {
        LogUtil.audit("#调查· 查询事故场景#入参#collectionCode=" + collectionCode);
        return ResponseResult.success(investigateService.getAccidentSceneData(collectionCode));
    }

    @ApiOperation("新增提调审批")
    @PostMapping(value = "/addInvestigateAudit")
    public ResponseResult<Object> addInvestigateAudit(@RequestBody InvestigateAuditDTO investigateAudit) throws GlobalBusinessException {

        LogUtil.audit("#调查· 完成提调审批#入参#investigateAudit=" + investigateAudit);

        UserInfoDTO u = WebServletContext.getUser();
        investigateAudit.setCreatedBy(u.getUserCode());
        investigateAudit.setUpdatedBy(u.getUserCode());
        investigateAudit.setAuditorUm(u.getUserCode());

        if (!StringUtils.isNotEmpty(investigateAudit.getInvestigateDepartment())){
            LogUtil.audit("#调查· 完成提调审批#入参#Department=" + WebServletContext.getDepartmentCode());
            investigateAudit.setInvestigateDepartment(WebServletContext.getDepartmentCode());
        }
        String olDepartmentCode = investigateAudit.getInvestigateDepartment();


        investigateAuditService.addInvestigateAudit(investigateAudit);
        InvestigateVO investigateVo = investigateDao.getInvestigateById(investigateAudit.getIdAhcsInvestigate());
        //生成公估委托书
        if(!InvestigateConstants.AHCS_INVESTIGATE_STATUS_BACK.equals(investigateVo.getInvestigateStatus())){
            PrintEntrustDTO printEntrustDTO = new PrintEntrustDTO();
            printEntrustDTO.setIdAhcsInvestigate(investigateAudit.getIdAhcsInvestigate());
            String now = System.currentTimeMillis()+"";
            printCoreService.saveCommissionFileAsync(now,null,now,printEntrustDTO);
        }


        if (StringUtils.isNotEmpty(investigateAudit.getInvestigateDepartment())
                && StringUtils.isNotEmpty(investigateAudit.getInvestigateItems())) {

            OffSiteInvestigateVO offSiteInvestigateVO = new OffSiteInvestigateVO();
            offSiteInvestigateVO.setIdAhcsInvestigate(investigateAudit.getIdAhcsInvestigate());
            offSiteInvestigateVO.setInvestigateDepartment(olDepartmentCode);
            offSiteInvestigateVO.setInvestigateItems(investigateAudit.getInvestigateItems());

            LogUtil.audit("#调查· 新增(调查审批表)#入参#offSiteInvestigateVO=" + offSiteInvestigateVO);
            investigateAuditService.addOffSiteInvestigate(offSiteInvestigateVO, u);
        }
        return ResponseResult.success();
    }

    @ApiOperation("根据报案号/赔付次数获取案件小类")
    @GetMapping(value = "/getCaseClassListCode/{reportNo}/{caseTimes}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String",dataTypeClass=String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int",dataTypeClass=Integer.class)
    })    public ResponseResult<List<String>> getCaseClassListCode(@PathVariable("reportNo") String reportNo,
                                                                   @PathVariable("caseTimes") Integer caseTimes) {
        LogUtil.audit("#调查·根据报案号/赔付次数获取案件小类code#入参#reportNo=%s,caseTimes=%s", reportNo, caseTimes);
        return ResponseResult.success(investigateService.getCaseClassListCode(reportNo, caseTimes));
    }

    @ApiOperation("获取未完成的调查记录")
    @GetMapping(value = "/getNoFinishInvestigateRecord/{reportNo}/{caseTimes}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String",dataTypeClass=String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int",dataTypeClass=Integer.class)
    })
    public ResponseResult<InvestigateDTO> getNoFinishInvestigateRecord(@PathVariable("reportNo") String reportNo,
                                                                       @PathVariable("caseTimes") Integer caseTimes) throws GlobalBusinessException {
        return ResponseResult.success(investigateService.getNoFinishInvestigateRecord(reportNo, caseTimes));
    }

    @ApiOperation("校验是否包含入场费")
    @GetMapping(value = "/getIsHaveEnteringFee/{reportNo}/{caseTimes}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号",dataType = "String",dataTypeClass=String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int",dataTypeClass=Integer.class)
    })
    public ResponseResult<String> getIsHaveEnteringFee(@PathVariable("reportNo") String reportNo,
                                                       @PathVariable("caseTimes") Integer caseTimes) {
        return ResponseResult.success(investigateService.getIsHaveEnteringFee(reportNo, caseTimes));
    }

    @ApiOperation("查询外部调查机构接口")
    @GetMapping("/getExternalDepartmentList")
    public ResponseResult<Object> getExternalDepartmentList(){
        LogUtil.audit("查询外部调查机构：");
        return  investigateService.getExternalDepartmentList();
    }

    @ApiOperation("查询服务信息")
    @GetMapping("/getServerInfoList")
    public ResponseResult<Object> getServerInfoList() {
        return investigateService.getServerInfoList();

    }

}