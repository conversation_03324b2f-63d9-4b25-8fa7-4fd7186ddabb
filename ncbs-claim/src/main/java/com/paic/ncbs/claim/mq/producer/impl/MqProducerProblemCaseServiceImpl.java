package com.paic.ncbs.claim.mq.producer.impl;

import cn.wesure.cmq.CmqTemplate;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Stopwatch;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.model.dto.problem.RequestData;
import com.paic.ncbs.claim.mq.producer.MqProducerProblemCaseService;
import com.paic.ncbs.claim.service.mqcompensation.MqCompensationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class MqProducerProblemCaseServiceImpl implements MqProducerProblemCaseService {

    @Autowired
    private CmqTemplate cmqTemplate;
    @Value("${mq.tpa.problemCase.topic:T-NCBS-CLAIM-PROBLEMCASE-DEV}")
    private String topic;
    @Value("${mq.tpa.problemCase.delayTime:2*60*1000}")
    private Integer delayTime;
    @Autowired
    private MqCompensationService mqCompensationService;
    @Override
    public void requestProblemCase(RequestData data) {

        try {
            ObjectMapper mapper = new ObjectMapper();
            String msg = mapper.writeValueAsString(data);
            log.info("MqProducerProblemCaseServiceImpl.requestProblemCase,topic={}, msg={}",topic, msg);
            Stopwatch stopwatch = Stopwatch.createStarted();
            String mqResp;
            try {
                LogUtil.audit("开始发送问题件答复mq topic"+topic+"msg="+msg);
                mqResp = cmqTemplate.sendQueue(topic,msg, delayTime);
//                mqResp = cmqTemplate.sendTopic(topic,msg);
                log.info("MqProducerProblemCaseServiceImpl.requestProblemCase, mqResp={}, useTime={}", mqResp, stopwatch.elapsed(TimeUnit.MILLISECONDS));
            } catch (Exception e) {
                log.error("MqProducerProblemCaseServiceImpl sendTopic error", e);
                mqCompensationService.addMqCompensation(topic, msg, BaseConstant.INT_0);
            }
        } catch (Exception e) {
            log.error("MqProducerProblemCaseServiceImpl.requestProblemCase,topic={}, msg={}",topic, JSON.toJSONString(data), e);
        }
    }
}
