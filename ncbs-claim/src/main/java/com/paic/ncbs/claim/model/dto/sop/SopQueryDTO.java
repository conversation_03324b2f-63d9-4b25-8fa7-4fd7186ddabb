package com.paic.ncbs.claim.model.dto.sop;

import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * SOP查询条件DTO
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Data
@ApiModel("SOP查询条件")
public class SopQueryDTO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("SOP名称")
    private String sopName;

    @ApiModelProperty("发布人员")
    private String publisherCode;

    @ApiModelProperty("适用环节")
    private String taskDefinitionBpmKey;



    @ApiModelProperty("文件类型（01-文本 02-文件 03-所有）")
    private String fileType;

    @ApiModelProperty("状态（01-暂存、02-有效、03-无效）")
    private String status;

    @ApiModelProperty("生效日期开始")
    private String effectiveDateStart;

    @ApiModelProperty("生效日期结束")
    private String effectiveDateEnd;

    @ApiModelProperty("案件产品代码（用于匹配查询）")
    private String caseProductCode;

    @ApiModelProperty("案件方案代码（用于匹配查询）")
    private String casePlanCode;

    @ApiModelProperty("案件险种代码（用于匹配查询）")
    private String caseDutyCode;

    @ApiModelProperty("案件环节代码（用于匹配查询）")
    private String caseTaskDefinitionBpmKey;

}
