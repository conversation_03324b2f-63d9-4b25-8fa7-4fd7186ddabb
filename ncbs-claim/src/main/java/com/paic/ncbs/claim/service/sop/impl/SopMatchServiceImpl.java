package com.paic.ncbs.claim.service.sop.impl;

import com.paic.ncbs.claim.common.util.RapeCollectionUtils;
import com.paic.ncbs.claim.common.util.RapeStringUtils;
import com.paic.ncbs.claim.model.vo.sop.SopMainVO;
import com.paic.ncbs.claim.service.sop.SopMainService;
import com.paic.ncbs.claim.service.sop.SopMatchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * SOP匹配服务实现类
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Slf4j
@Service
public class SopMatchServiceImpl implements SopMatchService {

    @Autowired
    private SopMainService sopMainService;

    @Override
    public List<SopMainVO> matchSopByCase(String reportNo, Integer caseTimes, String taskBpmKey) {
        log.info("根据案件信息匹配SOP规则，reportNo：{}，caseTimes：{}，taskBpmKey：{}",
                reportNo, caseTimes, taskBpmKey);

        if (RapeStringUtils.isEmptyStr(reportNo)) {
            log.warn("报案号为空，无法匹配SOP");
            return new ArrayList<>();
        }

        try {
            List<SopMainVO> matchedSops = sopMainService.matchSopRulesByCase(reportNo, caseTimes, taskBpmKey);

            log.info("匹配到{}条SOP规则", matchedSops.size());
            return matchedSops;

        } catch (Exception e) {
            log.error("根据案件信息匹配SOP规则失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<SopMainVO> matchSopByConditions(String productCode, String planCode, String dutyCode, String taskDefinitionBpmKey) {
        log.info("根据条件匹配SOP规则，productCode：{}，planCode：{}，dutyCode：{}，taskDefinitionBpmKey：{}", 
                productCode, planCode, dutyCode, taskDefinitionBpmKey);
        
        try {
            List<SopMainVO> matchedSops = sopMainService.matchSopRules(productCode, planCode, dutyCode, taskDefinitionBpmKey);
            log.info("匹配到{}条SOP规则", matchedSops.size());
            return matchedSops;
        } catch (Exception e) {
            log.error("根据条件匹配SOP规则失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public String getCaseProductCode(String reportNo, Integer caseTimes) {
        log.info("获取案件产品信息，reportNo：{}，caseTimes：{}", reportNo, caseTimes);
        
        // TODO: 实现获取案件产品信息的逻辑
        // 这里需要根据实际的数据库表结构和业务逻辑来实现
        // 示例代码：
        /*
        try {
            ReportInfoEntity reportInfo = reportInfoService.getByReportNo(reportNo);
            if (reportInfo != null) {
                return reportInfo.getProductCode();
            }
        } catch (Exception e) {
            log.error("获取案件产品信息失败", e);
        }
        */
        
        log.warn("获取案件产品信息功能待实现");
        return null;
    }

    @Override
    public String getCasePlanCode(String reportNo, Integer caseTimes) {
        log.info("获取案件方案信息，reportNo：{}，caseTimes：{}", reportNo, caseTimes);
        
        // TODO: 实现获取案件方案信息的逻辑
        // 这里需要根据实际的数据库表结构和业务逻辑来实现
        // 示例代码：
        /*
        try {
            ReportInfoEntity reportInfo = reportInfoService.getByReportNo(reportNo);
            if (reportInfo != null) {
                return reportInfo.getPlanCode();
            }
        } catch (Exception e) {
            log.error("获取案件方案信息失败", e);
        }
        */
        
        log.warn("获取案件方案信息功能待实现");
        return null;
    }

    @Override
    public List<String> getCaseDutyCodes(String reportNo, Integer caseTimes) {
        log.info("获取案件险种信息，reportNo：{}，caseTimes：{}", reportNo, caseTimes);
        
        // TODO: 实现获取案件险种信息的逻辑
        // 这里需要根据实际的数据库表结构和业务逻辑来实现
        // 示例代码：
        /*
        try {
            List<DutyAttrEntity> dutyAttrs = dutyAttrService.getByReportNoAndCaseTimes(reportNo, caseTimes);
            if (!RapeCollectionUtils.isEmpty(dutyAttrs)) {
                return dutyAttrs.stream()
                        .map(DutyAttrEntity::getDutyCode)
                        .distinct()
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("获取案件险种信息失败", e);
        }
        */
        
        log.warn("获取案件险种信息功能待实现");
        return new ArrayList<>();
    }

    /**
     * 检查SOP列表中是否包含指定的SOP
     */
    private boolean containsSop(List<SopMainVO> sopList, String idSopMain) {
        if (RapeCollectionUtils.isEmpty(sopList) || RapeStringUtils.isEmptyStr(idSopMain)) {
            return false;
        }
        
        for (SopMainVO sop : sopList) {
            if (idSopMain.equals(sop.getIdSopMain())) {
                return true;
            }
        }
        
        return false;
    }

}
