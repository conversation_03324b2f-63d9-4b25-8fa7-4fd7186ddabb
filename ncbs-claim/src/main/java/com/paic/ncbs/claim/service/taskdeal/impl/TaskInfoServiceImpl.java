package com.paic.ncbs.claim.service.taskdeal.impl;

import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service("whoTaskInfoService")
public class TaskInfoServiceImpl implements TaskInfoService {

    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Autowired
    private OcasMapper ocasMapper;

    @Override
    public void addTaskInfo(TaskInfoDTO taskInfoDTO) {
        try {
            String taskId = taskInfoDTO.getTaskId();
            String assigner = taskInfoDTO.getAssigner();
            String taskDefinitionBpmKey = taskInfoDTO.getTaskDefinitionBpmKey();
            //如果是黑名单任务允许重复插入
            if (taskDefinitionBpmKey.equals(BpmConstants.OC_BLACK_LIST)){
                taskInfoDTO.setIdAhcsTaskInfo(UuidUtil.getUUID());
                Date assigneeTime = taskInfoDTO.getAssigneeTime();

                if (StringUtils.isNotEmpty(assigner) && assigneeTime == null) {
                    LogUtil.audit("#派工人不为空，且派工时间为空，则设置派工时间为当前时间# taskId=" + taskId + ",taskDefinitionBpmKey=" + taskDefinitionBpmKey);
                    taskInfoDTO.setAssigneeTime(new Date());
                }
                if ( StringUtils.isEmptyStr(taskInfoDTO.getStatus())){
                    taskInfoDTO.setStatus(BpmConstants.TASK_STATUS_PENDING);
                }
                taskInfoMapper.addTaskInfo(taskInfoDTO);
            }else {
                String idAhcsTaskInfo = taskInfoMapper.getIdAhcsTaskInfo(taskId, assigner,taskDefinitionBpmKey);
                if (StringUtils.isEmptyStr(idAhcsTaskInfo)) {
                    taskInfoDTO.setIdAhcsTaskInfo(UuidUtil.getUUID());
                    Date assigneeTime = taskInfoDTO.getAssigneeTime();

                    if (StringUtils.isNotEmpty(assigner) && assigneeTime == null) {
                        LogUtil.audit("#派工人不为空，且派工时间为空，则设置派工时间为当前时间# taskId=" + taskId + ",taskDefinitionBpmKey=" + taskDefinitionBpmKey);
                        taskInfoDTO.setAssigneeTime(new Date());
                    }
                    if ( StringUtils.isEmptyStr(taskInfoDTO.getStatus())){
                        taskInfoDTO.setStatus(BpmConstants.TASK_STATUS_PENDING);
                    }
                    taskInfoMapper.addTaskInfo(taskInfoDTO);
                } else {
                    LogUtil.audit("#任务已存在，不重复插入任务信息# taskId=" + taskId + ",taskDefinitionBpmKey=" + taskDefinitionBpmKey);
                }
            }
        } catch (Exception e) {
            LogUtil.error("写入任务信息表出现异常", e);
        }
    }

    @Override
    public void modifyTaskInfo(TaskInfoDTO taskInfoDTO) {
        try {
            taskInfoMapper.modifyTaskInfo(taskInfoDTO);
        } catch (Exception e) {
            LogUtil.error("修改任务信息表出现异常", e);
        }
    }

    @Override
    public void modifyTaskInfoAssigner(TaskInfoDTO taskInfoDTO) {
        try {
            taskInfoMapper.modifyTaskInfoAssigner(taskInfoDTO);
        } catch (Exception e) {
            LogUtil.error("修改任务信息表出现异常", e);
        }
    }

    @Override
    public String getMajoyProcessDepartmentByReport(String reportNo, Integer caseTimes) {

        return taskInfoMapper.getMajoyProcessDepartmentByReport(reportNo, caseTimes);
    }

    @Override
    public TaskInfoDTO findLatestByReportNoAndBpmKey(String reportNo,
                                                     Integer caseTimes,
                                                     String taskDefinitionBpmKey){
        return taskInfoMapper.findLatestByReportNoAndBpmKey(reportNo, caseTimes, taskDefinitionBpmKey);
    }


    @Override
    public void modifyTaskInfoByDefKey(TaskInfoDTO taskInfoDTO) {
        taskInfoMapper.modifyTaskInfoByDefKey(taskInfoDTO);
    }

    @Override
    public void suspendOrActiveTask(TaskInfoDTO taskInfoDTO) {
        taskInfoMapper.suspendOrActiveTask(taskInfoDTO);
    }

    @Override
    public String getConclusion(String reportNo, Integer caseTimes) {
        return taskInfoMapper.getConclusion(reportNo,caseTimes);
    }

    @Override
    public TaskInfoDTO getTaskInfoForInvestigate(String reportNo, Integer caseTimes) {
        return taskInfoMapper.getTaskInfoForInvestigate(reportNo, caseTimes);
    }

    @Override
    public String getNoFinishScoreTask(String reportNo, Integer caseTimes) {
        return taskInfoMapper.getNoFinishScoreTask(reportNo, caseTimes);
    }

    @Override
    public boolean hasNotFinishTaskByTaskKey(String reportNo, Integer caseTimes, String taskKey,String taskId) {
        return taskInfoMapper.hasNotFinishTaskByTaskKey(reportNo,caseTimes,taskKey,taskId)>0;
    }

    @Override
    public TaskInfoDTO ownNewSuspendProcess(String reportNo, Integer caseTimes, List<String> caseProcess,String status) {
        return taskInfoMapper.ownNewSuspendProcess(reportNo,caseTimes,caseProcess,status);
    }

    @Override
    public int getSuspendTaskCount(TaskInfoDTO taskInfoDTO){
        return taskInfoMapper.getSuspendTaskCount(taskInfoDTO);
    }

    @Override
    public void suspendNotUpdateDate(TaskInfoDTO dto) {
        taskInfoMapper.suspendNotUpdateDate(dto);
    }

    @Override
    public TaskInfoDTO getTaskDtoByTaskId(String taskId) {
        return taskInfoMapper.getTaskDtoByTaskId(taskId);
    }

    @Override
    public boolean checkPolicyActualPremium(String reportNo) {
        List<AhcsPolicyInfoEntity> policyList = ocasMapper.getPolicyActualPremiumList(reportNo);
        if(ListUtils.isNotEmpty(policyList)){
            for (AhcsPolicyInfoEntity policy : policyList) {
                BigDecimal agree = Optional.ofNullable(policy.getTotalAgreePremium()).orElse(BigDecimal.ZERO);
                BigDecimal actual = Optional.ofNullable(policy.getTotalActualPremium()).orElse(BigDecimal.ZERO);
                //应收不等于实收意味着保费为未交齐
                if(agree.compareTo(actual) != 0){
                    return true;
                }
            }
        }else{
            //没有缴费记录意味着保费为未交齐
            return true;
        }
        return false;
    }

    @Override
    public TaskInfoDTO checkWorkflow(String reportNo, int caseTimes, String bpmConstants, String status) {
        return taskInfoMapper.checkWorkflow(reportNo,caseTimes,bpmConstants,status);
    }

    @Override
    public String getTaskId(String reportNo, Integer caseTimes, String taskDefinitionBpmKey) {
        return taskInfoMapper.getTaskId(reportNo, caseTimes, taskDefinitionBpmKey);
    }

    @Override
    public void deleteTaskInfo(TaskInfoDTO taskInfoDTO) {
        taskInfoMapper.deleteTaskInfo(taskInfoDTO);
    }

    @Override
    public String getLatestTaskId(String reportNo, Integer caseTimes, String taskKey) {
        return taskInfoMapper.getLatestTaskId(reportNo, caseTimes, taskKey);
    }

    public TaskInfoDTO getTaskAssignerName(String reportNo, Integer caseTimes, String taskKey) {
        return taskInfoMapper.getTaskAssignerName(reportNo,caseTimes,taskKey);
    }
}
