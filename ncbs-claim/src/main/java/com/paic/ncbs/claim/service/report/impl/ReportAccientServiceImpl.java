package com.paic.ncbs.claim.service.report.impl;

import com.paic.ncbs.claim.dao.base.BaseDao;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentEntity;
import com.paic.ncbs.claim.dao.mapper.report.ReportAccidentMapper;
import com.paic.ncbs.claim.service.report.ReportAccidentService;
import com.paic.ncbs.claim.service.base.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReportAccientServiceImpl extends BaseServiceImpl<ReportAccidentEntity> implements ReportAccidentService {

	@Autowired
	private ReportAccidentMapper reportAccidentMapper;
	
	@Override
	public BaseDao<ReportAccidentEntity> getDao() {
		return reportAccidentMapper;
	}

	@Override
	public ReportAccidentEntity getReportAccident(String reportNo) {
		return reportAccidentMapper.getReportAccident(reportNo);
	}

	@Override
	public void updateAccidentReasonByReportNo(String reportNo, String accidentCauseLevel1, String accidentCauseLevel2, String loginUm) {
		reportAccidentMapper.updateAccidentReasonByReportNo(reportNo, accidentCauseLevel1, accidentCauseLevel2,loginUm);
	}
}
