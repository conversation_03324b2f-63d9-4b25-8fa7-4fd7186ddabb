package com.paic.ncbs.claim.service.common;

public interface RedisService {

    /**
     * 加锁
     * @param key
     * @param value
     * @param expiredTime 过期时间 单位秒
     * @return
     */
    boolean tryLock(String key,String value,Long expiredTime);

    /**
     * 加锁
     * @param key
     * @param value
     * @return
     */
    boolean tryLock(String key,String value);

    /**
     * 释放锁
     * @param key
     * @param value
     * @return
     */
    boolean removeLock(String key,String value);

    /**
     * 防止重复提交 - 检查是否已存在重复提交
     * @param key
     * @return true表示存在重复提交，false表示不存在
     */
    boolean checkDuplicateSubmission(String key);

    /**
     * 防止重复提交 - 设置重复提交标记
     * @param key
     * @param expiredTime 过期时间 单位秒
     * @return true设置成功，false设置失败
     */
    boolean setDuplicateSubmission(String key, Long expiredTime);
}
