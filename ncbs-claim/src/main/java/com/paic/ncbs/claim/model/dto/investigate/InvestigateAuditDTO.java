package com.paic.ncbs.claim.model.dto.investigate;



import com.paic.ncbs.claim.model.dto.other.EntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@ApiModel("调查审批")
public class InvestigateAuditDTO extends EntityDTO {

    private static final long serialVersionUID = 1L;


    private String idAhcsInvestigateAudit;

    @ApiModelProperty("调查信息表主键")
    private String idAhcsInvestigate;

    @ApiModelProperty("发起人")
    private String initiatorUm;

    @ApiModelProperty("审批人")
    private String auditorUm;

    @ApiModelProperty("审批意见")
    private String auditOpinion;

    @ApiModelProperty("审批类型(01异地调查审批，02发起调查审批)")
    private String auditType;

    @ApiModelProperty(value = "备注")
    private String remark;


    private List<InvestigatorDto> investigators ;

    @ApiModelProperty(value = " 审批回复信息（分配信息/退回原因）")
    private String auditReply;

    @ApiModelProperty("调查机构")
    private String investigateDepartment;

    @ApiModelProperty("提调事项")
    private String investigateItems;

    @ApiModelProperty("调查类型")
    private String surveyType;

    @ApiModelProperty("外调公司统一社会信用代码")
    private String socialCreditCode;

    @ApiModelProperty("外调公司名称")
    private String companyName;

    @ApiModelProperty("任务类型，1-线上委托 2-线下委托")
    private String taskType;

    @ApiModelProperty("公估服务代码")
    private String serverCode;
}
