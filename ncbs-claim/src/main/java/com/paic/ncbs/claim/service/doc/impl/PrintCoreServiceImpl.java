package com.paic.ncbs.claim.service.doc.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.base.exception.NcbsException;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.controller.report.QueryReportController;
import com.paic.ncbs.claim.dao.entity.report.ReportAccidentEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateMapper;
import com.paic.ncbs.claim.dao.mapper.print.PrintTemplateMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportAccidentMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.model.dto.accident.AccidentSceneDto;
import com.paic.ncbs.claim.model.dto.doc.CreateClaimNoticeParamDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateDTO;
import com.paic.ncbs.claim.model.dto.ocas.OcasInsuredDTO;
import com.paic.ncbs.claim.model.dto.print.AppraisalCommissionDTO;
import com.paic.ncbs.claim.model.dto.print.PrintEntrustDTO;
import com.paic.ncbs.claim.model.dto.print.PrintTemplateDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.doc.PrintDutyPayVO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateVO;
import com.paic.ncbs.claim.model.vo.investigate.ServerInfoVO;
import com.paic.ncbs.claim.model.vo.investigate.TpaServerInfoListVO;
import com.paic.ncbs.claim.model.vo.taskdeal.TaskInfoVO;
import com.paic.ncbs.claim.service.doc.PrintCoreService;
import com.paic.ncbs.claim.service.doc.PrintService;
import com.paic.ncbs.claim.service.investigate.InvestigateService;
import com.paic.ncbs.claim.utils.HttpUtils;
import com.paic.ncbs.file.model.FileResponse;
import com.paic.ncbs.file.service.FileCommonService;
import com.paic.ncbs.policy.dto.ContractDTO;
import com.paic.ncbs.policy.dto.RiskPersonDTO;
import com.paic.ncbs.print.model.PolicyTermFtlDTO;
import com.paic.ncbs.print.util.GeneratePDFUtil;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import com.paic.ncbs.um.service.CacheService;
import freemarker.template.TemplateException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.util.LogUtil.log;

/**
 * <AUTHOR>
 * @Description
 * @date 2023-04-26 17:04
 */
@Service
@RefreshScope
public class PrintCoreServiceImpl implements PrintCoreService {

    @Autowired
    private GeneratePDFUtil generatePDFUtil;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private QueryReportController queryReportController;

    @Autowired
    private InvestigateMapper investigateMapper;

    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Autowired
    private InvestigateService investigateService;

    @Autowired
    private ReportInfoMapper reportInfoMapper;

    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;

    @Autowired
    private ReportAccidentMapper reportAccidentMapper;

    @Autowired
    private PrintTemplateMapper printTemplateMapper;

    @Autowired
    InvestigateMapper investigateDao;

    @Value("${samsung.print.url:http://*************/print/}")
    private String printNewUrl;

    @Value("${samsung.print.sysid:icoreclaimncbs}")
    private String printSysid;

    @Value("${samsung.print.userName:qianhaiclaim}")
    private String printUserName;

    @Value("${samsung.print.password:aiclaim12345}")
    private String printPassword;

    @Value("${samsung.print.isSign:N}")
    private String isSign;

    @Value("${env}")
    private String env;

    @Autowired
    private PrintService printService;

    @Autowired
    private FileCommonService fileCommonService;

    @Override
    @Async("asyncPool")
    public void saveFileAsync(String docNo, String xslFileName, String before, PrintDutyPayVO printDutyPayVO) {
        saveFile(docNo, xslFileName, before, printDutyPayVO);
    }

    @Override
    @Async("asyncPool")
    public void saveCommissionFileAsync(String docNo, String xslFileName, String before, PrintEntrustDTO printEntrustDTO) {
        saveCommissionFile(docNo, xslFileName, before, printEntrustDTO);
    }

    @Override
    public void saveCommissionFile(String docNo, String xslFileName, String before, PrintEntrustDTO printEntrustDTO) {
        try {
            String url = printNewUrl + "cgi-bin/jasperopenPdf.py?sysid=" + printSysid + "&docno=" + docNo
                    + "&doctype=EDR00001&userName=" + printUserName + "&password=" + printPassword;
            if ("Y".equals(isSign)) {
                url = url + "&" + printService.getSignature();
            }
            LogUtil.info("- 调用打印平台获取文件 - saveFile - file url = {}", url);
            // 打印平台暂时不对接mesh
            HttpRequest request = HttpUtil.createGet(url);
            request.setConnectionTimeout(60000);
            InputStream stream = request.execute().bodyStream();
            if ("samsung".equals(env)) {
                PrintTemplateDTO printTemplate = new PrintTemplateDTO();
                printTemplate.setPrintTemplateCode("ValuationAuthorizationLetter");
                printTemplate.setPrintTemplateType("CLAIM");
                printTemplate.setIdAhcsInvestigate(printEntrustDTO.getIdAhcsInvestigate());
                String base64 = freemarkPrint(printTemplate);
                String param = printEntrustDTO.getIdAhcsInvestigate();
                //调取签章接口
                CreateClaimNoticeParamDTO createClaimNoticeParamDTO = new CreateClaimNoticeParamDTO();
                createClaimNoticeParamDTO.setCtCode(param);
                createClaimNoticeParamDTO.setFileBase64Str(base64);
                try {
                    printService.sendClaimEntrustToCA(createClaimNoticeParamDTO);
                } catch (Exception e) {
                    LogUtil.error("签章接口调用异常", e);
                }
            } else {
                //签章问题 供测试使用
                String uuid = UuidUtil.getUUID();
                String fileId = upload(stream, ".pdf", uuid);
                LogUtil.info("- 上传文件到文件系统 - saveFile - file fileId = {}", fileId);
                printEntrustDTO.setFileId(fileId);
                //将文件ID存入数据库中
                printService.saveCommissionFileId(printEntrustDTO);
            }
        } catch (Exception e) {
            LogUtil.error("PrintCoreServiceImpl saveFile error", e);
        }
    }

    @Override
    public void saveFile(String docNo, String xslFileName, String before, PrintDutyPayVO printDutyPayVO) {
        try {
            //调取接口查看是否已经生成pdf文件
            String result = pdfexist(docNo, xslFileName);
            LogUtil.info("-调用打印平台获取文件- saveFile result = {}", result);
            String flag = result.replaceAll("returnValue=", "");
            LogUtil.info("result:{}",result);
            //0	PDF已成功  1正在排队，或者正在打印  2 数据格式不合法  3 Xml数据丢失，请重复发送数据  -1 密码错误
            if (BaseConstant.STRING_0.equals(flag)) {
                String reportNo = printDutyPayVO.getReportNo();
                Integer caseTimes = printDutyPayVO.getCaseTimes();
                String url = printNewUrl + "cgi-bin/jasperopenPdf.py?sysid=" + printSysid + "&docno=" + docNo
                        + "&doctype=EDR00001&userName=" + printUserName + "&password=" + printPassword;
                if ("Y".equals(isSign)) {
                    url = url + "&" + printService.getSignature();
                }
                LogUtil.info("- 调用打印平台获取文件 - saveFile - file url = {}", url);
                // 打印平台暂时不对接mesh
                HttpRequest request = HttpUtil.createGet(url);
                request.setConnectionTimeout(60000);
                InputStream stream = request.execute().bodyStream();

                if ("samsung".equals(env)) {
                    String base64 = getBase64FromInputStream(stream);
                    String param = reportNo + "_" + caseTimes;
                    //调取签章接口
                    CreateClaimNoticeParamDTO createClaimNoticeParamDTO = new CreateClaimNoticeParamDTO();
                    createClaimNoticeParamDTO.setCtCode(param);
                    createClaimNoticeParamDTO.setFileBase64Str(base64);
                    try {
                        printService.sendClaimNoticeToCA(createClaimNoticeParamDTO);
                    } catch (Exception e) {
                        LogUtil.error("签章接口调用异常", e);
                    }
                } else {
                    //签章问题 供测试使用
                    String uuid = UuidUtil.getUUID();
                    String fileId = upload(stream, ".pdf", uuid);
                    LogUtil.info("- 上传文件到文件系统 - saveFile - file fileId = {}", fileId);
                    //将文件ID存入数据库中
                    printService.saveClaimNoticeFileId(reportNo, caseTimes, fileId);
                }

            } else if (BaseConstant.STRING_1.equals(flag)) {
                //1 正在排队，或者正在打印 等待2s重新调用
                Long now = System.currentTimeMillis();
                Long costTime = now - Long.valueOf(before);
                //超过10s 放弃数据
                if (costTime < 100000) {
                    LogUtil.info("PrintServiceImpl pdfexist result is 1 ,wait and retry");
                    Thread.sleep(2000);
                    //回调方法
                    saveFile(docNo, xslFileName, before, printDutyPayVO);
                } else {
                    LogUtil.info("PrintController pdfexist result is 1,and cost time too long ,give up this data");
                }
            } else {
                LogUtil.info("调用打印平台查看是否生成文件接口 - PrintCoreServiceImpl saveFile error - result = {}", result);
            }
        } catch (Exception e) {
            LogUtil.error("PrintCoreServiceImpl saveFile error", e);
        }
    }

    public String freemarkPrint(PrintTemplateDTO printTemplate) throws TemplateException, IOException {
        AppraisalCommissionDTO appraisalCommissionDTO = printService.bulidPrintInfo(printTemplate.getIdAhcsInvestigate());
        printTemplate = printTemplateMapper.getPrintTemplate(printTemplate);
        byte[] data = generatePDFUtil.generatePDFFromHTML(printTemplate.getPrintTemplateValue(),appraisalCommissionDTO);
        return Base64.getEncoder().encodeToString(data);
    }

    private String upload(InputStream ins, String fileType, String iobskey) {
        FileResponse fileResponse = fileCommonService.upload(ins, iobskey, iobskey + fileType);
        return fileResponse.getFileId();
    }

    public String pdfexist(String docNo, String xslFileName) {
        Map<String, String> param = new HashMap<>();
        param.put("sysid", printSysid);
        param.put("docno", docNo);//System.currentTimeMillis()
        param.put("doctype", "EDR00001");//System.currentTimeMillis()
        param.put("xslFileName", xslFileName);//System.currentTimeMillis()
        param.put("userName", printUserName);
        param.put("password", printPassword);
        String requestUrl = printNewUrl + "cgi-bin/jaspergetPdf.py";//具体请求打印平台
        if ("Y".equals(isSign)) {
            requestUrl = requestUrl + "?" + printService.getSignature();
        }
        LogUtil.info("PrintCoreServiceImpl pdfexist http request param : docno = {}, xslFileName = {}",docNo,xslFileName);
        // 打印的不对接mesh
        return HttpUtils.post(requestUrl, null, param, null, "UTF-8");
    }

    private static String getBase64FromInputStream(InputStream inputStream) {
        byte[] data = null;
        try {
            ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
            byte[] buff = new byte[100];
            int rc = 0;
            while ((rc = inputStream.read(buff, 0, 100)) > 0) {
                swapStream.write(buff, 0, rc);
            }
            data = swapStream.toByteArray();
        } catch (IOException e) {
            LogUtil.error("getBase64FromInputStream error", e);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    LogUtil.error("getBase64FromInputStream error", e);
                }
            }
        }
        return Base64.getEncoder().encodeToString(data);
    }

}
