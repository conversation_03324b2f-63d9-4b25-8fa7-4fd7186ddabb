package com.paic.ncbs.claim.service.investigate;


import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskDTO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateTaskVO;
import com.paic.ncbs.claim.model.vo.investigate.InvestigateTaskVOForReport;

import java.util.List;
import java.util.Map;

public interface InvestigateTaskService {

    List<InvestigateTaskVO> getInvestigateTaskByInvestigateId(
            String idAhcsInvestigate) throws GlobalBusinessException;

    InvestigateTaskDTO getInvestigateTaskById(String idAhcsInvestigateTask);

    void modifyInvestigateTask(InvestigateTaskDTO investigateTask);


    InvestigateTaskVO getInvestigateTaskLinkedByTaskId(String idAhcsInvestigateTask);

    InvestigateTaskVOForReport getMajorInvestigateTaskLinkedByInvestigateId(
            String idAhcsInvestigate);


    List<InvestigateTaskVO> getAssistInvestigateTaskAllByMajorTaskId(
            String idAhcsInvestigateTask, String isOffsiteTask);


    void finishInvestigateTask(InvestigateTaskDTO investigateTask,
                               String userId) throws GlobalBusinessException;


    String getInvestigatorByTaskId(String idAhcsInvestigateTask) throws GlobalBusinessException;


    String getMajorTaskIdByInvestigateId(String idAhcsInvestigate);

    Map<String, String> generateInvestigatePdf(String idAhcsInvestigate);

}