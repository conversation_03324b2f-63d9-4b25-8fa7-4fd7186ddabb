package com.paic.ncbs.claim.dao.entity.sop;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * SOP信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Getter
@Setter
@TableName("clms_sop_main")
public class ClmsSopMain implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id_sop_main", type = IdType.INPUT)
    private String idSopMain;

    /**
     * sop名称
     */
    @TableField("sop_name")
    private String sopName;

    /**
     * 版本号
     */
    @TableField("version_no")
    private String versionNo;

    /**
     * sop简要描述
     */
    @TableField("sop_description")
    private String sopDescription;

    /**
     * 是否全流程（Y/N）
     */
    @TableField("is_all_process")
    private String isAllProcess;

    /**
     * sop规则内容
     */
    @TableField("sop_content")
    private String sopContent;

    /**
     * 发布人员
     */
    @TableField("publisher_code")
    private String publisherCode;

    /**
     * 发布人员姓名
     */
    @TableField("publisher_name")
    private String publisherName;

    /**
     * 发布时间
     */
    @TableField("publish_time")
    private LocalDateTime publishTime;

    /**
     * 是否有效（Y/N）
     */
    @TableField("valid_flag")
    private String validFlag;

    /**
     * 生效日期
     */
    @TableField("effective_date")
    private LocalDateTime effectiveDate;

    /**
     * 失效日期
     */
    @TableField("invalid_date")
    private LocalDateTime invalidDate;

    /**
     * 状态（01-暂存、02-有效、03-无效）
     */
    @TableField("status")
    private String status;

    /**
     * 类型（01-文本 02-文件 03-所有）
     */
    @TableField("file_type")
    private String fileType;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private LocalDateTime sysCtime;

    /**
     * 修改人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField("sys_utime")
    private LocalDateTime sysUtime;

    @TableField("batch_no")
    private String batchNo;
}
