package com.paic.ncbs.claim.common.constant;

import java.math.MathContext;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class ConstValues {


	public static final String CONFIRM_STATE_AGREE = "Y";
	public static final String CONFIRM_STATE_CONFIRM_DISAGREE = "N";
	public static final String CONFIRM_STATE_NOT_CONFIRM = "A";

	public static Map<String,String> confirmClaimMap = new HashMap<>();
	static
	{
		confirmClaimMap.put(CONFIRM_STATE_AGREE,"客户已确认");
		confirmClaimMap.put(CONFIRM_STATE_CONFIRM_DISAGREE,"客户不认可");
		confirmClaimMap.put(CONFIRM_STATE_NOT_CONFIRM,"客户未确认");
	}

	public static Map<String,String> umConfirmClaimMap = new HashMap<>();
	static
	{
		umConfirmClaimMap.put(CONFIRM_STATE_AGREE,"已与客户达成一致");
		umConfirmClaimMap.put(CONFIRM_STATE_CONFIRM_DISAGREE,"未与客户达成一致");
	}

	public static final String EMPTY_STRING = "";

	public static final String AND = "and";

	public static final String OR = "or";

	public static final String YES = "Y";

	public static final String NO = "N";

	public static final String SYSTEM = "SYSTEM";

	public static final String SYSTEM_UM = "SYSTEM";

	public static final String SYSTEM_NAME = "SYSTEM";


	public static final String UPLOAD_BY_PC = "11";


	public static final String ZERO_STR = "0";

	public static final String AUDIT_AGREE_CODE = "1";

	public static final String AUDIT_DISAGREE_CODE = "2";


	public static final Map<String, String> formulaTypeMap = new HashMap<String, String>();
	static {
		formulaTypeMap.put("01", "责任理算计算公式");
		formulaTypeMap.put("02", "限额控制公式");
	}

	public static final Map<String, String> INSURED_ATTRIBUTE_MAP = new HashMap<String, String>();
	static {
		INSURED_ATTRIBUTE_MAP.put("0", "团体");
		INSURED_ATTRIBUTE_MAP.put("1", "个人");
	}

	public static final MathContext mc = new MathContext(20, RoundingMode.HALF_UP);

	public static final String SUCCESS = "成功";
	public static final String FAIL = "失败";

	public static final String ERROR = "ERROR";

	public static final String REPORT_NO = "报案号";

	public static final String CASE_TIMES = "赔付次数";

	public static final String  INVESTIGATE_OTHER = "ASM_2007,ASM_2027,ASM_2020,ASM_2013";

	public static final String ACCIDENT_OUTPATIENT_ALLOWANCE = "601";

	public static final String ACCIDENT_HOSPITAL_ALLOWANCE = "602";

	public static final String CONCLUSION_PROTOCOL_VERIFY_PAY = "15";

	public static final String CONCLUSION_ACCOMMODATION_VERIFY_PAY = "16";


	public static final String TIME_UNIT_MIN = "min";

	public static final String TASK_SPONSOR_UM = "taskSponsorUm";

	public static final String TASK_DISPATH_UM = "taskDispatchUm";

	public static final Set<String> GROUP_OLDTONEW_PRO_CODE_SET = new HashSet<String>();
	static {
		GROUP_OLDTONEW_PRO_CODE_SET.add("MP18000024");
		GROUP_OLDTONEW_PRO_CODE_SET.add("MP18000026");
	}

	public static final Map<String,String> CERTIFICATE_NO_TYPE_MAP = new HashMap<>();
	static {
		CERTIFICATE_NO_TYPE_MAP.put("01","身份证");
		CERTIFICATE_NO_TYPE_MAP.put("02","护照");
		CERTIFICATE_NO_TYPE_MAP.put("03","军人证");
		CERTIFICATE_NO_TYPE_MAP.put("04","港澳通行证");
		CERTIFICATE_NO_TYPE_MAP.put("05","驾驶证");
		CERTIFICATE_NO_TYPE_MAP.put("06","港澳回乡证或台胞证");
		CERTIFICATE_NO_TYPE_MAP.put("07","临时身份证");
		CERTIFICATE_NO_TYPE_MAP.put("08","外国人永久居留身份证");
		CERTIFICATE_NO_TYPE_MAP.put("99","其他");
	}

	public static final String CASE_STATUS_END = "0";
	public static final String ZERO_PAY = "2";
	public static final String WHOLE_CASE_EXCLUSION = "4";
	public static final String CASE_CANCELL = "5";

	public static final String CLAIM_TYPE_PAY = "1";

	public static final String BILL_FEE_DOC = "009002";

	public static final String CASECLASS_PEOPLE_HURT = "1";

	public static final String CASECLASS_NO_PEOPLE_HURT = "2";

	public static final String CASECLASS_NO_PEOPLE_HURT_SX = "5";

	public static final String CITY_TYPE_CITY = "city";

	/**
	 * SCENE20022("20022", "续期抽档")
	 */
	public static final String SCENE20022 = "20022";

	public static final String ONE_STR = "1";

}
