package com.paic.ncbs.claim.model.dto.report;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReportAhcsBaseInfoDTO extends ReportBaseInfoDTO {

    private static final long serialVersionUID = 1L;
    private String reportNo;
    private String casetimes;
    private String accidentTypeName;
    private String reporterName;
    private String relationWithReport;
    private String relationWithReportName;
    private List<LinkManDTO> linkManList = new ArrayList<LinkManDTO>();
    private String reporterCallNo;
    private String reporterRegisterTel;
    private String reportMode;
    private String reportModeName;
    private String reportregisterUm;
    private String remark;
    private String reportRemark;
    private String accidentDetail;
    private String accidentPlace;
    private String isSelfHelp;
    private String isStandard;
    private Date reportDate;
    private Date accidentDate;
    private String isAbnormal;
    private String certificateNo;
    private String certificateType;
    private String certificateTypeName;
    private String accidentPersonName;
    private Date birthday;
    private String departmentCode;
    private String departmentName;
    private String policyNo;
    private String electronicNo;
    private String isVirtual;
    private String reportType;
    private String name;
    private String reasonDesc;
    private String reasonCode;
    private String abnormalCaseStatus;
    private String partnerCode;
    private List<String> remarks;
    private String isRepeatReport;
    private boolean isShowRepeatInfo;
    private List<String> RepeatReportNoList;
    private String personnelAttribute = "";
    /**
     * 出险原因大类
     */
    private String accidentCauseLevel1;
    /**
     * 出险原因细类
     */
    private String accidentCauseLevel2;
    /**
     * 是否有风险标识：Y-是，N-否:TPA调用立案接口会传入
     */
    private String riskFlag;
    /**
     * 风险描述备注：是否有风险标识 未Y时必填 TPA调用立案接口时传入
     */
    private String riskRemark;

    public String getOverseasOccur() {
        return overseasOccur;
    }

    public void setOverseasOccur(String overseasOccur) {
        this.overseasOccur = overseasOccur;
    }

    public String getAccidentArea() {
        return accidentArea;
    }

    public void setAccidentArea(String accidentArea) {
        this.accidentArea = accidentArea;
    }

    public String getAccidentNation() {
        return accidentNation;
    }

    public void setAccidentNation(String accidentNation) {
        this.accidentNation = accidentNation;
    }

    private String overseasOccur ;
    private String accidentArea ;
    private String accidentNation ;

    private String provinceCode;
    private String   accidentCityCode;
    private String accidentCountyCode;
    private String lossClass;
    /**
     * 出险原因大类
     */
    private String accidentCauseLevel1Code;
    /**
     * 出险原因细类
     */
    private String accidentCauseLevel2Code;
    public void setAccidentCauseLevel1Code(String accidentCauseLevel1Code) {
        this.accidentCauseLevel1Code = accidentCauseLevel1Code;
    }
    public String getAccidentCauseLevel1Code() {
        return accidentCauseLevel1Code;
    }
    public void setAccidentCauseLevel2Code(String accidentCauseLevel2Code) {
        this.accidentCauseLevel2Code = accidentCauseLevel2Code;
    }
    public String getAccidentCauseLevel2Code() {
        return accidentCauseLevel2Code;
    }
    public String getLossClass() {
        return lossClass;
    }
    public void setLossClass(String lossClass) {
        this.lossClass = lossClass;
    }
    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getAccidentCityCode() {
        return accidentCityCode;
    }

    public void setAccidentCityCode(String accidentCityCode) {
        this.accidentCityCode = accidentCityCode;
    }

    public String getAccidentCountyCode() {
        return accidentCountyCode;
    }

    public void setAccidentCountyCode(String accidentCountyCode) {
        this.accidentCountyCode = accidentCountyCode;
    }

    private String isDirectCompensation;

    public final String getReportNo() {
        return reportNo;
    }

    public final void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public final String getCasetimes() {
        return casetimes;
    }

    public final void setCasetimes(String casetimes) {
        this.casetimes = casetimes;
    }

    public final String getAccidentTypeName() {
        return accidentTypeName;
    }

    public final void setAccidentTypeName(String accidentTypeName) {
        this.accidentTypeName = accidentTypeName;
    }

    public final String getReporterName() {
        return reporterName;
    }

    public final void setReporterName(String reporterName) {
        this.reporterName = reporterName;
    }

    public final String getRelationWithReport() {
        return relationWithReport;
    }

    public final void setRelationWithReport(String relationWithReport) {
        this.relationWithReport = relationWithReport;
    }

    public final String getRelationWithReportName() {
        return relationWithReportName;
    }

    public final void setRelationWithReportName(String relationWithReportName) {
        this.relationWithReportName = relationWithReportName;
    }

    public final List<LinkManDTO> getLinkManList() {
        return linkManList;
    }

    public final void setLinkManList(List<LinkManDTO> linkManList) {
        this.linkManList = linkManList;
    }

    public final String getReporterCallNo() {
        return reporterCallNo;
    }

    public final void setReporterCallNo(String reporterCallNo) {
        this.reporterCallNo = reporterCallNo;
    }

    public final String getReporterRegisterTel() {
        return reporterRegisterTel;
    }

    public final void setReporterRegisterTel(String reporterRegisterTel) {
        this.reporterRegisterTel = reporterRegisterTel;
    }

    public final String getReportMode() {
        return reportMode;
    }

    public final void setReportMode(String reportMode) {
        this.reportMode = reportMode;
    }

    public final String getReportModeName() {
        return reportModeName;
    }

    public final void setReportModeName(String reportModeName) {
        this.reportModeName = reportModeName;
    }

    public final String getReportregisterUm() {
        return reportregisterUm;
    }

    public final void setReportregisterUm(String reportregisterUm) {
        this.reportregisterUm = reportregisterUm;
    }

    public final String getRemark() {
        return remark;
    }

    public final void setRemark(String remark) {
        this.remark = remark;
    }

    public final String getReportRemark() {
        return reportRemark;
    }

    public final void setReportRemark(String reportRemark) {
        this.reportRemark = reportRemark;
    }

    public final String getAccidentDetail() {
        return accidentDetail;
    }

    public final void setAccidentDetail(String accidentDetail) {
        this.accidentDetail = accidentDetail;
    }

    public final String getAccidentPlace() {
        return accidentPlace;
    }

    public final void setAccidentPlace(String accidentPlace) {
        this.accidentPlace = accidentPlace;
    }

    public final String getIsSelfHelp() {
        return isSelfHelp;
    }

    public final void setIsSelfHelp(String isSelfHelp) {
        this.isSelfHelp = isSelfHelp;
    }

    public String getIsStandard() {
        return isStandard;
    }

    public void setIsStandard(String isStandard) {
        this.isStandard = isStandard;
    }

    public final Date getReportDate() {
        return reportDate;
    }

    public final void setReportDate(Date reportDate) {
        this.reportDate = reportDate;
    }

    public final Date getAccidentDate() {
        return accidentDate;
    }

    public final void setAccidentDate(Date accidentDate) {
        this.accidentDate = accidentDate;
    }

    public final String getIsAbnormal() {
        return isAbnormal;
    }

    public final void setIsAbnormal(String isAbnormal) {
        this.isAbnormal = isAbnormal;
    }

    public final String getCertificateNo() {
        return certificateNo;
    }

    public final void setCertificateNo(String certificateNo) {
        this.certificateNo = certificateNo;
    }

    public final String getCertificateType() {
        return certificateType;
    }

    public final void setCertificateType(String certificateType) {
        this.certificateType = certificateType;
    }

    public final String getCertificateTypeName() {
        return certificateTypeName;
    }

    public final void setCertificateTypeName(String certificateTypeName) {
        this.certificateTypeName = certificateTypeName;
    }

    public final String getAccidentPersonName() {
        return accidentPersonName;
    }

    public final void setAccidentPersonName(String accidentPersonName) {
        this.accidentPersonName = accidentPersonName;
    }

    public final Date getBirthday() {
        return birthday;
    }

    public final void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public final String getDepartmentCode() {
        return departmentCode;
    }

    public final void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public final String getDepartmentName() {
        return departmentName;
    }

    public final void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public final String getPolicyNo() {
        return policyNo;
    }

    public final void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public final String getElectronicNo() {
        return electronicNo;
    }

    public final void setElectronicNo(String electronicNo) {
        this.electronicNo = electronicNo;
    }

    public final String getIsVirtual() {
        return isVirtual;
    }

    public final void setIsVirtual(String isVirtual) {
        this.isVirtual = isVirtual;
    }

    public final String getReportType() {
        return reportType;
    }

    public final void setReportType(String reportType) {
        this.reportType = reportType;
    }

    public final String getName() {
        return name;
    }

    public final void setName(String name) {
        this.name = name;
    }

    public final String getReasonDesc() {
        return reasonDesc;
    }

    public final void setReasonDesc(String reasonDesc) {
        this.reasonDesc = reasonDesc;
    }

    public final String getReasonCode() {
        return reasonCode;
    }

    public final void setReasonCode(String reasonCode) {
        this.reasonCode = reasonCode;
    }

    public final String getAbnormalCaseStatus() {
        return abnormalCaseStatus;
    }

    public final void setAbnormalCaseStatus(String abnormalCaseStatus) {
        this.abnormalCaseStatus = abnormalCaseStatus;
    }

    public final List<String> getRemarks() {
        return remarks;
    }

    public final void setRemarks(List<String> remarks) {
        this.remarks = remarks;
    }

    public final String getPartnerCode() {
        return partnerCode;
    }

    public final void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }

    public String getIsRepeatReport() {
        return isRepeatReport;
    }

    public void setIsRepeatReport(String isRepeatReport) {
        this.isRepeatReport = isRepeatReport;
    }

    public boolean isShowRepeatInfo() {
        return isShowRepeatInfo;
    }

    public void setShowRepeatInfo(boolean isShowRepeatInfo) {
        this.isShowRepeatInfo = isShowRepeatInfo;
    }

    public List<String> getRepeatReportNoList() {
        return RepeatReportNoList;
    }

    public void setRepeatReportNoList(List<String> repeatReportNoList) {
        RepeatReportNoList = repeatReportNoList;
    }

    public String getIsDirectCompensation() {
        return isDirectCompensation;
    }

    public void setIsDirectCompensation(String isDirectCompensation) {
        this.isDirectCompensation = isDirectCompensation;
    }

    public String getPersonnelAttribute() {
        return personnelAttribute;
    }

    public void setPersonnelAttribute(String personnelAttribute) {
        this.personnelAttribute = personnelAttribute;
    }

    public String getAccidentCauseLevel1() {
        return accidentCauseLevel1;
    }

    public void setAccidentCauseLevel1(String accidentCauseLevel1) {
        this.accidentCauseLevel1 = accidentCauseLevel1;
    }

    public String getAccidentCauseLevel2() {
        return accidentCauseLevel2;
    }

    public void setAccidentCauseLevel2(String accidentCauseLevel2) {
        this.accidentCauseLevel2 = accidentCauseLevel2;
    }

    public String getRiskFlag() {
        return riskFlag;
    }

    public void setRiskFlag(String riskFlag) {
        this.riskFlag = riskFlag;
    }

    public String getRiskRemark() {
        return riskRemark;
    }

    public void setRiskRemark(String riskRemark) {
        this.riskRemark = riskRemark;
    }
}
