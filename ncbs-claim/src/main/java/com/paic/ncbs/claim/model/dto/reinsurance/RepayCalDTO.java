package com.paic.ncbs.claim.model.dto.reinsurance;

import com.paic.ncbs.claim.common.enums.ReinsuranceClaimTypeEnum;
import lombok.Data;

/**
 * 理赔环节送再保
 */
@Data
public class RepayCalDTO {

    /**
     * 报案号
     */
    private String reportNo;

    /**
     * 赔付次数
     */
    private Integer caseTimes;

    /**
     * 理赔业务环节
     */
    private ReinsuranceClaimTypeEnum claimType;

    /**
     * 赔付结论（已结案必传）
     */
    private String indemnityConclusion;

    /**
     * 未决历史和修正表上一次id
     */
    private String oldIdFlagHistoryChange;

    /**
     * 案件场景，暂时只会赋值 01-退运险
     */
    private String caseScene;
    /**
     * 追偿的支付次数
     */
    private Integer subTimes;
    /**
     * 追偿的支付方式 1H-追偿 1HJ-费用
     */
    private String paymentType;


}
