package com.paic.ncbs.claim.controller.sop;

import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.sop.SopFileVO;
import com.paic.ncbs.claim.service.sop.SopFileService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * SOP文件管理Controller
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Api(tags = "SOP文件管理")
@RestController
@RequestMapping("/mng/app/sopFileAction")
@Slf4j
public class SopFileController extends BaseController {

    @Autowired
    private SopFileService sopFileService;

    @ApiOperation("根据SOP主键获取文件列表")
    @GetMapping(value = "/getFileList/{idSopMain}")
    @ApiImplicitParam(name = "idSopMain", value = "SOP主键", required = true, dataType = "String", paramType = "path")
    public ResponseResult<List<SopFileVO>> getFileList(@PathVariable("idSopMain") String idSopMain) {
        try {
            LogUtil.audit("根据SOP主键获取文件列表，idSopMain：{}", idSopMain);
            List<SopFileVO> result = sopFileService.getFileListByIdSopMain(idSopMain);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("根据SOP主键获取文件列表失败", e);
            throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("上传SOP文件")
    @PostMapping(value = "/uploadFiles/{idSopMain}")
    @ApiImplicitParam(name = "idSopMain", value = "SOP主键", required = true, dataType = "String", paramType = "path")
    public ResponseResult<List<SopFileVO>> uploadFiles(@PathVariable("idSopMain") String idSopMain,
                                                       @RequestParam("files") MultipartFile[] files) {
        try {
            LogUtil.audit("上传SOP文件，idSopMain：{}，文件数量：{}", idSopMain, files != null ? files.length : 0);
            List<SopFileVO> result = sopFileService.uploadSopFiles(idSopMain, files);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("上传SOP文件失败", e);
            throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("删除SOP文件")
    @DeleteMapping(value = "/deleteFile/{idSopFile}")
    @ApiImplicitParam(name = "idSopFile", value = "文件主键", required = true, dataType = "String", paramType = "path")
    public ResponseResult<String> deleteFile(@PathVariable("idSopFile") String idSopFile) {
        try {
            LogUtil.audit("删除SOP文件，idSopFile：{}", idSopFile);
            String result = sopFileService.deleteSopFile(idSopFile);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("删除SOP文件失败", e);
            throw new GlobalBusinessException(e.getMessage());
        }
    }

    @ApiOperation("获取文件下载地址")
    @GetMapping(value = "/getDownloadUrl/{idSopFile}")
    @ApiImplicitParam(name = "idSopFile", value = "文件主键", required = true, dataType = "String", paramType = "path")
    public ResponseResult<String> getDownloadUrl(@PathVariable("idSopFile") String idSopFile) {
        try {
            LogUtil.audit("获取文件下载地址，idSopFile：{}", idSopFile);
            String result = sopFileService.getFileDownloadUrl(idSopFile);
            return ResponseResult.success(result);
        } catch (Exception e) {
            log.error("获取文件下载地址失败", e);
            throw new GlobalBusinessException(e.getMessage());
        }
    }

}
