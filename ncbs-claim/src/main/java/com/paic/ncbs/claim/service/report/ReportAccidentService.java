package com.paic.ncbs.claim.service.report;

import com.paic.ncbs.claim.dao.entity.report.ReportAccidentEntity;
import com.paic.ncbs.claim.service.base.BaseService;

public interface ReportAccidentService extends BaseService<ReportAccidentEntity> {

	ReportAccidentEntity getReportAccident(String reportNo);

    void updateAccidentReasonByReportNo(String reportNo, String accidentCauseLevel1, String accidentCauseLevel2, String loginUm);
}
