<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.dashboard.JobDashboardMapper">

    <resultMap id="NumAmountMap" type="com.paic.ncbs.claim.dao.entity.dashboard.JobDashBoard">
        <result column="num" property="num" />
        <result column="amount" property="amount" />
    </resultMap>

    <!-- 接报案数 -->
    <select id="reportInfo" parameterType="map" resultType="int">
        select
            count(distinct report_no) num
        from clms_task_info
        where
            ASSIGNER = #{userCode}
            and created_date between #{startTime} and #{endTime}
    </select>

    <!-- 立案估损（件数+新估损金额） -->
    <select id="claimInfo" parameterType="map" resultMap="NumAmountMap">
        select
            count(distinct report_no) num,
            ifnull(sum(register_amount),0) amount
        from clms_case_register_apply
        where
            audit_opinion='1'
            and apply_um=#{userCode}
            and audit_date between #{startTime} and #{endTime}
    </select>

    <!-- 结案（结案案件数） -->
    <select id="endCaseNum" parameterType="map" resultType="int">
        select
            count(*) num
        from clm_whole_case_base b
        where case_times = 1
            and case_finisher_um = #{userCode}
            and end_case_date between #{startTime} and #{endTime}
    </select>

    <!-- 结案（结案金额） -->
    <select id="endCaseAmount" parameterType="map" resultType="java.math.BigDecimal">
        with p1 as(
            select
                ifnull(sum(policy_sum_pay),0) policy_sum_pay
            from
                clm_policy_pay pay
            where exists (select 1 from clm_whole_case_base b
                        where pay.report_no=b.report_no
                        and pay.case_times=b.case_times
                        and b.indemnity_conclusion = '1'
                        and case_finisher_um = #{userCode}
                        and end_case_date between #{startTime} and #{endTime})),
        p2 as(
            select
                ifnull(sum(fee_amount),0) fee_not_paid
            from clms_fee_pay fee
            where exists (select 1 from clm_whole_case_base b
                          where fee.report_no=b.report_no
                            and fee.case_times=b.case_times
                            and b.indemnity_conclusion in ('2','4','5','6')
                            and case_finisher_um = #{userCode}
                            and end_case_date between #{startTime} and #{endTime}))
        select policy_sum_pay+fee_not_paid amount from p1, p2
    </select>

    <!-- 估损调整（件数+调整金额） -->
    <select id="estimateInfo" parameterType="map" resultMap="NumAmountMap">
        with my_final_adjust as (
            select
                report_no,
                change_amount,
                created_date
            from (select
                    report_no,
                    change_amount,
                    created_date,
                    row_number() over(partition by report_no order by created_date desc) _r
                from
                    clms_estimate_change
                where case_times=1
                    and user_id=#{userCode}
                    and change_date between #{startTime} and #{endTime} ) my_adjust
            where _r = 1
        ),
        unchanged as (
            select report_no
            from
                clms_estimate_change cec
            where exists (select 1 from my_final_adjust rs where cec.report_no=rs.report_no)
            group by report_no
            having count(*)=1
        ),
        adjusted as (
            select
                row_number() over(partition by report_no order by created_date desc) _r,
                cec.report_no,
                change_amount
            from
                clms_estimate_change cec
            where exists(select 1 from my_final_adjust rs where cec.report_no=rs.report_no and cec.created_date &lt; rs.created_date)
                and not exists(select 1 from unchanged unc where cec.report_no=unc.report_no)
                and case_times=1
        )
        select
            count(*) num,
            ifnull(sum(r1.change_amount-r2.change_amount),0) amount
        from
            my_final_adjust r1,
            adjusted r2
        where
            r1.report_no = r2.report_no
            and  r2._r=1
    </select>

    <!-- 零注拒件数 -->
    <select id="zeroCancelRefuseNum" parameterType="map" resultType="int">
        with p1 as(
            select
                count(distinct report_no) ct
            from
                clms_zero_cancel_apply
            where VERIFY_OPTIONS ='1'
                and verify_um=#{userCode}
                and verify_date between #{startTime} and #{endTime}
        ),
        p2 as(
            select
                count(distinct report_no) ct
            from clms_claim_rejection_approval_record
            where audit_opinion ='1'
               and initiator_um=#{userCode}
               and audit_date between #{startTime} and #{endTime}
        )
        select p1.ct+p2.ct num from p1, p2
    </select>

    <!-- 差错件数 -->
    <select id="mistakeNum" parameterType="map" resultType="int">
        with my_verify as(
            select
                report_no,
                case_times
            from
                (select
                    report_no,
                    case_times,
                    settle_um,
                    row_number() over(partition by report_no,case_times order by created_date) _r
                from clms_verify
                where updated_date between #{startTime} and #{endTime}
                ) t
            where t._r = 1
                and t.settle_um = #{userCode}
        )
        select count(*) num
        from
            clms_verify v
        where exists (select 1 from my_verify mv where v.report_no = mv.report_no and v.case_times = mv.case_times)
            and exists(select 1
                        from clms_mistake_record cmr
                        where v.report_no=cmr.report_no
                        and v.case_times=cmr.case_times
                        and abs(TIMESTAMPDIFF(SECOND, v.VERIFY_DATE, cmr.created_date))&lt;1
                        and cmr.RECORD_TASK_ID ='OC_MANUAL_SETTLE'
                        and cmr.MISTAKE_CODE in('CheckDuty_02','CheckDuty_03','CheckDuty_04','Settle_01','Settle_02','Settle_03','Settle_04'))
            and v.VERIFY_CONCLUSION ='3'
    </select>

    <!-- 重开（案件数+估损金额） -->
    <select id="reopenCaseInfo" parameterType="map" resultMap="NumAmountMap">
        with my_reopen_case as(
            select
                cer.report_no,
                cer.case_times,
                cer.estimate_amount
            from
                clm_restart_case_record crcr,
                clms_estimate_record cer
            where
                crcr.report_no=cer.REPORT_NO
                and crcr.case_times+1=cer.case_times
                and crcr.APPROVAL_OPINIONS='0'
                and cer.estimate_type='02'
                and crcr.created_by= #{userName}
                and crcr.created_date between #{startTime} and #{endTime}
        ),
        first_tm as(
            select
                cer.report_no,
                cer.case_times,
                cer.estimate_amount,
                row_number() over(partition by cer.report_no order by created_date desc) _r
            from clms_estimate_record cer
            where exists(select 1 from my_reopen_case mrc where cer.report_no = mrc.report_no)
                and cer.case_times=1
                and cer.estimate_type='02'
        ),
        all_data as(
            select report_no, case_times, estimate_amount from first_tm where _r=1
            union all
            select report_no, case_times, estimate_amount
            from clms_estimate_record cer
            where exists(select 1 from my_reopen_case mrc where cer.report_no = mrc.report_no)
            and cer.case_times > 1
            and cer.estimate_type='02'
        ),
        sorted_data as(
            select
                report_no,
                case_times,
                estimate_amount,
                lag(estimate_amount, 1) over (partition by report_no order by case_times) last_estimate_amount
            from all_data
        )
        select
            count(*) num,
            ifnull(sum(s.estimate_amount-s.last_estimate_amount),0) amount
        from my_reopen_case my, sorted_data s
        where
            my.report_no=s.report_no
            and my.case_times = s.case_times
    </select>

    <!-- 核赔（案件数+核赔金额） -->
    <select id="verifyInfo" parameterType="map" resultMap="NumAmountMap">
        with my_verify as(
            select
                cwcb.report_no,
                cwcb.case_times
            from
                clm_whole_case_base cwcb,
                clms_verify cv
            where cwcb.report_no = cv.REPORT_NO
                and cwcb.case_times = cv.case_times
                and cv.verify_un= #{userCode}
                and cwcb.end_case_date between #{startTime} and #{endTime}
        ),
        all_data as(
            select
                report_no,
                case_times,
                ifnull(sum(policy_pay) - lag(sum(policy_pay), 1) over(partition by report_no order by case_times), sum(policy_pay)) substract
            from clm_policy_pay pay
            where exists (select 1 from my_verify v where pay.report_no=v.report_no)
            group by report_no, case_times
        )
        select
            count(*) num,
            ifnull(sum(substract),0) amount
        from
            all_data a
        where exists(select 1 from my_verify m
                    where a.report_no = m.report_no
                    and a.case_times = m.case_times)
    </select>

</mapper>
