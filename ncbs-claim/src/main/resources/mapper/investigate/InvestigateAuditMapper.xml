<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.investigate.InvestigateAuditMapper">

	<resultMap type="com.paic.ncbs.claim.model.dto.investigate.InvestigateAuditDTO" id="InvestigateAuditMap">
		<id property="idAhcsInvestigateAudit" column="ID_AHCS_INVESTIGATE_AUDIT" />
		<result property="createdBy" column="CREATED_BY" />
		<result property="createdDate" column="CREATED_DATE" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="updatedDate" column="UPDATED_DATE" />
		<result property="idAhcsInvestigate" column="ID_AHCS_INVESTIGATE" />
		<result property="initiatorUm" column="INITIATOR_UM" />
		<result property="auditorUm" column="AUDITOR_UM" />
		<result property="auditOpinion" column="AUDIT_OPINION" />
		<result property="auditType" column="AUDIT_TYPE" />
		<result property="remark" column="REMARK" />
		<result property="auditReply" column="AUDIT_REPLY" />
		<result property="investigateDepartment" column="INVESTIGATE_DEPARTMENT" />
		<result property="surveyType" column="SURVEY_TYPE" />
		<result property="socialCreditCode" column="SOCIAL_CREDIT_CODE" />
		<result property="companyName" column="COMPANY_NAME" />
	</resultMap>
	
	
	<resultMap type="com.paic.ncbs.claim.model.vo.investigate.InvestigateAuditVO" extends="InvestigateAuditMap" id="InvestigateAuditVOMap">
		
		<result property="initiatorUmName" column="INITIATOR_UM_NAME" />
		<result property="auditorUmName" column="AUDITOR_UM_NAME" />
		<result property="investigateDepartmentName" column="INVESTIGATE_DEPARTMENT_NAME" />
	</resultMap>
	

	<resultMap type="com.paic.ncbs.claim.model.vo.investigate.InvestigateVO" id="InvestigateVOMap">
		<result property="sendBackMan" column="SEND_BACK_MAN" />
		<result property="sendBackReason" column="SEND_BACK_REASON" />
		<result property="sendBackTime" column="SEND_BACK_TIME" />
	</resultMap>
	

	<insert id="addInvestigateAudit" parameterType="com.paic.ncbs.claim.model.dto.investigate.InvestigateAuditDTO" >
		INSERT INTO CLMS_INVESTIGATE_AUDIT (
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_INVESTIGATE_AUDIT,
			ID_AHCS_INVESTIGATE,
			INITIATOR_UM,
			AUDITOR_UM,
			AUDIT_OPINION,
			AUDIT_TYPE,
			REMARK,
			AUDIT_REPLY,
			INVESTIGATE_DEPARTMENT,
			INITIATOR_UM_name,
			AUDITOR_UM_name,
			INVESTIGATE_DEPARTMENT_name,
			ARCHIVE_TIME,
			SURVEY_TYPE ,
			SOCIAL_CREDIT_CODE,
			COMPANY_NAME
		)
	VALUES (
			#{createdBy ,jdbcType=VARCHAR},
			now(),
			#{updatedBy ,jdbcType=VARCHAR},
			now(),
			#{idAhcsInvestigateAudit ,jdbcType=VARCHAR},
			#{idAhcsInvestigate ,jdbcType=VARCHAR},
			#{initiatorUm ,jdbcType=VARCHAR},
			#{auditorUm ,jdbcType=VARCHAR},
			#{auditOpinion ,jdbcType=VARCHAR},
			#{auditType ,jdbcType=VARCHAR},
			#{remark ,jdbcType=VARCHAR},
			#{auditReply ,jdbcType=VARCHAR},
			#{investigateDepartment ,jdbcType=VARCHAR},
			(select user_name from CLMS_user_info t where user_id=#{initiatorUm ,jdbcType=VARCHAR}),
			(select user_name from CLMS_user_info t where user_id=#{auditorUm ,jdbcType=VARCHAR}),
			(select department_abbr_name from department_define  where department_code=#{investigateDepartment ,jdbcType=VARCHAR}),
			now(),
			#{surveyType ,jdbcType=VARCHAR},
			#{socialCreditCode ,jdbcType=VARCHAR},
			#{companyName ,jdbcType=VARCHAR}
	)
	</insert>



	<update id="modifyInvestigateAudit" parameterType="com.paic.ncbs.claim.model.dto.investigate.InvestigateAuditDTO">
		UPDATE CLMS_INVESTIGATE_AUDIT
		<set>
			<if test="updatedBy != null and updatedBy != '' ">
				UPDATED_BY  = #{updatedBy},
			</if>
			UPDATED_DATE = now(),

			<if test="initiatorUm != null and initiatorUm != '' ">
				INITIATOR_UM  = #{initiatorUm},
				INITIATOR_UM_name  = (select user_name from CLMS_user_info t where user_id=#{initiatorUm}),
			</if>

			<if test="auditorUm != null and auditorUm != '' ">
				AUDITOR_UM = #{auditorUm},
				AUDITOR_UM_name = (select user_name from CLMS_user_info t where user_id=#{auditorUm}),
			</if>

			<if test="auditOpinion != null and auditOpinion != '' ">
				AUDIT_OPINION  = #{auditOpinion},
			</if>

			<if test="auditType != null and auditType != '' ">
				AUDIT_TYPE  = #{auditType},
			</if>

			<if test="remark != null and remark != '' ">
				REMARK  = #{remark},
			</if>

			<if test="auditReply != null and auditReply != '' ">
				AUDIT_REPLY  = #{auditReply},
			</if>
			<if test="surveyType != null and surveyType != '' ">
				SURVEY_TYPE  = #{surveyType},
			</if>
			<if test="socialCreditCode != null and socialCreditCode != '' ">
				SOCIAL_CREDIT_CODE  = #{socialCreditCode},
			</if>
			<if test="companyName != null and companyName != '' ">
				COMPANY_NAME  = #{companyName},
			</if>
		</set>
		WHERE ID_AHCS_INVESTIGATE_AUDIT = #{idAhcsInvestigateAudit}
	</update>


	<select id="getInvestigateAuditByTaskId" resultMap="InvestigateAuditVOMap">
		select 
			t.CREATED_BY,
			t.CREATED_DATE,
			t.UPDATED_BY,
			t.UPDATED_DATE,
			t.ID_AHCS_INVESTIGATE_AUDIT,
			t.ID_AHCS_INVESTIGATE,
			t.INITIATOR_UM,
			t.AUDITOR_UM,
			t.AUDIT_OPINION,
			t.AUDIT_TYPE,
			t.REMARK,
			t.AUDIT_REPLY,
			t.INVESTIGATE_DEPARTMENT,
			t.INITIATOR_UM INITIATOR_UM_NAME,
		    AUDITOR_UM_NAME,
		    (select concat(a.department_code,'-',a.department_abbr_name)  from department_define a where a.department_code=t.INVESTIGATE_DEPARTMENT limit 1) as INVESTIGATE_DEPARTMENT_NAME
			
		from CLMS_INVESTIGATE_AUDIT t
		where  ID_AHCS_INVESTIGATE_AUDIT=
		(select a.ID_AHCS_INVESTIGATE_AUDIT from CLMS_INVESTIGATE_TASK a where a.ID_AHCS_INVESTIGATE_TASK=#{idAhcsInvestigateTask})
	</select>
	

	<select id="getInvestigateAuditByInvestigateId" resultMap="InvestigateAuditVOMap">
		select 
			t.CREATED_BY,
			t.CREATED_DATE,
			t.UPDATED_BY,
			t.UPDATED_DATE,
			t.ID_AHCS_INVESTIGATE_AUDIT,
			t.ID_AHCS_INVESTIGATE,
			t.INITIATOR_UM,
			t.AUDITOR_UM,
			t.AUDIT_OPINION,
			t.AUDIT_TYPE,
			t.REMARK,
			t.AUDIT_REPLY,
			t.INVESTIGATE_DEPARTMENT,
			IFNULL((select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.INITIATOR_UM limit 1),t.INITIATOR_UM) as INITIATOR_UM_NAME,
		    IFNULL((select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.AUDITOR_UM limit 1),t.AUDITOR_UM) as AUDITOR_UM_NAME,
		    (select concat(a.department_code,'-',a.department_abbr_name) from department_define a where a.department_code=t.INVESTIGATE_DEPARTMENT limit 1) as INVESTIGATE_DEPARTMENT_NAME,
			t.SURVEY_TYPE,
			t.SOCIAL_CREDIT_CODE,
		    t.COMPANY_NAME
		from CLMS_INVESTIGATE_AUDIT t
		where 
		  AUDIT_TYPE = '01'  and audit_opinion is null
		  and ID_AHCS_INVESTIGATE=#{idAhcsInvestigate} 
	</select>
	
	

	<select id="getInvestigateMajorAuditByInvestigateId" resultMap="InvestigateAuditVOMap">
		select 
			t.CREATED_BY,
			t.CREATED_DATE,
			t.UPDATED_BY,
			t.UPDATED_DATE,
			t.ID_AHCS_INVESTIGATE_AUDIT,
			t.ID_AHCS_INVESTIGATE,
			t.INITIATOR_UM,
			t.AUDITOR_UM,
			t.AUDIT_OPINION,
			t.AUDIT_TYPE,
			t.REMARK,
			t.AUDIT_REPLY,
			t.INVESTIGATE_DEPARTMENT
		from CLMS_INVESTIGATE_AUDIT t
		where 
		  AUDIT_TYPE = '02' 
		  and ID_AHCS_INVESTIGATE=#{idAhcsInvestigate} 
	</select>
	

	<select id="getInvestigateAuditById" resultMap="InvestigateAuditVOMap">
		select 
			t.CREATED_BY,
			t.CREATED_DATE,
			t.UPDATED_BY,
			t.UPDATED_DATE,
			t.ID_AHCS_INVESTIGATE_AUDIT,
			t.ID_AHCS_INVESTIGATE,
			t.INITIATOR_UM,
			t.AUDITOR_UM,
			t.AUDIT_OPINION,
			t.AUDIT_TYPE,
			t.REMARK,
			t.AUDIT_REPLY,
			t.INVESTIGATE_DEPARTMENT,
			(select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.INITIATOR_UM limit 1) as INITIATOR_UM_NAME,
		    (select concat(user_name,'-',user_id) from CLMS_user_info  where user_id=t.AUDITOR_UM limit 1) as AUDITOR_UM_NAME,
		    (select concat(a.department_code,'-',a.department_abbr_name) from department_define a where a.department_code=t.INVESTIGATE_DEPARTMENT limit 1) as INVESTIGATE_DEPARTMENT_NAME
			
		from CLMS_INVESTIGATE_AUDIT t
		where  ID_AHCS_INVESTIGATE_AUDIT=#{idAhcsInvestigateAudit} 
	</select>
	
	

	<select id="getInvestigateAuditForBack" resultMap="InvestigateVOMap">
		select 

		t.UPDATED_DATE SEND_BACK_TIME,

		t.audit_reply SEND_BACK_REASON,

		IFNULL((select  concat(user_name,'-',user_id)  from CLMS_user_info  where user_id=t.auditor_um limit 1),t.auditor_um) as SEND_BACK_MAN

		from CLMS_investigate_audit t
		where t.id_ahcs_investigate=#{idAhcsInvestigate} 
		and t.audit_type='02' 
		and t.audit_opinion='调查退回' 
	</select>
	
	
</mapper>