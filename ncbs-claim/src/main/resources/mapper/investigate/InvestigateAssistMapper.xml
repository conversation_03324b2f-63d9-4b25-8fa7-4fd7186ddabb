<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.investigate.InvestigateAssistMapper">

	<resultMap type="com.paic.ncbs.claim.model.dto.investigate.InvestigateAssistDTO" id="InvestigateAssistMap">
		<id property="idAhcsInvestigateAssist" column="ID_AHCS_INVESTIGATE_ASSIST" />
		<result property="createdBy" column="CREATED_BY" />
		<result property="createdDate" column="CREATED_DATE" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="updatedDate" column="UPDATED_DATE" />
		<result property="idAhcsInvestigateTask" column="ID_AHCS_INVESTIGATE_TASK" />
		<result property="initiatorUm" column="INITIATOR_UM" />
		<result property="assisterUm" column="ASSISTER_UM" />
		<result property="assistReason" column="ASSIST_REASON" />
		<result property="assistType" column="ASSIST_TYPE" />
		<result property="operationType" column="OPERATION_TYPE" />
		<result property="assistReply" column="ASSIST_REPLY" />
	</resultMap>


	<resultMap type="com.paic.ncbs.claim.model.vo.investigate.InvestigateAssistVO" extends="InvestigateAssistMap" id="InvestigateAssistVOMap">
		
		<result property="initiatorUmName" column="INITIATOR_UM_NAME" />
		<result property="assisterUmName" column="ASSISTER_UM_NAME" />
		<result property="assistTypeName" column="ASSIST_TYPE_NAME" />
		<result property="operationTypeName" column="OPERATION_TYPE_NAME" />

	</resultMap>
	
	

	<insert id="addInvestigateAssist" parameterType="com.paic.ncbs.claim.model.dto.investigate.InvestigateAssistDTO" >
		INSERT INTO CLMS_INVESTIGATE_ASSIST (
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_INVESTIGATE_ASSIST,
			ID_AHCS_INVESTIGATE_TASK,
			INITIATOR_UM,
			ASSISTER_UM,
			ASSIST_REASON,
			ASSIST_TYPE,
			OPERATION_TYPE,
			ASSIST_REPLY,
			INITIATOR_UM_name,
			ASSISTER_UM_name,
			ARCHIVE_TIME
		)
	VALUES (
			#{createdBy ,jdbcType=VARCHAR},
			now(),
			#{updatedBy ,jdbcType=VARCHAR},
			now(),
			#{idAhcsInvestigateAssist ,jdbcType=VARCHAR},
			#{idAhcsInvestigateTask ,jdbcType=VARCHAR},
			#{initiatorUm ,jdbcType=VARCHAR},
			#{assisterUm ,jdbcType=VARCHAR},
			#{assistReason ,jdbcType=VARCHAR},
			#{assistType ,jdbcType=VARCHAR},
			#{operationType ,jdbcType=VARCHAR},
			#{assistReply ,jdbcType=VARCHAR},
			(select user_name from CLMS_user_info t where user_id=#{initiatorUm ,jdbcType=VARCHAR}),
			(select user_name from CLMS_user_info t where user_id=#{assisterUm ,jdbcType=VARCHAR}),
			sysdate()
	)
	</insert>
	


	<update id="modifyInvestigateAssist" parameterType="com.paic.ncbs.claim.model.dto.investigate.InvestigateAssistDTO">
		UPDATE CLMS_INVESTIGATE_ASSIST
		<set>
			<if test="updatedBy != null and updatedBy != '' ">
				UPDATED_BY  = #{updatedBy}, 
			</if>
			UPDATED_DATE=sysdate,

			<if test="initiatorUm != null and initiatorUm != '' ">
				INITIATOR_UM  = #{initiatorUm},
				INITIATOR_UM_name  = (select user_name from CLMS_user_info t where user_id=#{initiatorUm}),
			</if>
					
			<if test="assisterUm != null and assisterUm != '' ">
				ASSISTER_UM  = #{assisterUm},
				ASSISTER_UM_name  = (select user_name from CLMS_user_info t where user_id=#{assisterUm}),
			</if>
					
			<if test="assistReason != null and assistReason != '' ">
				ASSIST_REASON  = #{assistReason}, 
			</if>
					
			<if test="assistType != null and assistType != '' ">
				ASSIST_TYPE  = #{assistType}, 
			</if>
					
			<if test="operationType != null and operationType != '' ">
				OPERATION_TYPE  = #{operationType}, 
			</if>

			<if test="assistReply != null and assistReply != '' ">
				ASSIST_REPLY  = #{assistReply}, 
			</if>
		</set>
		WHERE ID_AHCS_INVESTIGATE_ASSIST=#{idAhcsInvestigateAssist} 
	</update>



	<select id="getInvestigateAssistOngoingByTaskId" resultMap="InvestigateAssistMap">
		select 
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_INVESTIGATE_ASSIST,
			ID_AHCS_INVESTIGATE_TASK,
			INITIATOR_UM,
			ASSISTER_UM,
			ASSIST_REASON,
			ASSIST_TYPE,
			OPERATION_TYPE,
			ASSIST_REPLY
		from CLMS_INVESTIGATE_ASSIST
		where  ID_AHCS_INVESTIGATE_TASK=#{idAhcsInvestigateTask} 
		AND OPERATION_TYPE is null 
	</select>	
	
	
	

	<select id="getInvestigateAssistByTaskId" resultMap="InvestigateAssistVOMap">
		select 
			t.CREATED_BY,
			t.CREATED_DATE,
			t.UPDATED_BY,
			t.UPDATED_DATE,
			t.ID_AHCS_INVESTIGATE_ASSIST,
			t.ID_AHCS_INVESTIGATE_TASK,
			t.INITIATOR_UM,
			t.ASSISTER_UM,
			t.ASSIST_REASON,
			t.ASSIST_TYPE,
			t.OPERATION_TYPE,
			t.ASSIST_REPLY,
			(select VALUE_CHINESE_NAME from clm_common_parameter  where value_code=t.ASSIST_TYPE limit 1) as ASSIST_TYPE_NAME,
	        (select VALUE_CHINESE_NAME from clm_common_parameter  where value_code=t.OPERATION_TYPE limit 1) as OPERATION_TYPE_NAME
		from CLMS_INVESTIGATE_ASSIST t
		where  t.ID_AHCS_INVESTIGATE_TASK=#{idAhcsInvestigateTask} 
	</select>
	

	<select id="getInvestigateAssistGonoByTaskId" resultMap="InvestigateAssistMap">
		select 
			t.CREATED_BY,
			t.CREATED_DATE,
			t.UPDATED_BY,
			t.UPDATED_DATE,
			t.ID_AHCS_INVESTIGATE_ASSIST,
			t.ID_AHCS_INVESTIGATE_TASK,
			t.INITIATOR_UM,
			t.ASSISTER_UM,
			t.ASSIST_REASON,
			t.ASSIST_TYPE,
			t.OPERATION_TYPE,
			t.ASSIST_REPLY
		from CLMS_INVESTIGATE_ASSIST t
		where  t.ID_AHCS_INVESTIGATE_TASK=#{idAhcsInvestigateTask} 
		and t.ASSIST_REPLY is not null
	    limit 1
	</select>
	
</mapper>