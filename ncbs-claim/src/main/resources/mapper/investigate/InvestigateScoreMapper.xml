<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.investigate.InvestigateScoreMapper">

	<resultMap type="com.paic.ncbs.claim.model.dto.investigate.InvestigateScoreDTO" id="InvestigateScoreMap">
		<id property="idAhcsInvestigateScore" column="ID_AHCS_INVESTIGATE_SCORE" />
		<result property="createdBy" column="CREATED_BY" />
		<result property="createdDate" column="CREATED_DATE" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="updatedDate" column="UPDATED_DATE" />
		<result property="idAhcsInvestigateTask" column="ID_AHCS_INVESTIGATE_TASK" />
		<result property="scoreTime" column="SCORE_TIME" />
		<result property="scoreUm" column="SCORE_UM" />
		<result property="scoreUmName" column="SCORE_UM_NAME" />
		<result property="isQualified" column="IS_QUALIFIED" />
		<result property="isAllEvidence" column="IS_ALL_EVIDENCE" />
		<result property="scoreValue" column="SCORE_VALUE" />
		<result property="isJudgeError" column="IS_JUDGE_ERROR" />
		<result property="scoreRemark" column="SCORE_REMARK" />
		<result property="rejectReason" column="REJECT_REASON" />
		<result property="scoreDepartmentCode" column="score_department_code" />
		<result property="archiveTime" column="ARCHIVE_TIME" />
	</resultMap>


	<select id="listInvestigateScore" resultMap="InvestigateScoreMap" >
         select s.CREATED_BY,
                s.CREATED_DATE,
                s.UPDATED_BY,
                s.UPDATED_DATE,
                ID_AHCS_INVESTIGATE_SCORE,
                s.ID_AHCS_INVESTIGATE_TASK,
                s.SCORE_TIME,
                s.SCORE_UM,
                s.SCORE_UM_NAME,
                s.IS_QUALIFIED,
                s.IS_ALL_EVIDENCE,
                s.SCORE_VALUE,
                s.IS_JUDGE_ERROR,
                s.SCORE_REMARK,
                s.ARCHIVE_TIME
           from CLMS_investigate_score s, CLMS_investigate_task t
         where s.ID_AHCS_INVESTIGATE_TASK = t.id_ahcs_investigate_task
         and t.id_ahcs_investigate = #{idAhcsInvestigate,jdbcType = VARCHAR}
         order by created_date desc
	</select>

    <select id="listScore" resultMap="InvestigateScoreMap" >
        select s.CREATED_BY,
               s.CREATED_DATE,
               s.UPDATED_BY,
               s.UPDATED_DATE,
               ID_AHCS_INVESTIGATE_SCORE,
               s.ID_AHCS_INVESTIGATE_TASK,
               s.SCORE_TIME,
               s.SCORE_UM,
               s.SCORE_UM_NAME,
               s.IS_QUALIFIED,
               s.IS_ALL_EVIDENCE,
               s.SCORE_VALUE,
               s.IS_JUDGE_ERROR,
               s.SCORE_REMARK,
               s.ARCHIVE_TIME
        from CLMS_investigate_score s, CLMS_investigate_task t
        where s.ID_AHCS_INVESTIGATE_TASK = t.id_ahcs_investigate_task
          and t.id_ahcs_investigate = #{idAhcsInvestigate,jdbcType = VARCHAR}
        order by created_date desc limit 1
    </select>





	<insert id="saveInvestigateScore" parameterType="com.paic.ncbs.claim.model.dto.investigate.InvestigateScoreDTO" >
		INSERT INTO CLMS_investigate_score (
			      CREATED_BY,
                  CREATED_DATE,
                  UPDATED_BY,
                  UPDATED_DATE,
                  ID_AHCS_INVESTIGATE_TASK,
                  SCORE_TIME,
                  SCORE_UM,
                  SCORE_UM_NAME,
                  IS_QUALIFIED,
                  IS_ALL_EVIDENCE,
                  SCORE_VALUE,
                  IS_JUDGE_ERROR,
                  SCORE_REMARK,
                  score_department_code,
                  ARCHIVE_TIME,
                  ID_AHCS_INVESTIGATE_SCORE
		)
	    VALUES (
			#{createdBy ,jdbcType=VARCHAR},
			now(),
			#{updatedBy ,jdbcType=VARCHAR},
			now(),
			#{idAhcsInvestigateTask ,jdbcType=VARCHAR},
			now(),
			#{scoreUm ,jdbcType=VARCHAR},
			(select user_name from CLMS_user_info t where user_id=#{scoreUmName ,jdbcType=VARCHAR}),
			#{isQualified ,jdbcType=VARCHAR},
			#{isAllEvidence ,jdbcType=VARCHAR},
			#{scoreValue ,jdbcType=NUMERIC},
			#{isJudgeError ,jdbcType=VARCHAR},
			#{scoreRemark ,jdbcType=VARCHAR},
			#{scoreDepartmentCode ,jdbcType=VARCHAR},
            now(),
            #{idAhcsInvestigateScore, jdbcType=VARCHAR}
			)
	</insert>


	<select id="getAllScoreWithNoDepartment" resultMap="InvestigateScoreMap" >
         select s.CREATED_BY,
                s.CREATED_DATE,
                s.UPDATED_BY,
                s.UPDATED_DATE,
                ID_AHCS_INVESTIGATE_SCORE,
                s.ID_AHCS_INVESTIGATE_TASK,
                s.SCORE_TIME,
                s.SCORE_UM,
                s.SCORE_UM_NAME,
                s.IS_QUALIFIED,
                s.IS_ALL_EVIDENCE,
                s.SCORE_VALUE,
                s.IS_JUDGE_ERROR,
                s.SCORE_REMARK,
                s.score_department_code,
                s.ARCHIVE_TIME
           from CLMS_investigate_score s
         where s.score_department_code is null
	</select>


	<update id="updateScoreDepartmentCode" parameterType="String">
		UPDATE CLMS_investigate_score set updated_date=now(),score_department_code=#{code} where ID_AHCS_INVESTIGATE_SCORE=#{idAhcsInvestigateScore}
	</update>

</mapper>