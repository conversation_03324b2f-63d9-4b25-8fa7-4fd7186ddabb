<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.paic.ncbs.claim.dao.mapper.supplements.SupplementsMaterialMapper">
    <resultMap id="result" type="com.paic.ncbs.claim.dao.entity.supplements.SupplementsMaterialEntity">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="report_no" property="reportNo" jdbcType="VARCHAR"/>
        <result column="case_times" property="caseTimes" jdbcType="INTEGER"/>
        <result column="supplement_count" property="supplementCount" jdbcType="INTEGER"/>
        <result column="task_definition_bpm_key" property="taskDefinitionBpmKey" jdbcType="VARCHAR"/>
        <result column="supplements_content" property="supplementsContent" jdbcType="VARCHAR"/>
        <result column="supplements_state" property="supplementsState" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_date" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_date" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="supplements_desc" property="supplementsDesc"/>
        <result column="remark" property="remark"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID,
        REPORT_NO,
        CASE_TIMES,
        SUPPLEMENT_COUNT,
        TASK_DEFINITION_BPM_KEY,
        SUPPLEMENTS_CONTENT,
        SUPPLEMENTS_STATE,
        SUPPLEMENTS_DESC,
        REMARK,
        CREATED_DATE,
        CREATED_BY,
        UPDATED_DATE,
        UPDATED_BY
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="result">
        select
        <include refid="Base_Column_List"/>
        from clms_supplements_material_task
        where ID = #{id}
    </select>

    <select id="getOneNewEntity" parameterType="com.paic.ncbs.claim.dao.entity.supplements.SupplementsMaterialEntity" resultType="com.paic.ncbs.claim.dao.entity.supplements.SupplementsMaterialEntity">
        select  supplement_count as supplementCount from clms_supplements_material_task where
        report_no =#{reportNo}
        and case_times =#{caseTimes}
        and task_definition_bpm_key=#{taskDefinitionBpmKey}
        order by supplement_count  desc limit 1
    </select>
    <!-- 根据报案号，赔付次数，查询补材状态为00补材中的数据-->
    <select id="getSupplementsMaterial" parameterType="com.paic.ncbs.claim.dao.entity.supplements.SupplementsMaterialEntity" resultType="com.paic.ncbs.claim.dao.entity.supplements.SupplementsMaterialEntity">
        select id,report_no reportNo,case_times caseTimes,supplement_count supplementCount,task_definition_bpm_key taskDefinitionBpmKey,supplements_content supplementsContent,supplements_state supplementsState
        from clms_supplements_material_task
        where report_no=#{reportNo}
        and case_times=#{caseTimes}
    </select>
    <select id="getAllDataList"  parameterType="java.lang.Integer" resultMap="result">
        select id,report_no,case_times,supplement_count,task_definition_bpm_key,supplements_content,supplements_state,created_date
        from clms_supplements_material_task
        where supplements_state='00' and
        created_date <![CDATA[<]]>SUBDATE(now(),#{days})
    </select>

    <insert id="insert"  parameterType="com.paic.ncbs.claim.dao.entity.supplements.SupplementsMaterialEntity">
        insert into clms_supplements_material_task(id,report_no,case_times,supplement_count,task_definition_bpm_key,supplements_content,supplements_state,created_by,created_date,updated_by,updated_date,supplements_desc)
        values(
            #{id}, #{reportNo}, #{caseTimes}, #{supplementCount}, #{taskDefinitionBpmKey}, #{supplementsContent}, #{supplementsState}, #{createdBy}, #{createdDate}, #{updatedBy}, #{updatedDate},#{supplementsDesc}
        )
    </insert>
    <update id="update" parameterType="com.paic.ncbs.claim.dao.entity.supplements.SupplementsMaterialEntity">
        update clms_supplements_material_task set
         supplements_state=#{supplementsState},
         remark = #{remark},
         UPDATED_BY = #{updatedBy},
         UPDATED_DATE = #{updatedDate}
        where id=#{id}
    </update>
    <select id="getCustomerSupplements" resultMap="result">
        select id,report_no,case_times,supplement_count,task_definition_bpm_key,supplements_content,supplements_state,created_date,created_by,updated_by,updated_date,supplements_desc,remark
        from clms_supplements_material_task
        where report_no=#{reportNo}
        and case_times=#{caseTimes}
    </select>
    <!--查询客户补材未完成到期短信提醒任务 当时算一天要加1，第一次和第二次天数和类型不一样 -->
    <select id="getSmsNotificationTask" resultMap="result">
        select
        <include refid="Base_Column_List"/>
        from clms_supplements_material_task
        where SUPPLEMENTS_STATE = '00'
          and DATEDIFF(NOW(), CREATED_DATE) + 1 = #{days}
          and not exists (select 1
                          from clms_sms_info s
                          where s.SEND_LINK = CONCAT(#{smsType}, ':',
                                                     clms_supplements_material_task.ID));
    </select>
    <select id="getcompleteDate" resultType="com.paic.ncbs.claim.dao.entity.supplements.SupplementsMaterialEntity">
        select updated_date updatedDate from clms_supplements_material_task
        where report_no=#{reportNo}
        and case_times=#{caseTimes}
        and  supplements_state='01'
        ORDER BY updated_date desc limit 1
    </select>
</mapper>