<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.fileupload.ExemptDocumentMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.fileupload.ExemptDocumentDTO" id="result">
        <id column="ID_AHCS_EXEMPT_DOCUMENT" property="idAhcsExemptDocument"/>
        <result column="CREATED_BY" property="createdBy"/>
        <result column="CREATED_DATE" property="createdDate"/>
        <result column="UPDATED_BY" property="updatedBy"/>
        <result column="UPDATED_DATE" property="updatedDate"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="SMALL_CODES" property="smallCodes"/>
        <result column="USER_ID" property="userId"/>

    </resultMap>


    <select id="getExemptDocument" resultMap="result">
        SELECT CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_EXEMPT_DOCUMENT,
        REPORT_NO,
        CASE_TIMES,
        SMALL_CODES,
        USER_ID
        FROM CLMS_EXEMPT_DOCUMENT
        WHERE REPORT_NO=#{reportNo}
        <if test="caseTimes != null and caseTimes != '' ">
            AND CASE_TIMES=#{caseTimes}
        </if>
    </select>

</mapper>