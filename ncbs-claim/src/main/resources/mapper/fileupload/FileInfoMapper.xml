<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.fileupload.FileInfoMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO" id="resultFileList">
        <id column="ID_AHCS_FILE_INFO" property="idAhcsFileInfo"/>
        <result column="CREATED_BY" property="uploadPersonnel"/>
        <result column="CREATED_BY" property="createdBy"/>
        <result column="CREATED_DATE" property="createdDate"/>
        <result column="UPDATED_DATE" property="updatedDate"/>
        <result column="CREATED_DATE" property="uploadDate"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="DOCUMENT_GROUP_ID" property="documentGroupId"/>
        <result column="FLOW_TYPE" property="flowType"/>
        <result column="TASK_CODE" property="taskCode"/>
        <result column="FILE_TYPE" property="fileType"/>
        <result column="RESCUE_ID" property="rescueId"/>
    </resultMap>


    <insert id="addFileInfo" parameterType="com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO">
        INSERT INTO CLMS_FILE_INFO
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_FILE_INFO,
        REPORT_NO,
        CASE_TIMES,
        DOCUMENT_GROUP_ID,
        FLOW_TYPE,
        FILE_TYPE,
        ARCHIVE_TIME
        <if test="rescueId != null and rescueId != '' ">
            , RESCUE_ID
        </if>
        )
        VALUES
        (#{createdBy},
        sysdate(),
        #{updatedBy},
        sysdate(),
        #{idAhcsFileInfo},
        #{reportNo},
        #{caseTimes},
        #{documentGroupId},
        #{flowType,jdbcType=VARCHAR},
        #{fileType},
        sysdate()
        <if test="rescueId != null and rescueId != '' ">
            ,#{rescueId}
        </if>
        )
    </insert>


    <select id="getFileGroupId" parameterType="com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO"
            resultMap="resultFileList">
        SELECT DISTINCT AFI.DOCUMENT_GROUP_ID, AFI.FLOW_TYPE, AFI.CASE_TIMES,AFI.CREATED_BY,AFI.CREATED_DATE,AFI.UPDATED_DATE
        FROM CLMS_FILE_INFO AFI
        WHERE AFI.FILE_TYPE=#{fileType}
        AND AFI.REPORT_NO=#{reportNo}
        <if test="caseTimes != null and caseTimes != '' ">
            AND AFI.CASE_TIMES=#{caseTimes}
        </if>
        <if test="flowType != null and flowType != '' ">
            AND AFI.FLOW_TYPE=#{flowType}
        </if>
    </select>


    <select id="getFileList" resultMap="resultFileList">
		SELECT AFI.DOCUMENT_GROUP_ID, AFI.FLOW_TYPE, AFI.CASE_TIMES
		  FROM CLMS_FILE_INFO AFI
		 WHERE AFI.FILE_TYPE=#{fileType}
		       AND AFI.REPORT_NO=#{reportNo}
	</select>


    <select id="getDocumentGroupIdByReportNoAndCaseTimes" resultType="java.lang.String">
        SELECT AFI.DOCUMENT_GROUP_ID
        FROM CLMS_FILE_INFO AFI
        WHERE AFI.FILE_TYPE='01'
        <if test="reportNo != null and reportNo != '' ">
            AND AFI.REPORT_NO=#{reportNo}
        </if>
        <if test="caseTimes != null and caseTimes != '' ">
            AND AFI.CASE_TIMES=#{caseTimes}
        </if>
    </select>

    <select id="getSeq" resultType="java.lang.String">
        select left(md5(uuid()),5)
    </select>

    <select id="getDocumentItemId" resultType="java.lang.String">
        select CONCAT(DB_INSTANCE,left(md5(uuid()),12)) from db_instance_config limit 1
    </select>


    <select id="getGroupIdList" resultType="java.lang.String">
        SELECT DISTINCT AFI.DOCUMENT_GROUP_ID
        FROM CLMS_FILE_INFO AFI
        WHERE AFI.FILE_TYPE = '01' AND AFI.REPORT_NO IN
        <foreach collection="reportNoList" index="index" item="reportNo" open="(" separator="," close=")">
            #{reportNo}
        </foreach>
    </select>


    <insert id="createDocument" parameterType="com.paic.ncbs.claim.model.vo.doc.FileDocumentVO">
        insert into document
        (created_by, created_date, updated_by, updated_date,  document_type,document_name, document_format,
         document_desc, upload_personnel, upload_date, upload_path,document_class, document_size,origin_name,
         document_group_items_id,document_group_id,document_order,document_status,remark,document_id,queueid)
        VALUES
        ( #{createdBy},now(),#{updatedBy}, now(),#{documentType,jdbcType=VARCHAR},#{documentName,jdbcType=VARCHAR},#{documentFormat,jdbcType=VARCHAR},
        #{documentDesc,jdbcType=VARCHAR},#{uploadPersonnel},now(),#{uploadPath},#{documentClass},
        #{documentSize},#{originName,jdbcType=VARCHAR},#{documentGroupItemsId},#{documentGroupId},#{documentOrder, jdbcType=NUMERIC},
        #{documentStatus},#{remark,jdbcType=VARCHAR},#{documentGroupId},#{queueId,jdbcType=VARCHAR})
    </insert>

    <insert id="apiCreateDocument" parameterType="com.paic.ncbs.claim.model.vo.doc.FileDocumentVO">
        insert into document
        (created_by, created_date, updated_by, updated_date,  document_type,document_name, document_format,
        document_desc, upload_personnel, upload_date, upload_path,document_class, document_size,origin_name,
        document_group_items_id,document_group_id,document_order,document_status,remark,document_id,queueid,document_source,network_flag)
        VALUES
        ( #{createdBy},now(),#{updatedBy}, now(),#{documentType,jdbcType=VARCHAR},#{documentName,jdbcType=VARCHAR},#{documentFormat,jdbcType=VARCHAR},
        #{documentDesc,jdbcType=VARCHAR},#{uploadPersonnel},now(),#{uploadPath},#{documentClass},
        #{documentSize},#{originName,jdbcType=VARCHAR},#{documentGroupItemsId},#{documentGroupId},#{documentOrder, jdbcType=NUMERIC},
        #{documentStatus},#{remark,jdbcType=VARCHAR},#{documentGroupId},#{queueId,jdbcType=VARCHAR},#{documentSource},#{networkFlag})
    </insert>

    <select id="queryDocumentByGroupId" parameterType="java.lang.String"
            resultType="com.paic.ncbs.claim.model.dto.fileupload.FileDocumentDTO" >
        select d.document_group_id       documentGroupId,
               d.upload_path             uploadPath,
               d.document_group_items_id documentGroupItemsId,
               d.document_name           documentName,
               d.origin_name             originName,
               d.document_format         documentFormat,
               d.upload_path             url,
               d.created_date            createdDate,
               d.upload_personnel        uploadPersonnel,
               d.document_type           documentType,
               d.document_class          documentClass,
               d.document_status         documentStatus,
               d.queueid                 queueId,
               d.DOCUMENT_SIZE           documentSize
        from document d
        where d.document_group_id = #{documentGroupId}
          and d.document_status = 'Y'
    </select>
    <!--根据案件号，文件号file-id 查询影像名称-->
    <select id="getDocumentName" parameterType="com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO" resultType="com.paic.ncbs.claim.model.dto.fileupload.FileDocumentDTO">
        select
            d.document_group_id       documentGroupId,
            d.upload_path             uploadPath,
            d.document_group_items_id documentGroupItemsId,
            d.document_name           documentName,
            d.origin_name             originName,
            d.document_format         documentFormat,
            d.upload_path             url,
            d.created_date            createdDate,
            d.upload_personnel        uploadPersonnel,
            d.document_type           documentType,
            d.document_class          documentClass,
            d.document_status         documentStatus,
            d.document_size           documentSize,
            d.queueid                 queueIdfrom
        from document d
        where  DOCUMENT_GROUP_ID =#{reportNo}
        and UPLOAD_PATH =#{fileId}
        and document_status='Y'
    </select>

    <!--根据文件号file-id 查询影像名称-->
    <select id="getDocumentByFileId" parameterType="com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO" resultType="com.paic.ncbs.claim.model.dto.fileupload.FileDocumentDTO">
        select
        d.document_group_id       documentGroupId,
        d.upload_path             uploadPath,
        d.document_group_items_id documentGroupItemsId,
        d.document_name           documentName,
        d.origin_name             originName,
        d.document_format         documentFormat,
        d.upload_path             url,
        d.created_date            createdDate,
        d.upload_personnel        uploadPersonnel,
        d.document_type           documentType,
        d.document_class          documentClass,
        d.document_status         documentStatus,
        d.document_size           documentSize,
        d.queueid                 queueIdfrom
        from document d
        where UPLOAD_PATH =#{fileId}
        and document_status='Y'
    </select>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        INSERT INTO CLMS_FILE_INFO(
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_AHCS_FILE_INFO,
            REPORT_NO,
            CASE_TIMES,
            DOCUMENT_GROUP_ID,
            FLOW_TYPE,
            FILE_TYPE,
            ARCHIVE_TIME,
            RESCUE_ID
        )
        SELECT
            #{userId},
            NOW(),
            #{userId},
            NOW(),
            MD5(UUID()),
            REPORT_NO,
            #{reopenCaseTimes},
            DOCUMENT_GROUP_ID,
            FLOW_TYPE,
            FILE_TYPE,
            NOW(),
            RESCUE_ID
        FROM CLMS_FILE_INFO
        WHERE REPORT_NO=#{reportNo}
          AND CASE_TIMES=#{caseTimes}
    </insert>
</mapper>