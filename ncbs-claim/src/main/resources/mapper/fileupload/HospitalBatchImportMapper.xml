<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.fileupload.HospitalBatchImportMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.fileupload.HospitalBatchImportDTO" id="result">
        <id column="ID_AHCS_HOSPITAL_BATCH_IMPORT" property="idAhcsHospitalBatchImport"/>
        <result column="BATCH_NO" property="batchNo"/>
        <result column="HOSPITAL_NAME" property="hospitalName"/>
        <result column="HOSPITAL_GRADE" property="grade"/>
        <result column="HOSPITAL_TYPE" property="hospitalPropertyDes"/>
        <result column="FAIL_REASON" property="failReason"/>
        <result column="CREATED_BY" property="createdBy"/>
        <result column="UPDATED_BY" property="updatedBy"/>
        <result column="sys_ctime" property="createdDate"/>
        <result column="sys_utime" property="updatedDate"/>
    </resultMap>

    <insert id="insertImportInfo" parameterType="com.paic.ncbs.claim.model.dto.fileupload.HospitalBatchImportDTO">
        insert into HOSPITAL_BATCH_IMPORT(ID_AHCS_HOSPITAL_BATCH_IMPORT,BATCH_NO,HOSPITAL_NAME,HOSPITAL_GRADE,HOSPITAL_TYPE,FAIL_REASON,CREATED_BY,UPDATED_BY)
            values (#{idAhcsHospitalBatchImport},#{batchNo},#{hospitalName},#{grade},#{hospitalPropertyDes},#{failReason},#{createdBy},#{updatedBy})
    </insert>

    <select id="getImportFailInfo" resultType="com.paic.ncbs.claim.model.vo.fileupolad.HospitalInfoImportFailVO">
        select batch_no batchNo,
               HOSPITAL_NAME hospitalName,
               HOSPITAL_GRADE grade,
               HOSPITAL_TYPE hospitalPropertyDes,
               FAIL_REASON failReason
            from HOSPITAL_BATCH_IMPORT
                where batch_no= #{batchNo}
        and fail_reason != 'success'
    </select>

</mapper>