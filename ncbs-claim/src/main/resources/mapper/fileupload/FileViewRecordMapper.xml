<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.fileupload.FileViewRecordMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.fileupload.FileViewRecordDTO" id="result">
        <id column="ID_AHCS_FILE_VIEW_RECORD" property="fileViewRecordId"/>
        <result column="CREATED_BY" property="createdBy"/>
        <result column="CREATED_DATE" property="createdDate"/>
        <result column="UPDATED_BY" property="updatedBy"/>
        <result column="UPDATED_DATE" property="updatedDate"/>
        <result column="DOCUMENT_GROUP_ITEMS_ID" property="documentGroupItemsId"/>
        <result column="USER_ID" property="userId"/>
    </resultMap>


    <insert id="addFileViewRecord" parameterType="com.paic.ncbs.claim.model.dto.fileupload.FileViewRecordDTO">
        INSERT INTO CLMS_FILE_VIEW_RECORD
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        DOCUMENT_GROUP_ITEMS_ID,
        USER_ID,
        ARCHIVE_TIME)
        VALUES
        (#{createdBy},
        sysdate(),
        #{updatedBy},
        sysdate(),
        #{documentGroupItemsId},
        #{userId},
        sysdate())
    </insert>


    <select id="getFileViewRecord" parameterType="com.paic.ncbs.claim.model.dto.fileupload.FileViewRecordDTO"
            resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM CLMS_FILE_VIEW_RECORD
        WHERE DOCUMENT_GROUP_ITEMS_ID=#{documentGroupItemsId}
        AND USER_ID=#{userId}
    </select>


</mapper>