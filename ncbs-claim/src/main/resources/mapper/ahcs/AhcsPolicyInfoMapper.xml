<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity">
        <id column="ID_AHCS_POLICY_INFO" property="idAhcsPolicyInfo" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="REPORT_NO" property="reportNo" jdbcType="VARCHAR"/>
        <result column="POLICY_NO" property="policyNo" jdbcType="VARCHAR"/>
        <result column="ACCEPT_INSURANCE_DATE" property="acceptInsuranceDate" jdbcType="TIMESTAMP"/>
        <result column="DEPARTMENT_CODE" property="departmentCode" jdbcType="VARCHAR"/>
        <result column="INSURANCE_BEGIN_TIME" property="insuranceBeginTime" jdbcType="TIMESTAMP"/>
        <result column="INSURANCE_END_TIME" property="insuranceEndTime" jdbcType="TIMESTAMP"/>
        <result column="UNDERWRITE_DATE" property="underwriteDate" jdbcType="TIMESTAMP"/>
        <result column="POLICY_STATUS" property="policyStatus" jdbcType="VARCHAR"/>
        <result column="PRODUCT_CODE" property="productCode" jdbcType="VARCHAR"/>
        <result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR"/>
        <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR"/>
        <result column="TOTAL_AGREE_PREMIUM" property="totalAgreePremium" jdbcType="DECIMAL"/>
        <result column="TOTAL_ACTUAL_PREMIUM" property="totalActualPremium" jdbcType="DECIMAL"/>
        <result column="AMOUNT_CURRENCY_CODE" property="amountCurrencyCode" jdbcType="VARCHAR"/>
        <result column="PREMIUM_CURRENCY_CODE" property="premiumCurrencyCode" jdbcType="VARCHAR"/>
        <result column="DATA_SOURCE" property="dataSource" jdbcType="VARCHAR"/>
        <result column="COINSURANCE_MARK" property="coinsuranceMark" jdbcType="VARCHAR"/>
        <result column="TOTAL_INSURED_AMOUNT" property="totalInsuredAmount" jdbcType="DECIMAL"/>
        <result column="REPLY_CODE" property="replyCode" jdbcType="VARCHAR"/>
        <result column="REPLY_NAME" property="replyName" jdbcType="VARCHAR"/>
        <result column="SYSTEM_ID" property="systemId" jdbcType="VARCHAR"/>
        <result column="ENDORSE_SYSTEM_ID" property="endorseSystemId" jdbcType="VARCHAR"/>
        <result column="SALE_AGENT_CODE" property="saleAgentCode" jdbcType="VARCHAR"/>
        <result column="SALE_AGENT_NAME" property="saleAgentName" jdbcType="VARCHAR"/>
        <result column="SOCIAL_FLAG" property="socialFlag" jdbcType="VARCHAR"/>
        <result column="AGREENMENT_DEFINE" property="agreenmentDefine" jdbcType="VARCHAR"/>
        <result column="BUSINESS_SOURCE_CODE" property="businessSourceCode" jdbcType="VARCHAR"/>
        <result column="BUSINESS_SOURCE_NAME" property="businessSourceName" jdbcType="VARCHAR"/>
        <result column="BUSINESS_SOURCE_DETAIL_CODE" property="businessSourceDetailCode" jdbcType="VARCHAR"/>
        <result column="BUSINESS_SOURCE_DETAIL_NAME" property="businessSourceDetailName" jdbcType="VARCHAR"/>
        <result column="CHANNEL_SOURCE_CODE" property="channelSourceCode" jdbcType="VARCHAR"/>
        <result column="CHANNEL_SOURCE_NAME" property="channelSourceName" jdbcType="VARCHAR"/>
        <result column="CHANNEL_SOURCE_DETAIL_CODE" property="channelSourceDetailCode" jdbcType="VARCHAR"/>
        <result column="CHANNEL_SOURCE_DETAIL_NAME" property="channelSourceDetailName" jdbcType="VARCHAR"/>
        <result column="VIRTUAL_TARGET_NUM" property="virtualTargetNum" jdbcType="DECIMAL"/>
        <result column="EDR_EFFECTIVE_DATE" property="edrEffectiveDate" jdbcType="TIMESTAMP"/>
        <result column="ENDORSE_NO" property="endorseNo" jdbcType="VARCHAR"/>
        <result column="APPLY_DATE" property="applyDate" jdbcType="TIMESTAMP"/>
        <result column="PRODUCT_PACKAGE_TYPE" property="productPackageType" jdbcType="VARCHAR"/>
        <result column="SUBJECT_ID" property="subjectId" jdbcType="VARCHAR"/>
        <result column="PRODUCT_VERSION" property="productVersion" jdbcType="VARCHAR"/>
        <result column="BATCH_NO" property="batchNo" jdbcType="VARCHAR"/>
        <result column="POLICY_CER_NO" property="policyCerNo" jdbcType="VARCHAR"/>
        <result column="SELF_CARD_NO" property="selfCardNo" jdbcType="VARCHAR"/>
        <result column="IS_UPLOAD_FLAT_SUCCESSED" property="isUploadFlatSuccessed" jdbcType="VARCHAR"/>
        <result column="CASE_NO" property="caseNo" jdbcType="VARCHAR"/>
        <result column="GENERATE_FLAG" property="generateFlag" jdbcType="VARCHAR"/>
        <result column="ORG_PRODUCT_CODE" property="orgProductCode" jdbcType="VARCHAR"/>
        <result column="ORG_PRODUCT_NAME" property="orgProductName" jdbcType="VARCHAR"/>
        <result column="IS_POLICY_BEFORE_PAY_FEE" property="isPolicyBeforePayfee" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="LAST_POLICY_NO" property="lastPolicyNo" jdbcType="VARCHAR"/>
        <result column="GROUP_ID" property="groupId" jdbcType="VARCHAR"/>
        <result column="APPLY_NUM" property="applyNum" jdbcType="DECIMAL"/>
        <result column="PARTNER_CODE" property="partnerCode" jdbcType="VARCHAR"/>
        <result column="HEALTH_NOTIFICATION" property="healthNotification" jdbcType="VARCHAR"/>
        <result column="PAY_TERM_NO" property="payTermNo" jdbcType="INTEGER"/>
        <result column="REPORT_REGISTER_UM" property="reportRegisterUm" jdbcType="VARCHAR"/>
        <result column="PRIMARY_INTRODUCER_CODE" property="primaryIntroducerCode" jdbcType="VARCHAR"/>
        <result column="AGENT_BROKER_CODE" property="agentBrokerCode" jdbcType="VARCHAR"/>
        <result column="AGENT_BROKER_NAME" property="agentBrokerName" jdbcType="VARCHAR"/>
        <result column="AGENT_BROKER_TYPE" property="agentBrokerType" jdbcType="VARCHAR"/>
        <result column="total_duty_limit" property="totalDutyLimit" jdbcType="DECIMAL"/>
        <result column="once_duty_limit" property="onceDutyLimit" jdbcType="DECIMAL"/>
        <result column="total_law_duty_limit" property="totalLawDutyLimit" jdbcType="DECIMAL"/>
        <result column="once_law_duty_limit" property="onceLawDutyLimit" jdbcType="DECIMAL"/>
        <result column="remit_condition" property="remitCondition" jdbcType="VARCHAR"/>
        <result column="policy_special_promise" property="policySpecialPromise" jdbcType="VARCHAR"/>
        <result column="PROFIT_CENTER" property="profitCenter" jdbcType="VARCHAR"/>
        <result column="is_transfer_insure" property="isTransferInsure" jdbcType="VARCHAR"/>
        <result column="transfer_insurance_policy_no" property="transferInsurancePolicyNo" jdbcType="VARCHAR"/>
        <result column="transfer_insurance_end_date" property="transferInsuranceEndDate" jdbcType="VARCHAR"/>
        <result column="transfer_insurance_product_name" property="transferInsuranceProductName" jdbcType="VARCHAR"/>
        <result column="prosecution_period" property="prosecutionPeriod" jdbcType="DECIMAL"/>
        <result column="extend_report_date" property="extendReportDate" jdbcType="DECIMAL"/>
        <result column="DEPARTMENT_CODE_NEW" property="departmentCodeNew" jdbcType="VARCHAR"/>
        <result column="RISK_GROUP_TYPE" property="riskGroupType" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID_AHCS_POLICY_INFO,
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        POLICY_NO,
        ACCEPT_INSURANCE_DATE,
        DEPARTMENT_CODE,
        INSURANCE_BEGIN_TIME,
        INSURANCE_END_TIME,
        UNDERWRITE_DATE,
        POLICY_STATUS,
        PRODUCT_CODE,
        PRODUCT_NAME,
        BUSINESS_TYPE,
        TOTAL_AGREE_PREMIUM,
        TOTAL_ACTUAL_PREMIUM,
        AMOUNT_CURRENCY_CODE,
        PREMIUM_CURRENCY_CODE,
        DATA_SOURCE,
        COINSURANCE_MARK,
        TOTAL_INSURED_AMOUNT,
        REPLY_CODE,
        REPLY_NAME,
        SYSTEM_ID,
        ENDORSE_SYSTEM_ID,
        SALE_AGENT_CODE,
        SALE_AGENT_NAME,
        SOCIAL_FLAG,
        AGREENMENT_DEFINE,
        BUSINESS_SOURCE_CODE,
        BUSINESS_SOURCE_NAME,
        BUSINESS_SOURCE_DETAIL_CODE,
        BUSINESS_SOURCE_DETAIL_NAME,
        CHANNEL_SOURCE_CODE,
        CHANNEL_SOURCE_NAME,
        CHANNEL_SOURCE_DETAIL_CODE,
        CHANNEL_SOURCE_DETAIL_NAME,
        VIRTUAL_TARGET_NUM,
        POLICY_VALID,
        PRODUCT_PACKAGE_TYPE,
        SUBJECT_ID,
        EDR_EFFECTIVE_DATE,
        ENDORSE_NO,
        APPLY_DATE,
        PRODUCT_VERSION,
        BATCH_NO,
        POLICY_CER_NO,
        SELF_CARD_NO,
        IS_UPLOAD_FLAT_SUCCESSED,
        CASE_NO,
        GENERATE_FLAG,
        SHARE_INSURED_AMOUNT,
        ORG_PRODUCT_CODE,
        ORG_PRODUCT_NAME,
        IS_POLICY_BEFORE_PAY_FEE,
        REMARK,
        LAST_POLICY_NO,
        GROUP_ID,
        TRAFFIC_NO,
        APPLY_NUM,
        PARTNER_CODE,
        HEALTH_NOTIFICATION,
        PAY_TERM_NO,
        PRIMARY_INTRODUCER_CODE,
        AGENT_BROKER_CODE,
        AGENT_BROKER_NAME,
        AGENT_BROKER_TYPE,
        total_duty_limit,
        once_duty_limit,
        total_law_duty_limit,
        once_law_duty_limit,
        remit_condition,
        policy_special_promise,
        PROFIT_CENTER,
        is_transfer_insure,
        transfer_insurance_policy_no,
        transfer_insurance_end_date,
        transfer_insurance_product_name,
        prosecution_period,
        extend_report_date,
        DEPARTMENT_CODE_NEW,
        RISK_GROUP_TYPE,
        IS_FAMILY
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLMS_POLICY_INFO
        where ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLMS_POLICY_INFO
        where ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity">
        insert into CLMS_POLICY_INFO (ID_AHCS_POLICY_INFO, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, REPORT_NO,
        POLICY_NO, ACCEPT_INSURANCE_DATE, DEPARTMENT_CODE,
        INSURANCE_BEGIN_TIME, INSURANCE_END_TIME,
        UNDERWRITE_DATE, POLICY_STATUS, PRODUCT_CODE,
        PRODUCT_NAME, BUSINESS_TYPE, TOTAL_AGREE_PREMIUM,
        TOTAL_ACTUAL_PREMIUM, AMOUNT_CURRENCY_CODE,
        PREMIUM_CURRENCY_CODE, DATA_SOURCE, COINSURANCE_MARK,
        TOTAL_INSURED_AMOUNT, REPLY_CODE, REPLY_NAME,
        SYSTEM_ID, ENDORSE_SYSTEM_ID, SALE_AGENT_CODE,
        SALE_AGENT_NAME, SOCIAL_FLAG, AGREENMENT_DEFINE,
        BUSINESS_SOURCE_CODE, BUSINESS_SOURCE_NAME,
        BUSINESS_SOURCE_DETAIL_CODE, BUSINESS_SOURCE_DETAIL_NAME,
        CHANNEL_SOURCE_CODE, CHANNEL_SOURCE_NAME, CHANNEL_SOURCE_DETAIL_CODE,
        CHANNEL_SOURCE_DETAIL_NAME, VIRTUAL_TARGET_NUM, EDR_EFFECTIVE_DATE, ENDORSE_NO,
        APPLY_DATE, PRODUCT_PACKAGE_TYPE, SUBJECT_ID, PRODUCT_VERSION, BATCH_NO, POLICY_CER_NO, SELF_CARD_NO,
        IS_UPLOAD_FLAT_SUCCESSED, CASE_NO, GENERATE_FLAG, SHARE_INSURED_AMOUNT, ORG_PRODUCT_CODE, ORG_PRODUCT_NAME,
        IS_POLICY_BEFORE_PAY_FEE, REMARK, LAST_POLICY_NO, GROUP_ID, TRAFFIC_NO, APPLY_NUM, POLICY_EXTEND,
        PARTNER_CODE, HEALTH_NOTIFICATION, PAY_TERM_NO,
        PRIMARY_INTRODUCER_CODE,
        AGENT_BROKER_CODE,
        AGENT_BROKER_NAME,
        AGENT_BROKER_TYPE,
        IS_FACULTATIVE_BUSINESS,
        IS_FAMILY,
        ONLINE_ORDER_NO,
        PROFIT_CENTER,
        total_duty_limit,
        once_duty_limit,
        total_law_duty_limit,
        once_law_duty_limit,
        remit_condition,
        policy_special_promise,
        is_transfer_insure,
        transfer_insurance_policy_no,
        transfer_insurance_end_date,
        transfer_insurance_product_name,
        prosecution_period,
        extend_report_date,
        DEPARTMENT_CODE_NEW,
        RISK_GROUP_TYPE,
        PRODUCT_CLASS
        )
        values (#{idAhcsPolicyInfo,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP},
        #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP}, #{reportNo,jdbcType=VARCHAR},
        #{policyNo,jdbcType=VARCHAR}, #{acceptInsuranceDate,jdbcType=TIMESTAMP}, #{departmentCode,jdbcType=VARCHAR},
        #{insuranceBeginTime,jdbcType=TIMESTAMP}, #{insuranceEndTime,jdbcType=TIMESTAMP},
        #{underwriteDate,jdbcType=TIMESTAMP}, #{policyStatus,jdbcType=VARCHAR}, #{productCode,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR}, #{businessType,jdbcType=VARCHAR}, #{totalAgreePremium,jdbcType=DECIMAL},
        #{totalActualPremium,jdbcType=DECIMAL}, #{amountCurrencyCode,jdbcType=VARCHAR},
        #{premiumCurrencyCode,jdbcType=VARCHAR}, #{dataSource,jdbcType=VARCHAR}, #{coinsuranceMark,jdbcType=VARCHAR},
        #{totalInsuredAmount,jdbcType=DECIMAL}, #{replyCode,jdbcType=VARCHAR}, #{replyName,jdbcType=VARCHAR},
        #{systemId,jdbcType=VARCHAR}, #{endorseSystemId,jdbcType=VARCHAR}, #{saleAgentCode,jdbcType=VARCHAR},
        #{saleAgentName,jdbcType=VARCHAR}, #{socialFlag,jdbcType=VARCHAR}, #{agreenmentDefine,jdbcType=VARCHAR},
        #{businessSourceCode,jdbcType=VARCHAR}, #{businessSourceName,jdbcType=VARCHAR},
        #{businessSourceDetailCode,jdbcType=VARCHAR}, #{businessSourceDetailName,jdbcType=VARCHAR},
        #{channelSourceCode,jdbcType=VARCHAR}, #{channelSourceName,jdbcType=VARCHAR},
        #{channelSourceDetailCode,jdbcType=VARCHAR},
        #{channelSourceDetailName,jdbcType=VARCHAR}, #{virtualTargetNum,jdbcType=DECIMAL},
        #{edrEffectiveDate,jdbcType=TIMESTAMP},
        #{endorseNo,jdbcType=VARCHAR},#{applyDate,jdbcType=TIMESTAMP},#{productPackageType,jdbcType=VARCHAR},
        #{subjectId,jdbcType=VARCHAR},#{productVersion,jdbcType=VARCHAR},#{batchNo,jdbcType=VARCHAR},#{policyCerNo,jdbcType=VARCHAR},
        #{selfCardNo,jdbcType=VARCHAR},#{isUploadFlatSuccessed,jdbcType=VARCHAR},#{caseNo,jdbcType=VARCHAR},#{generateFlag,jdbcType=VARCHAR},
        #{shareInsuredAmount,jdbcType=VARCHAR},#{orgProductCode,jdbcType=VARCHAR},#{orgProductName,jdbcType=VARCHAR},
        #{isPolicyBeforePayfee,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{lastPolicyNo,jdbcType=VARCHAR},
        #{groupId,jdbcType=VARCHAR},
        #{trafficNo,jdbcType=VARCHAR}, #{applyNum,jdbcType=DECIMAL}, #{policyExtend,jdbcType=VARCHAR},
        #{partnerCode,jdbcType=VARCHAR},
        #{healthNotification,jdbcType=VARCHAR},#{payTermNo,jdbcType=DECIMAL},
        #{primaryIntroducerCode,jdbcType=VARCHAR},
        #{agentBrokerCode,jdbcType=VARCHAR},
        #{agentBrokerName,jdbcType=VARCHAR},
        #{agentBrokerType,jdbcType=VARCHAR},
        #{isFacultativeBusiness},
        #{isFamily},
        #{onlineOrderNo},
        #{profitCenter,jdbcType=VARCHAR},
        #{totalDutyLimit},
        #{onceDutyLimit},
        #{totalLawDutyLimit},
        #{onceLawDutyLimit},
        #{remitCondition},
        #{policySpecialPromise},
        #{isTransferInsure},
        #{transferInsurancePolicyNo},
        #{transferInsuranceEndDate},
        #{transferInsuranceProductName},
        #{prosecutionPeriod},
        #{extendReportDate},
        #{departmentCodeNew,jdbcType=VARCHAR},
        #{riskGroupType,jdbcType=VARCHAR},
        #{productClass,jdbcType=VARCHAR})
    </insert>
    
    <insert id="insertList" parameterType="java.util.List">
        insert into CLMS_POLICY_INFO (ID_AHCS_POLICY_INFO, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, REPORT_NO,
        POLICY_NO, ACCEPT_INSURANCE_DATE, DEPARTMENT_CODE,
        INSURANCE_BEGIN_TIME, INSURANCE_END_TIME,
        UNDERWRITE_DATE, POLICY_STATUS, PRODUCT_CODE,
        PRODUCT_NAME, BUSINESS_TYPE, TOTAL_AGREE_PREMIUM,
        TOTAL_ACTUAL_PREMIUM, AMOUNT_CURRENCY_CODE,
        PREMIUM_CURRENCY_CODE, DATA_SOURCE, COINSURANCE_MARK,
        TOTAL_INSURED_AMOUNT, REPLY_CODE, REPLY_NAME,
        SYSTEM_ID, ENDORSE_SYSTEM_ID, SALE_AGENT_CODE,
        SALE_AGENT_NAME, SOCIAL_FLAG, AGREENMENT_DEFINE,
        BUSINESS_SOURCE_CODE, BUSINESS_SOURCE_NAME,
        BUSINESS_SOURCE_DETAIL_CODE, BUSINESS_SOURCE_DETAIL_NAME,
        CHANNEL_SOURCE_CODE, CHANNEL_SOURCE_NAME, CHANNEL_SOURCE_DETAIL_CODE,
        CHANNEL_SOURCE_DETAIL_NAME, VIRTUAL_TARGET_NUM, EDR_EFFECTIVE_DATE, ENDORSE_NO,
        APPLY_DATE, PRODUCT_PACKAGE_TYPE, SUBJECT_ID, PRODUCT_VERSION, BATCH_NO, POLICY_CER_NO,
        SELF_CARD_NO, IS_UPLOAD_FLAT_SUCCESSED, CASE_NO, GENERATE_FLAG, SHARE_INSURED_AMOUNT,
        ORG_PRODUCT_CODE, ORG_PRODUCT_NAME, IS_POLICY_BEFORE_PAY_FEE, REMARK, LAST_POLICY_NO, GROUP_ID, TRAFFIC_NO,
        APPLY_NUM,IS_FACULTATIVE_BUSINESS,IS_FAMILY,ONLINE_ORDER_NO,PROFIT_CENTER,is_transfer_insure,
        prosecution_period,
        extend_report_date,
        DEPARTMENT_CODE_NEW,
        RISK_GROUP_TYPE
        ) 
        values
        <foreach collection="list" separator="," index="index" item="item">
            (#{item.idAhcsPolicyInfo,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR},
            #{item.createdDate,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedDate,jdbcType=TIMESTAMP},
            #{item.reportNo,jdbcType=VARCHAR},
            #{item.policyNo,jdbcType=VARCHAR}, #{item.acceptInsuranceDate,jdbcType=TIMESTAMP},
            #{item.departmentCode,jdbcType=VARCHAR},
            #{item.insuranceBeginTime,jdbcType=TIMESTAMP}, #{item.insuranceEndTime,jdbcType=TIMESTAMP},
            #{item.underwriteDate,jdbcType=TIMESTAMP}, #{item.policyStatus,jdbcType=VARCHAR},
            #{item.productCode,jdbcType=VARCHAR},
            #{item.productName,jdbcType=VARCHAR}, #{item.businessType,jdbcType=VARCHAR},
            #{item.totalAgreePremium,jdbcType=DECIMAL},
            #{item.totalActualPremium,jdbcType=DECIMAL}, #{item.amountCurrencyCode,jdbcType=VARCHAR},
            #{item.premiumCurrencyCode,jdbcType=VARCHAR}, #{item.dataSource,jdbcType=VARCHAR},
            #{item.coinsuranceMark,jdbcType=VARCHAR},
            #{item.totalInsuredAmount,jdbcType=DECIMAL}, #{item.replyCode,jdbcType=VARCHAR},
            #{item.replyName,jdbcType=VARCHAR},
            #{item.systemId,jdbcType=VARCHAR}, #{item.endorseSystemId,jdbcType=VARCHAR},
            #{item.saleAgentCode,jdbcType=VARCHAR},
            #{item.saleAgentName,jdbcType=VARCHAR}, #{item.socialFlag,jdbcType=VARCHAR},
            #{item.agreenmentDefine,jdbcType=VARCHAR},
            #{item.businessSourceCode,jdbcType=VARCHAR}, #{item.businessSourceName,jdbcType=VARCHAR},
            #{item.businessSourceDetailCode,jdbcType=VARCHAR},
            #{item.businessSourceDetailName,jdbcType=VARCHAR},
            #{item.channelSourceCode,jdbcType=VARCHAR}, #{item.channelSourceName,jdbcType=VARCHAR},
            #{item.channelSourceDetailCode,jdbcType=VARCHAR},
            #{item.channelSourceDetailName,jdbcType=VARCHAR}, #{item.virtualTargetNum,jdbcType=DECIMAL},
            #{item.edrEffectiveDate,jdbcType=TIMESTAMP},
            #{item.endorseNo,jdbcType=VARCHAR},#{item.applyDate,jdbcType=TIMESTAMP},#{item.productPackageType,jdbcType=VARCHAR},
            #{item.subjectId,jdbcType=VARCHAR},#{item.productVersion,jdbcType=VARCHAR},#{item.batchNo,jdbcType=VARCHAR},#{item.policyCerNo,jdbcType=VARCHAR},
            #{item.selfCardNo,jdbcType=VARCHAR}, #{item.isUploadFlatSuccessed,jdbcType=VARCHAR},
            #{item.caseNo,jdbcType=VARCHAR}, #{item.generateFlag,jdbcType=VARCHAR},
            #{item.shareInsuredAmount,jdbcType=VARCHAR}, #{item.orgProductCode,jdbcType=VARCHAR},
            #{item.orgProductName,jdbcType=VARCHAR},
            #{item.isPolicyBeforePayfee,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR},
            #{item.lastPolicyNo,jdbcType=VARCHAR}, #{item.groupId,jdbcType=VARCHAR},
            #{item.trafficNo,jdbcType=VARCHAR}, #{item.applyNum,jdbcType=DECIMAL},
            #{item.isFacultativeBusiness},
            #{item.isFamily,jdbcType=VARCHAR},
            #{item.onlineOrderNo,jdbcType=VARCHAR},
            #{item.profitCenter,jdbcType=VARCHAR},
            #{item.isTransferInsure,jdbcType=VARCHAR},
            #{item.prosecutionPeriod,jdbcType=DECIMAL},
            #{item.extendReportDate,jdbcType=DECIMAL},
            #{item.departmentCodeNew,jdbcType=VARCHAR},
            #{item.riskGroupType,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity">
        insert into CLMS_POLICY_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idAhcsPolicyInfo != null">
                ID_AHCS_POLICY_INFO,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="reportNo != null">
                REPORT_NO,
            </if>
            <if test="policyNo != null">
                POLICY_NO,
            </if>
            <if test="acceptInsuranceDate != null">
                ACCEPT_INSURANCE_DATE,
            </if>
            <if test="departmentCode != null">
                DEPARTMENT_CODE,
            </if>
            <if test="insuranceBeginTime != null">
                INSURANCE_BEGIN_TIME,
            </if>
            <if test="insuranceEndTime != null">
                INSURANCE_END_TIME,
            </if>
            <if test="underwriteDate != null">
                UNDERWRITE_DATE,
            </if>
            <if test="policyStatus != null">
                POLICY_STATUS,
            </if>
            <if test="productCode != null">
                PRODUCT_CODE,
            </if>
            <if test="productName != null">
                PRODUCT_NAME,
            </if>
            <if test="businessType != null">
                BUSINESS_TYPE,
            </if>
            <if test="totalAgreePremium != null">
                TOTAL_AGREE_PREMIUM,
            </if>
            <if test="totalActualPremium != null">
                TOTAL_ACTUAL_PREMIUM,
            </if>
            <if test="amountCurrencyCode != null">
                AMOUNT_CURRENCY_CODE,
            </if>
            <if test="premiumCurrencyCode != null">
                PREMIUM_CURRENCY_CODE,
            </if>
            <if test="dataSource != null">
                DATA_SOURCE,
            </if>
            <if test="coinsuranceMark != null">
                COINSURANCE_MARK,
            </if>
            <if test="totalInsuredAmount != null">
                TOTAL_INSURED_AMOUNT,
            </if>
            <if test="replyCode != null">
                REPLY_CODE,
            </if>
            <if test="replyName != null">
                REPLY_NAME,
            </if>
            <if test="systemId != null">
                SYSTEM_ID,
            </if>
            <if test="endorseSystemId != null">
                ENDORSE_SYSTEM_ID,
            </if>
            <if test="saleAgentCode != null">
                SALE_AGENT_CODE,
            </if>
            <if test="saleAgentName != null">
                SALE_AGENT_NAME,
            </if>
            <if test="socialFlag != null">
                SOCIAL_FLAG,
            </if>
            <if test="agreenmentDefine != null">
                AGREENMENT_DEFINE,
            </if>
            <if test="businessSourceCode != null">
                BUSINESS_SOURCE_CODE,
            </if>
            <if test="businessSourceName != null">
                BUSINESS_SOURCE_NAME,
            </if>
            <if test="businessSourceDetailCode != null">
                BUSINESS_SOURCE_DETAIL_CODE,
            </if>
            <if test="businessSourceDetailName != null">
                BUSINESS_SOURCE_DETAIL_NAME,
            </if>
            <if test="channelSourceCode != null">
                CHANNEL_SOURCE_CODE,
            </if>
            <if test="channelSourceName != null">
                CHANNEL_SOURCE_NAME,
            </if>
            <if test="channelSourceDetailCode != null">
                CHANNEL_SOURCE_DETAIL_CODE,
            </if>
            <if test="channelSourceDetailName != null">
                CHANNEL_SOURCE_DETAIL_NAME,
            </if>
            <if test="virtualTargetNum != null">
                VIRTUAL_TARGET_NUM,
            </if>
            <if test="edrEffectiveDate != null">
                EDR_EFFECTIVE_DATE,
            </if>
            <if test="endorseNo != null">
                ENDORSE_NO,
            </if>
            <if test="applyDate != null">
                APPLY_DATE,
            </if>
            <if test="subjectId != null">
                SUBJECT_ID,
            </if>
            <if test="productPackageType != null">
                PRODUCT_PACKAGE_TYPE,
            </if>
            <if test="productVersion != null">
                PRODUCT_VERSION,
            </if>
            <if test="batchNo != null">
                BATCH_NO,
            </if>
            <if test="selfCardNo != null">
                SELF_CARD_NO,
            </if>
            <if test="policyCerNo != null">
                POLICY_CER_NO,
            </if>
            <if test="isUploadFlatSuccessed != null">
                IS_UPLOAD_FLAT_SUCCESSED,
            </if>
            <if test="caseNo != null">
                CASE_NO,
            </if>
            <if test="generateFlag != null">
                GENERATE_FLAG,
            </if>
            <if test="shareInsuredAmount != null">
                SHARE_INSURED_AMOUNT,
            </if>
            <if test="orgProductCode != null">
                ORG_PRODUCT_CODE,
            </if>
            <if test="orgProductName != null">
                ORG_PRODUCT_NAME,
            </if>
            <if test="isPolicyBeforePayfee != null">
                IS_POLICY_BEFORE_PAY_FEE,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
            <if test="lastPolicyNo != null">
                LAST_POLICY_NO,
            </if>
            <if test="groupId != null">
                GROUP_ID,
            </if>
            <if test="trafficNo != null">
                TRAFFIC_NO,
            </if>
            <if test="applyNum != null">
                APPLY_NUM,
            </if>
            <if test="healthNotification != null">
                HEALTH_NOTIFICATION,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idAhcsPolicyInfo != null">
                #{idAhcsPolicyInfo,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportNo != null">
                #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="policyNo != null">
                #{policyNo,jdbcType=VARCHAR},
            </if>
            <if test="acceptInsuranceDate != null">
                #{acceptInsuranceDate,jdbcType=TIMESTAMP},
            </if>
            <if test="departmentCode != null">
                #{departmentCode,jdbcType=VARCHAR},
            </if>
            <if test="insuranceBeginTime != null">
                #{insuranceBeginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="insuranceEndTime != null">
                #{insuranceEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="underwriteDate != null">
                #{underwriteDate,jdbcType=TIMESTAMP},
            </if>
            <if test="policyStatus != null">
                #{policyStatus,jdbcType=VARCHAR},
            </if>
            <if test="productCode != null">
                #{productCode,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="totalAgreePremium != null">
                #{totalAgreePremium,jdbcType=DECIMAL},
            </if>
            <if test="totalActualPremium != null">
                #{totalActualPremium,jdbcType=DECIMAL},
            </if>
            <if test="amountCurrencyCode != null">
                #{amountCurrencyCode,jdbcType=VARCHAR},
            </if>
            <if test="premiumCurrencyCode != null">
                #{premiumCurrencyCode,jdbcType=VARCHAR},
            </if>
            <if test="dataSource != null">
                #{dataSource,jdbcType=VARCHAR},
            </if>
            <if test="coinsuranceMark != null">
                #{coinsuranceMark,jdbcType=VARCHAR},
            </if>
            <if test="totalInsuredAmount != null">
                #{totalInsuredAmount,jdbcType=DECIMAL},
            </if>
            <if test="replyCode != null">
                #{replyCode,jdbcType=VARCHAR},
            </if>
            <if test="replyName != null">
                #{replyName,jdbcType=VARCHAR},
            </if>
            <if test="systemId != null">
                #{systemId,jdbcType=VARCHAR},
            </if>
            <if test="endorseSystemId != null">
                #{endorseSystemId,jdbcType=VARCHAR},
            </if>
            <if test="saleAgentCode != null">
                #{saleAgentCode,jdbcType=VARCHAR},
            </if>
            <if test="saleAgentName != null">
                #{saleAgentName,jdbcType=VARCHAR},
            </if>
            <if test="socialFlag != null">
                #{socialFlag,jdbcType=VARCHAR},
            </if>
            <if test="agreenmentDefine != null">
                #{agreenmentDefine,jdbcType=VARCHAR},
            </if>
            <if test="businessSourceCode != null">
                #{businessSourceCode,jdbcType=VARCHAR},
            </if>
            <if test="businessSourceName != null">
                #{businessSourceName,jdbcType=VARCHAR},
            </if>
            <if test="businessSourceDetailCode != null">
                #{businessSourceDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="businessSourceDetailName != null">
                #{businessSourceDetailName,jdbcType=VARCHAR},
            </if>
            <if test="channelSourceCode != null">
                #{channelSourceCode,jdbcType=VARCHAR},
            </if>
            <if test="channelSourceName != null">
                #{channelSourceName,jdbcType=VARCHAR},
            </if>
            <if test="channelSourceDetailCode != null">
                #{channelSourceDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="channelSourceDetailName != null">
                #{channelSourceDetailName,jdbcType=VARCHAR},
            </if>
            <if test="virtualTargetNum != null">
                #{virtualTargetNum,jdbcType=DECIMAL},
            </if>
            <if test="edrEffectiveDate != null">
                #{edrEffectiveDate,jdbcType=TIMESTAMP},
            </if>
            <if test="endorseNo != null">
                #{endorseNo,jdbcType=VARCHAR},
            </if>
            <if test="applyDate != null">
                #{applyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="subjectId != null">
                #{subjectId,jdbcType=TIMESTAMP},
            </if>
            <if test="productPackageType != null">
                #{productPackageType,jdbcType=TIMESTAMP},
            </if>
            <if test="productVersion != null">
                #{productVersion,jdbcType=TIMESTAMP},
            </if>
            <if test="batchNo != null">batchNo
                #{batchNo,jdbcType=TIMESTAMP},
            </if>
            <if test="selfCardNo != null">
                #{selfCardNo,jdbcType=VARCHAR},
            </if>
            <if test="policyCerNo != null">
                #{policyCerNo,jdbcType=VARCHAR},
            </if>
            <if test="isUploadFlatSuccessed != null">
                #{isUploadFlatSuccessed,jdbcType=VARCHAR},
            </if>
            <if test="caseNo != null">
                #{caseNo,jdbcType=VARCHAR},
            </if>
            <if test="generateFlag != null">
                #{generateFlag,jdbcType=VARCHAR},
            </if>
            <if test="shareInsuredAmount != null">
                #{shareInsuredAmount,jdbcType=VARCHAR},
            </if>
            <if test="orgProductCode != null">
                #{orgProductCode,jdbcType=VARCHAR},
            </if>
            <if test="orgProductName != null">
                #{orgProductName,jdbcType=VARCHAR},
            </if>
            <if test="isPolicyBeforePayfee != null">
                #{isPolicyBeforePayfee,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="lastPolicyNo != null">
                #{lastPolicyNo,jdbcType=VARCHAR},
            </if>
            <if test="groupId != null">
                #{groupId,jdbcType=VARCHAR},
            </if>
            <if test="trafficNo != null">
                #{trafficNo,jdbcType=VARCHAR},
            </if>
            <if test="applyNum != null">
                #{applyNum,jdbcType=DECIMAL},
            </if>
            <if test="healthNotification != null">
                #{healthNotification,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity">
        update CLMS_POLICY_INFO
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportNo != null">
                REPORT_NO = #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="policyNo != null">
                POLICY_NO = #{policyNo,jdbcType=VARCHAR},
            </if>
            <if test="acceptInsuranceDate != null">
                ACCEPT_INSURANCE_DATE = #{acceptInsuranceDate,jdbcType=TIMESTAMP},
            </if>
            <if test="departmentCode != null">
                DEPARTMENT_CODE = #{departmentCode,jdbcType=VARCHAR},
            </if>
            <if test="insuranceBeginTime != null">
                INSURANCE_BEGIN_TIME = #{insuranceBeginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="insuranceEndTime != null">
                INSURANCE_END_TIME = #{insuranceEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="underwriteDate != null">
                UNDERWRITE_DATE = #{underwriteDate,jdbcType=TIMESTAMP},
            </if>
            <if test="policyStatus != null">
                POLICY_STATUS = #{policyStatus,jdbcType=VARCHAR},
            </if>
            <if test="productCode != null">
                PRODUCT_CODE = #{productCode,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                PRODUCT_NAME = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="totalAgreePremium != null">
                TOTAL_AGREE_PREMIUM = #{totalAgreePremium,jdbcType=DECIMAL},
            </if>
            <if test="totalActualPremium != null">
                TOTAL_ACTUAL_PREMIUM = #{totalActualPremium,jdbcType=DECIMAL},
            </if>
            <if test="amountCurrencyCode != null">
                AMOUNT_CURRENCY_CODE = #{amountCurrencyCode,jdbcType=VARCHAR},
            </if>
            <if test="premiumCurrencyCode != null">
                PREMIUM_CURRENCY_CODE = #{premiumCurrencyCode,jdbcType=VARCHAR},
            </if>
            <if test="dataSource != null">
                DATA_SOURCE = #{dataSource,jdbcType=VARCHAR},
            </if>
            <if test="coinsuranceMark != null">
                COINSURANCE_MARK = #{coinsuranceMark,jdbcType=VARCHAR},
            </if>
            <if test="totalInsuredAmount != null">
                TOTAL_INSURED_AMOUNT = #{totalInsuredAmount,jdbcType=DECIMAL},
            </if>
            <if test="replyCode != null">
                REPLY_CODE = #{replyCode,jdbcType=VARCHAR},
            </if>
            <if test="replyName != null">
                REPLY_NAME = #{replyName,jdbcType=VARCHAR},
            </if>
            <if test="systemId != null">
                SYSTEM_ID = #{systemId,jdbcType=VARCHAR},
            </if>
            <if test="endorseSystemId != null">
                ENDORSE_SYSTEM_ID = #{endorseSystemId,jdbcType=VARCHAR},
            </if>
            <if test="saleAgentCode != null">
                SALE_AGENT_CODE = #{saleAgentCode,jdbcType=VARCHAR},
            </if>
            <if test="saleAgentName != null">
                SALE_AGENT_NAME = #{saleAgentName,jdbcType=VARCHAR},
            </if>
            <if test="socialFlag != null">
                SOCIAL_FLAG = #{socialFlag,jdbcType=VARCHAR},
            </if>
            <if test="agreenmentDefine != null">
                AGREENMENT_DEFINE = #{agreenmentDefine,jdbcType=VARCHAR},
            </if>
            <if test="businessSourceCode != null">
                BUSINESS_SOURCE_CODE = #{businessSourceCode,jdbcType=VARCHAR},
            </if>
            <if test="businessSourceName != null">
                BUSINESS_SOURCE_NAME = #{businessSourceName,jdbcType=VARCHAR},
            </if>
            <if test="businessSourceDetailCode != null">
                BUSINESS_SOURCE_DETAIL_CODE = #{businessSourceDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="businessSourceDetailName != null">
                BUSINESS_SOURCE_DETAIL_NAME = #{businessSourceDetailName,jdbcType=VARCHAR},
            </if>
            <if test="channelSourceCode != null">
                CHANNEL_SOURCE_CODE = #{channelSourceCode,jdbcType=VARCHAR},
            </if>
            <if test="channelSourceName != null">
                CHANNEL_SOURCE_NAME = #{channelSourceName,jdbcType=VARCHAR},
            </if>
            <if test="channelSourceDetailCode != null">
                CHANNEL_SOURCE_DETAIL_CODE = #{channelSourceDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="channelSourceDetailName != null">
                CHANNEL_SOURCE_DETAIL_NAME = #{channelSourceDetailName,jdbcType=VARCHAR},
            </if>
            <if test="virtualTargetNum != null">
                VIRTUAL_TARGET_NUM = #{virtualTargetNum,jdbcType=DECIMAL},
            </if>
            <if test="edrEffectiveDate != null">
                EDR_EFFECTIVE_DATE = #{edrEffectiveDate,jdbcType=TIMESTAMP},
            </if>
            <if test="endorseNo != null">
                ENDORSE_NO = #{endorseNo,jdbcType=VARCHAR},
            </if>
            <if test="applyDate != null">
                APPLY_DATE = #{applyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="subjectId != null">
                SUBJECT_ID = #{subjectId,jdbcType=TIMESTAMP},
            </if>
            <if test="productPackageType != null">
                PRODUCT_PACKAGE_TYPE = #{productPackageType,jdbcType=TIMESTAMP},
            </if>
            <if test="productVersion != null">
                PRODUCT_VERSION = #{productVersion,jdbcType=TIMESTAMP},
            </if>
            <if test="batchNo != null">
                BATCH_NO = #{batchNo,jdbcType=TIMESTAMP},
            </if>
            <if test="selfCardNo != null">
                SELF_CARD_NO = #{selfCardNo,jdbcType=VARCHAR},
            </if>
            <if test="policyCerNo != null">
                POLICY_CER_NO = #{policyCerNo,jdbcType=VARCHAR},
            </if>
            <if test="isUploadFlatSuccessed != null">
                IS_UPLOAD_FLAT_SUCCESSED = #{isUploadFlatSuccessed,jdbcType=VARCHAR},
            </if>
            <if test="caseNo != null">
                CASE_NO = #{caseNo,jdbcType=VARCHAR},
            </if>
            <if test="generateFlag != null">
                GENERATE_FLAG = #{generateFlag,jdbcType=VARCHAR},
            </if>
            <if test="shareInsuredAmount != null">
                SHARE_INSURED_AMOUNT = #{shareInsuredAmount,jdbcType=VARCHAR},
            </if>
            <if test="orgProductCode != null">
                ORG_PRODUCT_CODE = #{orgProductCode,jdbcType=VARCHAR},
            </if>
            <if test="orgProductName != null">
                ORG_PRODUCT_NAME = #{orgProductName,jdbcType=VARCHAR},
            </if>
            <if test="isPolicyBeforePayfee != null">
                IS_POLICY_BEFORE_PAY_FEE = #{isPolicyBeforePayfee,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="lastPolicyNo != null">
                LAST_POLICY_NO = #{lastPolicyNo,jdbcType=VARCHAR},
            </if>
            <if test="groupId != null">
                GROUP_ID = #{groupId,jdbcType=VARCHAR},
            </if>
            <if test="trafficNo != null">
                TRAFFIC_NO = #{trafficNo,jdbcType=VARCHAR},
            </if>
            <if test="applyNum != null">
                APPLY_NUM = #{applyNum,jdbcType=DECIMAL},
            </if>
            <if test="healthNotification != null">
                HEALTH_NOTIFICATION = #{healthNotification,jdbcType=VARCHAR},
            </if>
        </set>
        where ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity">
        update CLMS_POLICY_INFO
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        REPORT_NO = #{reportNo,jdbcType=VARCHAR},
        POLICY_NO = #{policyNo,jdbcType=VARCHAR},
        ACCEPT_INSURANCE_DATE = #{acceptInsuranceDate,jdbcType=TIMESTAMP},
        DEPARTMENT_CODE = #{departmentCode,jdbcType=VARCHAR},
        INSURANCE_BEGIN_TIME = #{insuranceBeginTime,jdbcType=TIMESTAMP},
        INSURANCE_END_TIME = #{insuranceEndTime,jdbcType=TIMESTAMP},
        UNDERWRITE_DATE = #{underwriteDate,jdbcType=TIMESTAMP},
        POLICY_STATUS = #{policyStatus,jdbcType=VARCHAR},
        PRODUCT_CODE = #{productCode,jdbcType=VARCHAR},
        PRODUCT_NAME = #{productName,jdbcType=VARCHAR},
        BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR},
        TOTAL_AGREE_PREMIUM = #{totalAgreePremium,jdbcType=DECIMAL},
        TOTAL_ACTUAL_PREMIUM = #{totalActualPremium,jdbcType=DECIMAL},
        AMOUNT_CURRENCY_CODE = #{amountCurrencyCode,jdbcType=VARCHAR},
        PREMIUM_CURRENCY_CODE = #{premiumCurrencyCode,jdbcType=VARCHAR},
        DATA_SOURCE = #{dataSource,jdbcType=VARCHAR},
        COINSURANCE_MARK = #{coinsuranceMark,jdbcType=VARCHAR},
        TOTAL_INSURED_AMOUNT = #{totalInsuredAmount,jdbcType=DECIMAL},
        REPLY_CODE = #{replyCode,jdbcType=VARCHAR},
        REPLY_NAME = #{replyName,jdbcType=VARCHAR},
        SYSTEM_ID = #{systemId,jdbcType=VARCHAR},
        ENDORSE_SYSTEM_ID = #{endorseSystemId,jdbcType=VARCHAR},
        SALE_AGENT_CODE = #{saleAgentCode,jdbcType=VARCHAR},
        SALE_AGENT_NAME = #{saleAgentName,jdbcType=VARCHAR},
        SOCIAL_FLAG = #{socialFlag,jdbcType=VARCHAR},
        AGREENMENT_DEFINE = #{agreenmentDefine,jdbcType=VARCHAR},
        BUSINESS_SOURCE_CODE = #{businessSourceCode,jdbcType=VARCHAR},
        BUSINESS_SOURCE_NAME = #{businessSourceName,jdbcType=VARCHAR},
        BUSINESS_SOURCE_DETAIL_CODE = #{businessSourceDetailCode,jdbcType=VARCHAR},
        BUSINESS_SOURCE_DETAIL_NAME = #{businessSourceDetailName,jdbcType=VARCHAR},
        CHANNEL_SOURCE_CODE = #{channelSourceCode,jdbcType=VARCHAR},
        CHANNEL_SOURCE_NAME = #{channelSourceName,jdbcType=VARCHAR},
        CHANNEL_SOURCE_DETAIL_CODE = #{channelSourceDetailCode,jdbcType=VARCHAR},
        CHANNEL_SOURCE_DETAIL_NAME = #{channelSourceDetailName,jdbcType=VARCHAR},
        VIRTUAL_TARGET_NUM = #{virtualTargetNum,jdbcType=DECIMAL},
        EDR_EFFECTIVE_DATE = #{edrEffectiveDate,jdbcType=TIMESTAMP},
        ENDORSE_NO = #{endorseNo,jdbcType=VARCHAR},
        APPLY_DATE = #{applyDate,jdbcType=TIMESTAMP},
        SUBJECT_ID = #{subjectId,jdbcType=TIMESTAMP},
        PRODUCT_PACKAGE_TYPE = #{productPackageType,jdbcType=TIMESTAMP},
        PRODUCT_VERSION = #{productVersion,jdbcType=TIMESTAMP},
        BATCH_NO = #{batchNo,jdbcType=TIMESTAMP},
        SELF_CARD_NO = #{selfCardNo,jdbcType=VARCHAR},
        POLICY_CER_NO = #{policyCerNo,jdbcType=VARCHAR},
        IS_UPLOAD_FLAT_SUCCESSED = #{isUploadFlatSuccessed,jdbcType=VARCHAR},
        CASE_NO = #{caseNo,jdbcType=VARCHAR},
        GENERATE_FLAG = #{generateFlag,jdbcType=VARCHAR},
        SHARE_INSURED_AMOUNT = #{shareInsuredAmount,jdbcType=VARCHAR},
        ORG_PRODUCT_CODE = #{orgProductCode,jdbcType=VARCHAR},
        ORG_PRODUCT_NAME = #{orgProductName,jdbcType=VARCHAR},
        IS_POLICY_BEFORE_PAY_FEE = #{isPolicyBeforePayfee,jdbcType=VARCHAR},
        REMARK = #{remark,jdbcType=VARCHAR},
        LAST_POLICY_NO = #{lastPolicyNo,jdbcType=VARCHAR},
        GROUP_ID = #{groupId,jdbcType=VARCHAR},
        TRAFFIC_NO = #{trafficNo,jdbcType=VARCHAR},
        APPLY_NUM = #{applyNum,jdbcType=DECIMAL},
        HEALTH_NOTIFICATION = #{healthNotification,jdbcType=VARCHAR}
        where ID_AHCS_POLICY_INFO = #{idAhcsPolicyInfo,jdbcType=VARCHAR}
    </update>

    <delete id="deleteByReportNoAndPolicyNo" parameterType="java.lang.String">
        delete
        from CLMS_POLICY_INFO b
        where b.report_no = #{reportNo,jdbcType=VARCHAR}
        and b.policy_no =#{policyNo,jdbcType=VARCHAR}
    </delete>


    <select id="getList" parameterType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from CLMS_POLICY_INFO
        <where>
            <if test="reportNo != null and reportNo != ''">
                REPORT_NO = #{reportNo,jdbcType=VARCHAR}
            </if>
            <if test="policyNo != null and policyNo != ''">
                AND POLICY_NO = #{policyNo,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="selectDepartmentCodeByReportNo" resultType="java.lang.String" parameterType="java.lang.String">
        select
            DEPARTMENT_CODE
        from CLMS_POLICY_INFO
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
        LIMIT 1
    </select>

    <select id="getReportNoByPolicyNo" resultType="java.lang.String" parameterType="java.lang.String">
        select distinct REPORT_NO from clms_policy_info  where POLICY_NO = TRIM(#{policyNo})
    </select>

    <select id="getPolicyNoByReportNo" resultType="java.lang.String" parameterType="java.lang.String">
        select distinct POLICY_NO from clms_policy_info  where REPORT_NO = TRIM(#{reportNo})
    </select>

    <select id="selectDepartmentNameByReportNo" resultType="java.lang.String" parameterType="java.lang.String">
        select DEPARTMENT_ABBR_NAME from department_define
        where DEPARTMENT_CODE = (
            select
                DEPARTMENT_CODE
            from CLMS_POLICY_INFO
            where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
            LIMIT 1
            )

    </select>

    <select id="getOnePolicyNoByReportNo" parameterType="java.lang.String" resultType="java.lang.String">
        select policy_no from clms_policy_info where report_no=#{reportNo}
    </select>

    <select id="getProductNameByReportNo" parameterType="java.lang.String" resultType="java.lang.String">
        select PRODUCT_NAME from clms_policy_info where report_no=#{reportNo}
    </select>

    <select id="getDepartmentNameByReportNo" resultType="java.lang.String" parameterType="java.lang.String">
        select d.DEPARTMENT_ABBR_NAME
        from  clms_policy_info c
        left  join  department_define d
         on  c.`DEPARTMENT_CODE` =d.`DEPARTMENT_CODE`
        where  C.`REPORT_NO` =#{reportNo,jdbcType=VARCHAR}
        LIMIT 1
    </select>
    <select id="selectByReportNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from clms_policy_info
        where REPORT_NO = #{reportNo}
    </select>
    <select id="getLastPolicyNo" parameterType="java.lang.String" resultType="java.lang.String">
        select
        LAST_POLICY_NO
        from clms_policy_info
        where REPORT_NO = #{reportNo}
    </select>

    <!--查保单-产品大类、产品、险种、条款-->
    <select id="getProductInfo" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT
            a.POLICY_NO policyNo,
            a.PRODUCT_CODE productCode,
            a.PRODUCT_NAME productName,
            a.PRODUCT_VERSION productVersion,
            b.PLAN_CODE planCode,
            b.PLAN_NAME planName,
            c.TERM_CODE termCode,
            c.`STATUS` status,
            c.IS_MAIN isMain,
            c.PRODUCT_CLASS productClass
        FROM
            clms_policy_info a
                JOIN clms_policy_plan b ON a.ID_AHCS_POLICY_INFO = b.ID_AHCS_POLICY_INFO
                JOIN plan_info c ON b.PLAN_CODE = c.PLAN_CODE
        WHERE
            a.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
            <if test="isMain != null and isMain != ''">
                AND c.IS_MAIN = #{isMain}
            </if>
    </select>
    <select id="getPolicyStartAndEnddate"  resultType="com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity">
        select INSURANCE_BEGIN_TIME insuranceBeginTime,INSURANCE_END_TIME insuranceEndTime from clms_policy_info
        where report_no=#{reportNo}
        and policy_no=#{policyNo}
    </select>
    <select id="getProductClassByReportNo" resultType="java.lang.String" parameterType="java.lang.String">
        select
            PRODUCT_CLASS
        from CLMS_POLICY_INFO
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
        LIMIT 1
    </select>
    <select id="getDutyDetails" resultType="com.paic.ncbs.claim.dao.entity.duty.ReportDutyDetailVo">
        select ply.REPORT_NO,ply.POLICY_NO,plan.PLAN_CODE,plan.PLAN_NAME ,duty.DUTY_CODE,
        duty.DUTY_NAME,duty.DUTY_AMOUNT,duty.IS_DUTY_SHARED_AMOUNT ,
        duty.DUTY_SHARED_AMOUNT_MERGE ,dd.DUTY_DETAIL_CODE ,dd.DUTY_DETAIL_NAME,
        (select ifnull(sum(cda.ATTRIBUTE_VALUE),0)
        from clms_duty_attribute cda
        where dd.ID_AHCS_POLICY_DUTY = cda.ID_AHCS_POLICY_DUTY  and cda.ATTRIBUTE_CODE ='271') deductible
        from clms_policy_info ply,clms_policy_plan plan,clms_policy_duty duty, clms_policy_duty_detail dd
        where plan.ID_AHCS_POLICY_INFO = ply.ID_AHCS_POLICY_INFO
        and duty.ID_AHCS_POLICY_PLAN = plan.ID_AHCS_POLICY_PLAN
        and dd.ID_AHCS_POLICY_DUTY = duty.ID_AHCS_POLICY_DUTY
        and ply.REPORT_NO = #{reportNo};
    </select>
</mapper>