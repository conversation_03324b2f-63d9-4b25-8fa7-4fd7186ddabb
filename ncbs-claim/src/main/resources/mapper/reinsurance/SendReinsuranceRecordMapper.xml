<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.reinsurance.SendReinsuranceRecordMapper">

    <resultMap type="com.paic.ncbs.claim.dao.entity.reinsurance.SendReinsuranceRecord" id="BaseResultMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="reportNo" column="report_no" jdbcType="VARCHAR"/>
        <result property="caseTimes" column="case_times" jdbcType="INTEGER"/>
        <result property="caseNo" column="case_no" jdbcType="NUMERIC"/>
        <result property="registNo" column="regist_no" jdbcType="VARCHAR"/>
        <result property="policyNo" column="policy_no" jdbcType="VARCHAR"/>
        <result property="claimType" column="claim_type" jdbcType="VARCHAR"/>
        <result property="claimTypeDesc" column="claim_type_desc" jdbcType="VARCHAR"/>
        <result property="requestParam" column="request_param" jdbcType="VARCHAR"/>
        <result property="responseParam" column="response_param" jdbcType="VARCHAR"/>
        <result property="isSuccess" column="is_success" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDate" column="updated_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,
        REPORT_NO,
        CASE_TIMES,
        CASE_NO,
        REGIST_NO,
        POLICY_NO,
        CLAIM_TYPE,
        CLAIM_TYPE_DESC,
        REQUEST_PARAM,
        RESPONSE_PARAM,
        IS_SUCCESS,
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE
    </sql>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO clms_send_reinsurance_record (id, report_no, case_times, case_no, regist_no, policy_no,
        claim_type, claim_type_desc, request_param, response_param, is_success)
        VALUES (#{id}, #{reportNo}, #{caseTimes}, #{caseNo}, #{registNo}, #{policyNo},
        #{claimType}, #{claimTypeDesc}, #{requestParam}, #{responseParam}, #{isSuccess})
    </insert>

    <select id="queryOneByCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM clms_send_reinsurance_record
        WHERE report_no = #{reportNo,jdbcType=VARCHAR}
          AND case_times = #{caseTimes,jdbcType=INTEGER}
          AND claim_type = #{claimType,jdbcType=VARCHAR}
    </select>

    <select id="getCompensateList" resultType="com.paic.ncbs.claim.model.dto.reinsurance.RepayCalDTO">
        SELECT w.report_no reportNo,
               w.case_times caseTimes
        FROM clm_whole_case_base w
        WHERE NOT EXISTS (
            SELECT 1 FROM clms_send_reinsurance_record r
            WHERE r.report_no = w.report_no
            AND r.case_times = w.case_times
        )  AND EXISTS (
            SELECT 1 FROM CLMS_REPORT_CUSTOMER c
            WHERE c.report_no = w.report_no
        ) AND is_register = 'Y'
        ORDER BY w.case_times
    </select>

    <select id="selectCount" resultType="int">
        SELECT COUNT(*)
        FROM clms_send_reinsurance_record
        WHERE REPORT_NO = #{reportNo}
        AND CASE_TIMES = #{caseTimes}
        AND claim_type = #{claimType}
        AND IS_SUCCESS = 'Y'
    </select>
</mapper>

