<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.instalment.InstalmentFailMapper">

	<resultMap type="com.paic.ncbs.claim.model.dto.instalment.InstalmentFailDTO" id="InstalmentFailDTOResult">
		<result column="ID_AHCS_INSTALMENT_FAIL" property="idAhcsInstalmentFail" />
		<result column="REPORT_NO" property="reportNo" />
		<result column="CASE_TIMES" property="caseTimes" />
		<result column="POLICY_NO" property="policyNo" />
		<result column="NOTICE_NO" property="noticeNo" />
		<result column="QR_CODE_URL" property="qrCodeUrl" />
		<result column="STATUS" property="status" />
		<result column="QUERY_TIME" property="queryTime" />
		<result column="REMARK" property="remark" />
	</resultMap>


	<select id="findByReportNoAndCaseTimes" resultMap="InstalmentFailDTOResult">
		SELECT
		T.ID_AHCS_INSTALMENT_FAIL,
		T.REPORT_NO,
		T.CASE_TIMES,
		T.POLICY_NO,
		T.NOTICE_NO,
		T.QR_CODE_URL,
		T.STATUS,
		T.QUERY_TIME,
		T.REMARK
		FROM CLMS_INSTALMENT_FAIL T
		WHERE T.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		AND T.CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
	</select>

	<select id="findAllPaymentItem" resultType="com.paic.ncbs.claim.model.dto.pay.PaymentItemComData">
		select pi.report_no as reportNo,
			   pi.case_times as caseTimes,
			   pi.policy_no as policyNo,
			   pi.case_no as caseNo,
			   pi.id_clm_payment_item as idClmPaymentItem,
			   pi.id_clm_payment_info as idClmPaymentInfo,
			   pi.payment_item_status as paymentItemStatus,
			   pi.payment_type as paymentType,
			   pi.claim_type as claimType
		from clm_payment_item pi
				 inner join clm_notice_item_relation nir
							on pi.id_clm_payment_item = nir.id_clm_payment_item
				 inner join clm_payment_notice pn
							on pn.id_clm_payment_notice = nir.id_clm_payment_notice
		where pi.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		  AND pi.CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		union
		select pi.report_no as reportNo,
			   pi.case_times as caseTimes,
			   pi.policy_no as policyNo,
			   pi.case_no as caseNo,
			   pi.id_clm_payment_item as idClmPaymentItem,
			   pi.id_clm_payment_info as idClmPaymentInfo,
			   pi.payment_item_status as paymentItemStatus,
			   pi.payment_type as paymentType,
			   pi.claim_type as claimType
		from clm_payment_item pi
		where pi.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		  AND pi.CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		  and pi.payment_item_status != '90'
	</select>


</mapper>