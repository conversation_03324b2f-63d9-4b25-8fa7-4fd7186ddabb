<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.PersonDiagnoseMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.duty.PersonDiagnoseDTO" id="result">
        <id column="ID_AHCS_PERSON_DIAGNOSE" property="personDiagnoseId"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="ID_AHCS_CHANNEL_PROCESS" property="idAhcsChannelProcess"/>
        <result column="DISPLAY_NO" property="displayNo"/>
        <result column="DIAGNOSE_CODE" property="diagnoseCode"/>
        <result column="DIAGNOSE_NAME" property="diagnoseName"/>
        <result column="IS_SURGICAL" property="isSurgical"/>
        <result column="SURGICAL_CODE" property="surgicalCode"/>
        <result column="SURGICAL_NAME" property="surgicalName"/>
        <result column="TASK_ID" property="taskId"/>
        <result column="STATUS" property="status"/>
        <result column="IS_FERTILITY" property="isFertility"/>
        <result column="SURGICAL_TYPE" property="surgicalType"/>
        <result column="SURGICAL_TYPE_NAME" property="surgicalTypeName"/>
        <result column="SURGICAL_NAME" property="surgicalName"/>
        <result column="IS_FERTILITY" property="isFertility"/>
        <result column="ARCHIVE_TIME" property="archiveTime"/>
        <result column="diagnostic_typology_code" property="diagnosticTypologyCode"/>
    </resultMap>

    <insert id="savePersonDiagnose" parameterType="com.paic.ncbs.claim.model.dto.duty.PersonDiagnoseDTO">
        insert into CLMS_person_diagnose
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        DISPLAY_NO,
        DIAGNOSE_CODE,
        IS_SURGICAL,
        SURGICAL_NAME,
        SURGICAL_CODE,
        TASK_ID,
        STATUS,
        IS_FERTILITY,
        SURGICAL_TYPE,
        ARCHIVE_TIME,
        ID_AHCS_PERSON_DIAGNOSE,
        IS_EFFECTIVE,
        diagnostic_typology_code,
        IS_MAIN
        )
        values
        (#{createdBy},
        SYSDATE(),
        #{createdBy},
        SYSDATE(),
        #{reportNo},
        #{caseTimes},
        #{idAhcsChannelProcess},
        #{displayNo},
        #{diagnoseCode},
        #{isSurgical},
        #{surgicalName},
        #{surgicalCode},
        #{taskId},
        #{status},
        #{isFertility},
        #{surgicalType},
        <if test="archiveTime != null ">
            #{archiveTime},
        </if>
        <if test="archiveTime == null ">
            SYSDATE(),
        </if>
		left(hex(uuid()),32),
        'Y',
        #{diagnosticTypologyCode},
        #{isMain}
        )
    </insert>

    <delete id="removePersonDiagnose">
        delete from CLMS_person_diagnose where ID_AHCS_CHANNEL_PROCESS=#{idAhcsChannelProcess}
        <if test="taskId != null and taskId != '' ">
            and TASK_ID = #{taskId}
        </if>
    </delete>

    <update id="updateEffective" parameterType="com.paic.ncbs.claim.model.dto.duty.PersonDiagnoseDTO">
        UPDATE
        CLMS_PERSON_DIAGNOSE
        SET
        UPDATED_BY = #{updatedBy},
        UPDATED_DATE = SYSDATE(),
        IS_EFFECTIVE = 'N'
        WHERE
        ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        <if test="taskId != null and taskId != '' ">
            and TASK_ID = #{taskId}
        </if>
        AND IS_EFFECTIVE = 'Y'
    </update>

    <!-- 根据通道号、环节号获取最新环节的诊断信息 -->
    <select id="getPersonDiagnoseListById" parameterType="string" resultMap="result">
        select t1.ID_AHCS_PERSON_DIAGNOSE,
        t1.REPORT_NO,
        t1.CASE_TIMES,
        t1.ID_AHCS_CHANNEL_PROCESS,
        t1.DISPLAY_NO,
        t1.DIAGNOSE_CODE,
        (SELECT t2.DIAGNOSE_NAME
        FROM CLMS_diagnose_define T2
        WHERE T2.DIAGNOSE_CODE = T1.DIAGNOSE_CODE limit 1) DIAGNOSE_NAME,
        t1.IS_SURGICAL,
        t1.SURGICAL_NAME,
        t1.SURGICAL_CODE,
        t1.IS_FERTILITY,
        t1.SURGICAL_TYPE,
        t1.diagnostic_typology_code
        from CLMS_person_diagnose t1
        where t1.id_ahcs_channel_process = #{idAhcsChannelProcess}
        AND t1.IS_EFFECTIVE = 'Y'
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        and t1.task_id =
        (select * from
        (select t.task_id from CLMS_person_diagnose t where
        t.id_ahcs_channel_process = #{idAhcsChannelProcess}
        AND t.IS_EFFECTIVE = 'Y'
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        order by t.created_date desc)
        as temp limit 1
        )
        ORDER BY t1.DISPLAY_NO
    </select>

    <!-- 根据通道号、环节号获取最新环节的诊断信息 -->
    <select id="getPersonDiagnoseListByReportNo" parameterType="string" resultMap="result">
        select t1.ID_AHCS_PERSON_DIAGNOSE,
        t1.REPORT_NO,
        t1.CASE_TIMES,
        t1.ID_AHCS_CHANNEL_PROCESS,
        t1.DISPLAY_NO,
        t1.DIAGNOSE_CODE,
        (SELECT t2.DIAGNOSE_NAME
        FROM CLMS_diagnose_define t2
        WHERE t2.DIAGNOSE_CODE = t1.DIAGNOSE_CODE limit 1) DIAGNOSE_NAME,
        t1.IS_SURGICAL,
        t1.SURGICAL_NAME,
        t1.SURGICAL_CODE,
        t1.SURGICAL_TYPE,
        t1.IS_FERTILITY,
        (SELECT CCP.VALUE_CHINESE_NAME
        FROM CLM_COMMON_PARAMETER CCP
        WHERE CCP.COLLECTION_CODE = 'AHCS_SURGICAL_TYPE'
        AND CCP.VALUE_CODE = t1.SURGICAL_TYPE) SURGICAL_TYPE_NAME
        from CLMS_person_diagnose t1
        where t1.report_no=#{reportNo} AND t1.case_times=#{caseTimes}
        AND t1.IS_EFFECTIVE = 'Y'
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        and t1.task_id =
        (select * from
        (select t.task_id from CLMS_person_diagnose t where
        t.report_no=#{reportNo} AND t.case_times=#{caseTimes}
        AND t.IS_EFFECTIVE = 'Y'
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID =#{taskId}
        </if>
        order by t.created_date desc)
        as temp limit 1
        )
    </select>

    <!-- 根据通道号获取诊断信息 -->
    <select id="getPersonDiagnoseDTOList" parameterType="string" resultMap="result">
        select pd.REPORT_NO,
        pd.DISPLAY_NO,
        pd.DIAGNOSE_CODE,
        (SELECT dd.DIAGNOSE_NAME
        FROM CLMS_diagnose_define dd
        WHERE pd.DIAGNOSE_CODE = dd.DIAGNOSE_CODE limit 1) DIAGNOSE_NAME,
        pd.IS_SURGICAL,
        pd.SURGICAL_CODE,
        (select t.OPERATION_NAME from clms_therapy_operation t
            where t.OPERATION_CODE = pd.SURGICAL_CODE limit 1)SURGICAL_NAME,
        pd.TASK_ID,
        pd.STATUS,
        pd.IS_FERTILITY,
        pd.SURGICAL_TYPE,
        (CASE WHEN pd.SURGICAL_TYPE = '01' THEN '切开复位'
            WHEN pd.SURGICAL_TYPE = '02' THEN '未切开复位'
            WHEN pd.SURGICAL_TYPE = '03' THEN '其他'
            END) SURGICAL_TYPE_NAME
        from CLMS_person_diagnose pd
        where pd.id_ahcs_channel_process = #{idAhcsChannelProcess}
        AND pd.IS_EFFECTIVE = 'Y'
        <if test="status != null and status != '' ">
            and pd.STATUS = #{status}
        </if>
        <if test="taskId != null and taskId != '' ">
            and pd.TASK_ID = #{taskId}
        </if>
        and pd.task_id =
        (select * from
        (select t.task_id from CLMS_person_diagnose t where
        t.id_ahcs_channel_process = #{idAhcsChannelProcess}
        AND t.IS_EFFECTIVE = 'Y'
        <if test="status != null and status != '' ">
            and t.STATUS = #{status}
        </if>
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        order by t.created_date desc)
        as temp limit 1
        )
    </select>

    <!-- 新增多条 诊断信息 -->
    <insert id="addPersonDiagnoseList">
        insert into CLMS_person_diagnose
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        DISPLAY_NO,
        DIAGNOSE_CODE,
        IS_SURGICAL,
        SURGICAL_NAME,
        SURGICAL_CODE,
        TASK_ID,
        STATUS,
        IS_FERTILITY,
        SURGICAL_TYPE,
        archive_time)
        <foreach collection="personDiagnoseList" index="index" item="item" open="(" close=")" separator="union all">
            select #{userId},
            SYSDATE(),
            #{userId},
            SYSDATE(),
            #{item.reportNo},
            #{caseTimes},
            #{channelProcessId},
            #{item.displayNo},
            #{item.diagnoseCode},
            #{item.isSurgical},
            #{item.surgicalName},
            #{item.surgicalCode},
            #{item.taskId},
            #{item.status} ,
            #{item.isFertility},
            #{item.surgicalType} ,
            <if test="item.archiveTime != null ">
                #{item.archiveTime}
            </if>
            <if test="item.archiveTime == null ">
                SYSDATE()
            </if>
            from DUAL
        </foreach>
    </insert>

    <!-- 根据通道号、环节号获取诊断信息 -->
    <select id="getPersonDiagnoseListByCt" parameterType="string" resultMap="result">
        select pd.REPORT_NO,
        pd.DISPLAY_NO,
        pd.DIAGNOSE_CODE,
        pd.IS_SURGICAL,
        pd.SURGICAL_NAME,
        pd.SURGICAL_CODE,
        pd.TASK_ID,
        pd.STATUS ,
        pd.IS_FERTILITY,
        pd.SURGICAL_TYPE,
        pd.ARCHIVE_TIME
        from CLMS_person_diagnose pd
        where pd.id_ahcs_channel_process = #{idAhcsChannelProcess}
        and pd.STATUS = '1'
        and pd.TASK_ID = #{taskId}
        AND pd.IS_EFFECTIVE = 'Y'
    </select>

    <!-- 获取是否手术、手术名称、诊断信息code -->
    <select id="getSurgicalInfo" resultMap="result">
        select pd.IS_SURGICAL,
        pd.SURGICAL_NAME,
        pd.SURGICAL_CODE,
        pd.DIAGNOSE_CODE
        from CLMS_PERSON_DIAGNOSE pd
        where pd.REPORT_NO = #{reportNo}
        and pd.CASE_TIMES = #{caseTimes}
        and pd.STATUS = '1'
        AND pd.IS_EFFECTIVE = 'Y'
        and pd.task_id =
        (select * from
        (select t.task_id from CLMS_person_diagnose t where
        t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        and t.STATUS = '1'
        AND t.IS_EFFECTIVE = 'Y'
        and t.TASK_ID in
        <foreach collection="tacheCodeList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by t.UPDATED_DATE desc)
        limit 1
        )
    </select>

    <!-- 获取是否手术、手术名称、诊断信息code -->
    <select id="getSurgicalByTaskCode" resultMap="result">
        select pd.IS_SURGICAL,
        pd.SURGICAL_NAME,
        pd.SURGICAL_CODE,
        pd.DIAGNOSE_CODE
        from CLMS_PERSON_DIAGNOSE pd
        where pd.REPORT_NO = #{reportNo}
        and pd.CASE_TIMES = #{caseTimes}
        and pd.STATUS = '1'
        and pd.TASK_ID = #{taskId}
        AND pd.IS_EFFECTIVE = 'Y'
    </select>

    <!-- 获取上海平台诊断代码 -->
    <select id="getDiseaseCodeList" resultType="string">
        select (select pc.platform_code
        from CLMS_platform_code_rel pc
        where pc.plat_name = 'SH'
        and pc.object_name = 'diagnosis'
        and pc.param_code = pd.DIAGNOSE_CODE
        limit 1)
        from CLMS_PERSON_DIAGNOSE pd
        where pd.REPORT_NO = #{reportNo}
        and pd.CASE_TIMES = #{caseTimes}
        and pd.DIAGNOSE_CODE is not null
        AND pd.IS_EFFECTIVE = 'Y'
        and pd.TASK_ID =
        (select task_id from
        (select pd1.task_id from CLMS_person_diagnose pd1
        where pd1.REPORT_NO=#{reportNo}
        and pd1.CASE_TIMES=#{caseTimes}
        and pd1.DIAGNOSE_CODE is not null
        AND pd1.IS_EFFECTIVE = 'Y'
        order by pd1.created_date desc)
        as temp limit 1
        )
    </select>

    <!-- 根据通道号、环节号获取最新环节的伤残信息 -->
    <select id="getPersonDisabilityList" parameterType="string" resultMap="result">
        select t.ID_AHCS_PERSON_DISABILITY,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.ID_AHCS_CHANNEL_PROCESS,
        t.DISABILITY_NO,
        t.DISABILITY_CODE,
        t.DISABILITY_CLAUSE,
        t.DISABILITY_GRADE,
        t.ASSESSMENT_DATE,
        t.APPRAISAL_SITUATION,
        t.APPRAISAL_DEPARTMENT,
        t.APPRAISER_NAME,
        t.APPRAISER_NAME_SECOND,
        t.IS_COOPERATE,
        t.TASK_ID,
        t.STATUS
        from CLMS_person_disability t
        where t.id_ahcs_channel_process = #{idAhcsChannelProcess}
        AND t.IS_EFFECTIVE = 'Y'
        <if test="status != null and status != '' ">
            and t.STATUS = #{status}
        </if>
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        and t.task_id =
        (select * from
        (select t1.TASK_ID from CLMS_person_disability t1 where
        t1.ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        AND t1.IS_EFFECTIVE = 'Y'
        <if test="status != null and status != '' ">
            and t1.STATUS = #{status}
        </if>
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        order by t1.CREATED_DATE desc)
        limit 1
        )
    </select>

    <select id="getList" parameterType="com.paic.ncbs.claim.model.dto.duty.PersonDisabilityDTO" resultMap="result">
        select t.ID_AHCS_PERSON_DISABILITY,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.ID_AHCS_CHANNEL_PROCESS,
        t.DISABILITY_NO,
        t.DISABILITY_CODE,
        t.DISABILITY_CLAUSE,
        t.DISABILITY_GRADE,
        t.ASSESSMENT_DATE,
        t.APPRAISAL_SITUATION,
        t.APPRAISAL_DEPARTMENT,
        t.APPRAISER_NAME,
        t.APPRAISER_NAME_SECOND,
        t.IS_COOPERATE
        from CLMS_person_disability t
        where t.report_no = #{personDisability.reportNo}
        and t.case_times = #{personDisability.caseTimes}
        AND t.IS_EFFECTIVE = 'Y'
        <if test="personDisability.taskId != null and personDisability.taskId != '' ">
            and t.TASK_ID = #{personDisability.taskId}
        </if>
        and t.task_id =
        (select * from
        (select t1.task_id from CLMS_person_disability t1 where
        t1.report_no = #{personDisability.reportNo}
        and t1.case_times = #{personDisability.caseTimes}
        AND t1.IS_EFFECTIVE = 'Y'
        <if test="personDisability.taskId != null and personDisability.taskId != '' ">
            and t1.TASK_ID = #{personDisability.taskId}
        </if>
        order by t1.created_date desc)
        limit 1
        )
        order by t.DISABILITY_GRADE
    </select>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        INSERT INTO CLMS_PERSON_DIAGNOSE (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_AHCS_PERSON_DIAGNOSE,
            REPORT_NO,
            CASE_TIMES,
            ID_AHCS_CHANNEL_PROCESS,
            DISPLAY_NO,
            DIAGNOSE_CODE,
            IS_FERTILITY,
            IS_SURGICAL,
            SURGICAL_NAME,
            SURGICAL_TYPE,
            TASK_ID,
            STATUS,
            SURGICAL_CODE,
            ARCHIVE_TIME,
            IS_EFFECTIVE,
            ID_AHCS_ADDITIONAL_SURVEY,
            DIAGNOSTIC_TYPOLOGY_CODE
        )
        SELECT
            #{userId},
            NOW(),
            #{userId},
            NOW(),
            LEFT(HEX(UUID()), 32),
            REPORT_NO,
            #{reopenCaseTimes},
            #{idClmChannelProcess},
            DISPLAY_NO,
            DIAGNOSE_CODE,
            IS_FERTILITY,
            IS_SURGICAL,
            SURGICAL_NAME,
            SURGICAL_TYPE,
            TASK_ID,
            STATUS,
            SURGICAL_CODE,
            NOW(),
            IS_EFFECTIVE,
            ID_AHCS_ADDITIONAL_SURVEY,
            DIAGNOSTIC_TYPOLOGY_CODE
        FROM CLMS_PERSON_DIAGNOSE
        WHERE REPORT_NO=#{reportNo}
        AND CASE_TIMES=#{caseTimes}
        AND IS_EFFECTIVE='Y'
    </insert>

    <select id="getPersonDiagnoseInfo" parameterType="java.lang.String" resultMap="result">
        select DIAGNOSE_CODE,DIAGNOSE_NAME,report_no from
            clms_person_diagnose
        where
            report_no = #{reportNo}
    </select>
</mapper>