<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.PersonHospitalMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.duty.PersonHospitalDTO" id="personHospitalMap">
        <id column="ID_AHCS_PERSON_HOSPITAL" property="idAhcsPersonHospital"/>
        <result column="CREATED_BY" property="createdBy"/>
        <result column="CREATED_DATE" property="createdDate"/>
        <result column="UPDATED_BY" property="updatedBy"/>
        <result column="UPDATED_DATE" property="updatedDate"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="ID_AHCS_CHANNEL_PROCESS" property="idAhcsChannelProcess"/>
        <result column="HOSPITAL_CODE" property="hospitalCode"/>
        <result column="HOSPITAL_NAME" property="hospitalName"/>
        <result column="HOSPITAL_GRADE" property="hospitalGrade"/>
        <result column="IS_COOPERATION" property="isCooperation"/>
        <result column="IS_SOCIAL_INSURANCE" property="isSocialInsurance"/>
        <result column="IS_APPOINTED_HOSPITAL" property="isAppointedHospital"/>
        <result column="HOSPITAL_PROPERTY_DES" property="hospitalPropertyDes"/>
        <result column="SUBJECT_CODE" property="subjectCode"/>
        <result column="BED_CODE" property="bedCode"/>
        <result column="HOSPITALIZATION_NUMBER" property="hospitalizationNumber"/>
        <result column="WARD_BUMBER" property="wardNumber"/>
        <result column="MEDICAL_STATUS" property="medicalStatus"/>
        <result column="THERAPY_TYPE" property="therapyType"/>
        <result column="TASK_ID " property="taskId"/>
    </resultMap>

    <!-- 批量增加操作 -->
    <insert id="addPersonHospitalList" parameterType="java.util.List">
        insert into
        CLMS_PERSON_HOSPITAL
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        ID_AHCS_CHANNEL_PROCESS,
        CASE_TIMES,
        HOSPITAL_CODE,
        HOSPITAL_NAME,
        HOSPITAL_GRADE,
        IS_COOPERATION,
        IS_SOCIAL_INSURANCE,
        IS_APPOINTED_HOSPITAL,
        HOSPITAL_PROPERTY_DES,
        SUBJECT_CODE,
        BED_CODE,
        HOSPITALIZATION_NUMBER,
        WARD_BUMBER,
        MEDICAL_STATUS,
        THERAPY_TYPE,
        TASK_ID,
        STATUS,
        archive_time,
        ID_AHCS_PERSON_HOSPITAL
        )
        <foreach collection="list" item="item" index="index" separator="union all">
            select
            #{item.createdBy},
            SYSDATE(),
            #{item.updatedBy},
            SYSDATE(),
            #{item.reportNo},
            #{item.idAhcsChannelProcess},
            #{item.caseTimes},
            #{item.hospitalCode},
            #{item.hospitalName},
            #{item.hospitalGrade},
            #{item.isCooperation},
            #{item.isSocialInsurance},
            #{item.isAppointedHospital},
            #{item.hospitalPropertyDes},
            #{item.subjectCode},
            #{item.bedCode},
            #{item.hospitalizationNumber},
            #{item.wardNumber},
            #{item.medicalStatus},
            #{item.therapyType},
            #{item.taskId} ,
            #{item.status} ,
            SYSDATE(),
            left(hex(uuid()),32)
            from dual
        </foreach>
    </insert>

    <update id="modifyPersonHospitalList" parameterType="java.util.List">
        <foreach collection="params" index="index" item="item" open="begin" separator=";" close=";end;">
            update
            CLMS_PERSON_HOSPITAL
            set
            UPDATED_BY=#{updatedBy},
            UPDATED_DATE=SYSDATE(),
            HOSPITAL_CODE=#{item.hospitalCode},
            HOSPITAL_NAME=#{item.hospitalName},
            HOSPITAL_GRADE=#{item.hospitalGrade},
            IS_COOPERATION=#{item.isCooperation},
            IS_SOCIAL_INSURANCE=#{item.isSocialInsurance},
            IS_APPOINTED_HOSPITAL=#{item.isAppointedHospital},
            HOSPITAL_PROPERTY_DES=#{item.hospitalPropertyDes},
            SUBJECT_CODE=#{item.subjectCode},
            BED_CODE=#{item.bedCode},
            HOSPITALIZATION_NUMBER=#{item.hospitalizationNumber},
            WARD_BUMBER=#{item.wardNumber},
            MEDICAL_STATUS=#{item.medicalStatus},
            THERAPY_TYPE=#{item.therapyType},
            status=#{item.status}
            where ID_AHCS_CHANNEL_PROCESS=#{item.idAhcsChannelProcess} and CASE_TIMES=#{item.caseTimes}
            <if test="taskId != null and taskId != '' ">
                and TASK_ID = #{item.taskId}
            </if>
        </foreach>
    </update>

    <select id="getPersonHospitalList" resultMap="personHospitalMap"
            parameterType="com.paic.ncbs.claim.model.dto.duty.PersonHospitalDTO">
        select CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_PERSON_HOSPITAL,
        REPORT_NO,
        ID_AHCS_CHANNEL_PROCESS,
        CASE_TIMES,
        HOSPITAL_CODE,
        HOSPITAL_NAME,
        HOSPITAL_GRADE,
        IS_COOPERATION,
        IS_SOCIAL_INSURANCE,
        IS_APPOINTED_HOSPITAL,
        HOSPITAL_PROPERTY_DES,
        SUBJECT_CODE,
        BED_CODE,
        HOSPITALIZATION_NUMBER,
        WARD_BUMBER,
        MEDICAL_STATUS,
        THERAPY_TYPE,
        TASK_ID,
        STATUS
        from
        CLMS_PERSON_HOSPITAL
        where REPORT_NO=#{reportNo} and CASE_TIMES=#{caseTimes}
        <if test="taskId != null and taskId != '' ">
            and TASK_ID = #{taskId}
        </if>
        and task_id =
        (select * from
        (select t1.task_id from CLMS_PERSON_HOSPITAL t1 where
        t1.REPORT_NO=#{reportNo} and t1.CASE_TIMES=#{caseTimes}
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        order by t1.created_date desc)
        as temp limit 1
        )
    </select>

    <select id="getPersonHospital" resultMap="personHospitalMap">
        select CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_PERSON_HOSPITAL,
        REPORT_NO,
        ID_AHCS_CHANNEL_PROCESS,
        CASE_TIMES,
        HOSPITAL_CODE,
        HOSPITAL_NAME,
        HOSPITAL_GRADE,
        IS_COOPERATION,
        IS_SOCIAL_INSURANCE,
        IS_APPOINTED_HOSPITAL,
        HOSPITAL_PROPERTY_DES,
        SUBJECT_CODE,
        BED_CODE,
        HOSPITALIZATION_NUMBER,
        WARD_BUMBER,
        MEDICAL_STATUS,
        THERAPY_TYPE,
        TASK_ID,
        STATUS
        from CLMS_PERSON_HOSPITAL
        where REPORT_NO=#{reportNo} and CASE_TIMES=#{caseTimes}
        <if test="taskId != null and taskId != '' ">
            and TASK_ID = #{taskId}
        </if>
        and task_id =
        (select * from
        (select t1.task_id from CLMS_PERSON_HOSPITAL t1 where
        t1.REPORT_NO=#{reportNo} and t1.CASE_TIMES=#{caseTimes}
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        order by t1.created_date desc)
        as temp limit 1
        )
    </select>

    <select id="getPersonHospitalNumber" resultType="int">
        select count(distinct REPORT_NO)
        from CLMS_PERSON_HOSPITAL
        where HOSPITALIZATION_NUMBER = #{hospitalizationNumber}
        and REPORT_NO != #{reportNo}
    </select>

    <select id="getPersonHospitalByIdAhcsChannelProcess" resultMap="personHospitalMap">
        select CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_PERSON_HOSPITAL,
        REPORT_NO,
        ID_AHCS_CHANNEL_PROCESS,
        CASE_TIMES,
        HOSPITAL_CODE,
        HOSPITAL_NAME,
        HOSPITAL_GRADE,
        IS_COOPERATION,
        IS_SOCIAL_INSURANCE,
        IS_APPOINTED_HOSPITAL,
        HOSPITAL_PROPERTY_DES,
        SUBJECT_CODE,
        BED_CODE,
        HOSPITALIZATION_NUMBER,
        WARD_BUMBER,
        MEDICAL_STATUS,
        THERAPY_TYPE,
        TASK_ID,
        STATUS
        from
        CLMS_PERSON_HOSPITAL
        where ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        <if test="taskId != null and taskId != '' ">
            and TASK_ID = #{taskId}
        </if>
        <if test="status != null and status != '' ">
            and STATUS = #{status}
        </if>
        and task_id =
        (select * from
        (select t1.task_id from CLMS_PERSON_HOSPITAL t1 where
        t1.ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        <if test="status != null and status != '' ">
            and t1.STATUS = #{status}
        </if>
        order by t1.created_date desc)
        as temp limit 1
        )
    </select>

    <delete id="removePersonHospital" parameterType="com.paic.ncbs.claim.model.dto.duty.PersonHospitalDTO">
        delete from
        CLMS_PERSON_HOSPITAL
        where ID_AHCS_CHANNEL_PROCESS=#{idAhcsChannelProcess} and CASE_TIMES=#{caseTimes} and
        HOSPITAL_CODE=#{hospitalCode}
        <if test="taskId != null and taskId != '' ">
            and TASK_ID = #{taskId}
        </if>
    </delete>

    <delete id="removePersonHospitalByIdAhcsChannelProcess"
            parameterType="com.paic.ncbs.claim.model.dto.duty.PersonHospitalDTO">
        delete from
        CLMS_PERSON_HOSPITAL
        where ID_AHCS_CHANNEL_PROCESS=#{idAhcsChannelProcess}
        <if test="taskId != null and taskId != '' ">
            and TASK_ID = #{taskId}
        </if>
    </delete>

    <!-- 获取医院代码 -->
    <select id="getHospitalCode" resultType="string">
        select HOSPITAL_CODE
        from CLMS_PERSON_HOSPITAL ph
        where ph.REPORT_NO = #{reportNo}
        and ph.CASE_TIMES = #{caseTimes}
        and ph.STATUS = '1'
        and task_id =
        (select * from
        (select t1.task_id from CLMS_PERSON_HOSPITAL t1 where
        t1.REPORT_NO = #{reportNo}
        and t1.CASE_TIMES = #{caseTimes}
        and t1.STATUS = '1'
        order by t1.UPDATED_DATE desc)
        as temp limit 1
        )
        limit 1
    </select>
    <select id="getPersonHospitals" resultMap="personHospitalMap">
        select
        (select
        t1.value_chinese_name
        from clm_common_parameter t1
        where t1.collection_code='AHCS_MEDICAL_STATUS'
        and t1.value_code=t.medical_status) medical_status,
        (select
        t1.value_chinese_name
        from clm_common_parameter t1
        where t1.collection_code='AHCS_THERAPY'
        and t1.value_code=t.therapy_type) therapy_type,
        t.hospital_name,t.subject_code from CLMS_person_hospital t
        where ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        <if test="taskId != null and taskId != '' ">
            and TASK_ID = #{taskId}
        </if>
        <if test="status != null and status != '' ">
            and STATUS = #{status}
        </if>
        and task_id =
        (select * from
        (select t1.task_id from CLMS_PERSON_HOSPITAL t1 where
        t1.ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        <if test="status != null and status != '' ">
            and t1.STATUS = #{status}
        </if>
        order by t1.created_date desc)
        as temp limit 1
        )
    </select>

    <!-- 获取医院名称 -->
    <select id="getHospitalName" resultType="string">
        select ph.HOSPITAL_NAME
        from CLMS_PERSON_HOSPITAL ph
        where ph.REPORT_NO = #{reportNo}
        and ph.CASE_TIMES = #{caseTimes}
        and ph.HOSPITAL_NAME is not null
        and ph.task_id =
        (select * from
        (select t1.task_id from CLMS_PERSON_HOSPITAL t1 where
        t1.REPORT_NO = #{reportNo}
        and t1.CASE_TIMES = #{caseTimes}
        and t1.HOSPITAL_NAME is not null
        order by t1.UPDATED_DATE desc)
        as temp limit 1
        )
        limit 1
    </select>

    <!-- 获取医院科室信息 -->
    <select id="getHospitalInfoByReportNo" resultMap="personHospitalMap">
        select ph.HOSPITAL_NAME,
        ph.HOSPITAL_CODE,
        ph.SUBJECT_CODE,
        ph.BED_CODE
        from CLMS_PERSON_HOSPITAL ph
        where ph.REPORT_NO = #{reportNo}
        and ph.CASE_TIMES = #{caseTimes}
        and ph.HOSPITAL_CODE is not null
        and ph.task_id =
        (select * from
        (select t1.task_id from CLMS_PERSON_HOSPITAL t1
        where t1.REPORT_NO = #{reportNo}
        and t1.CASE_TIMES = #{caseTimes}
        and t1.HOSPITAL_CODE is not null
        order by t1.created_date desc)
        limit 1
        )
        limit 1
    </select>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        INSERT INTO CLMS_PERSON_HOSPITAL (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_AHCS_PERSON_HOSPITAL,
            REPORT_NO,
            CASE_TIMES,
            ID_AHCS_CHANNEL_PROCESS,
            HOSPITAL_CODE,
            HOSPITAL_NAME,
            HOSPITAL_GRADE,
            IS_COOPERATION,
            IS_SOCIAL_INSURANCE,
            IS_APPOINTED_HOSPITAL,
            HOSPITAL_PROPERTY_DES,
            SUBJECT_CODE,
            BED_CODE,
            HOSPITALIZATION_NUMBER,
            WARD_BUMBER,
            MEDICAL_STATUS,
            THERAPY_TYPE,
            TASK_ID,
            STATUS,
            ARCHIVE_TIME,
            ID_AHCS_ADDITIONAL_SURVEY
        )
        SELECT
            #{userId},
            NOW(),
            #{userId},
            NOW(),
            LEFT(HEX(UUID()), 32),
            REPORT_NO,
            #{reopenCaseTimes},
            #{idClmChannelProcess},
            HOSPITAL_CODE,
            HOSPITAL_NAME,
            HOSPITAL_GRADE,
            IS_COOPERATION,
            IS_SOCIAL_INSURANCE,
            IS_APPOINTED_HOSPITAL,
            HOSPITAL_PROPERTY_DES,
            SUBJECT_CODE,
            BED_CODE,
            HOSPITALIZATION_NUMBER,
            WARD_BUMBER,
            MEDICAL_STATUS,
            THERAPY_TYPE,
            TASK_ID,
            STATUS,
            NOW(),
            ID_AHCS_ADDITIONAL_SURVEY
        FROM CLMS_PERSON_HOSPITAL
        WHERE REPORT_NO=#{reportNo}
        AND CASE_TIMES=#{caseTimes}
    </insert>
</mapper>
