<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.DisabilityAppraisalMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.checkloss.DisabilityAppraisalDTO" id="disabilityAppraisalMap">
        <id column="ID_AHCS_DISABILITY_APPRAISAL" property="idAhcsDisabilityAppraisal"/>
        <result column="CREATED_BY" property="createdBy"/>
        <result column="CREATED_DATE" property="createdDate"/>
        <result column="UPDATED_BY" property="updatedBy"/>
        <result column="UPDATED_DATE" property="updatedDate"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="ID_AHCS_CHANNEL_PROCESS" property="idAhcsChannelProcess"/>
        <result column="IS_APPRAISAL" property="isAppraisal"/>
    </resultMap>

    <insert id="addDisabilityAppraisal" parameterType="com.paic.ncbs.claim.model.dto.checkloss.DisabilityAppraisalDTO">
        insert into
        CLMS_DISABILITY_APPRAISAL
        ( CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_DISABILITY_APPRAISAL,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        IS_APPRAISAL,
        STATUS
        )
        values
        (#{createdBy},
        sysdate(),
        #{updatedBy},
        sysdate(),
        #{idAhcsDisabilityAppraisal},
        #{reportNo},
        #{caseTimes},
        #{idAhcsChannelProcess,jdbcType=VARCHAR},
        #{isAppraisal,jdbcType=VARCHAR},
        #{status,jdbcType=VARCHAR}
        )
    </insert>

    <update id="modifyDisabilityAppraisal" parameterType="com.paic.ncbs.claim.model.dto.checkloss.DisabilityAppraisalDTO">
        update
        CLMS_DISABILITY_APPRAISAL
        set
        UPDATED_BY=#{updatedBy},
        UPDATED_DATE=sysdate(),
        IS_APPRAISAL=#{isAppraisal,jdbcType=VARCHAR},
        STATUS=#{status,jdbcType=VARCHAR}
        where ID_AHCS_CHANNEL_PROCESS=#{idAhcsChannelProcess} and CASE_TIMES=#{caseTimes}
    </update>

    <select id="getDisabilityAppraisal" resultMap="disabilityAppraisalMap"
            parameterType="com.paic.ncbs.claim.model.dto.checkloss.DisabilityAppraisalDTO">
        select CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_DISABILITY_APPRAISAL,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        IS_APPRAISAL,
        STATUS
        from CLMS_DISABILITY_APPRAISAL
        where REPORT_NO=#{reportNo} and CASE_TIMES=#{caseTimes}
    </select>

    <select id="getIsAppraisal" resultMap="disabilityAppraisalMap">
        select IS_APPRAISAL from CLMS_DISABILITY_APPRAISAL
        where ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
    </select>

    <select id="getDisabilityAppraisalByIdAhcsChannelProcess" resultMap="disabilityAppraisalMap">
        select CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_DISABILITY_APPRAISAL,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        IS_APPRAISAL,
        STATUS
        from CLMS_DISABILITY_APPRAISAL
        where ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
    </select>

    <delete id="removeDisabilityAppraisalByIdAhcsChannelProcess">
        delete from
        CLMS_DISABILITY_APPRAISAL
        where ID_AHCS_CHANNEL_PROCESS=#{idAhcsChannelProcess}
    </delete>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        INSERT INTO CLMS_DISABILITY_APPRAISAL (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_AHCS_DISABILITY_APPRAISAL,
            REPORT_NO,
            CASE_TIMES,
            ID_AHCS_CHANNEL_PROCESS,
            IS_APPRAISAL,
            STATUS
        )
        SELECT
            #{userId},
            NOW(),
            #{userId},
            NOW(),
            MD5(UUID()),
            REPORT_NO,
            #{reopenCaseTimes},
            #{idClmChannelProcess},
            IS_APPRAISAL,
            STATUS
        FROM CLMS_DISABILITY_APPRAISAL
        WHERE REPORT_NO=#{reportNo}
        AND CASE_TIMES=#{caseTimes}
    </insert>
</mapper>
