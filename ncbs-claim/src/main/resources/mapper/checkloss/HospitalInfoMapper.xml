<?xml version =  "1.0" encoding =  "UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.HospitalInfoMapper">


    <resultMap type="com.paic.ncbs.claim.model.vo.checkloss.HospitalInfoVO" id="hospitalMap">
        <id column="ID_HOSPITAL_INFO" property="idHospitalInfo"/>
        <result column="department_code" property="departmentCode"/>
        <result column="DEPARTMENT_ABBR_NAME" property="departmentName"/>
        <result column="hospital_code" property="hospitalCode"/>
        <result column="hospital_name" property="hospitalName"/>
        <result column="hospital_type" property="hospitalPropertyDes"/>
        <result column="hospital_grade" property="grade"/>
        <result column="hospital_level" property="hospitalLevel"/>
        <result column="province_code" property="provinceCode"/>
        <result column="prefecture_level_code" property="prefectureLevelCode"/>
        <result column="district_code" property="districtCode"/>
        <result column="address_desc" property="addressDesc"/>
        <result column="is_medicare_designated" property="isAppointedHospital"/>
        <result column="is_cooperate" property="isCooperate"/>
        <result column="department_link_man" property="departmentLinkMan"/>
        <result column="is_valid" property="isValid"/>
        <result column="maintain_time" property="maintainTime"/>
        <result column="updated_date" property="updatedDate"/>
        <result column="created_date" property="createdDate"/>
    </resultMap>
    <select id="getHospitalList" resultMap="hospitalMap">
        select
        t1.ID_HOSPITAL_INFO,
        t1.department_code,
        t1.hospital_code,
        t1.hospital_name,
        t1.hospital_grade,
        t1.hospital_level,
        t1.hospital_type,
        (CASE t1.is_profit_making
        WHEN 'N' THEN 'N'
        WHEN '2' THEN 'N'
        WHEN '1' THEN 'Y'
        WHEN 'Y' THEN 'Y'
        END) AS is_profit_making,
        t1.is_medicare_designated,
        t1.is_cooperate,
        t1.department_link_man,
        t1.is_valid,
        t1.maintain_time,
        t1.updated_date,
        t1.province_code,
        t1.prefecture_level_code,
        t1.district_code,
        t1.address_desc
        from
        hospital_info t1
        where t1.is_valid = 'Y'
        <if test="hospitalName != null and hospitalName != '' ">
            and t1.hospital_name like CONCAT('%',#{hospitalName},'%')
        </if>
        <if test="orgType != null and orgType != '' ">
            and t1.org_type = #{orgType}
        </if>
        order by t1.updated_date desc
        limit 1000
    </select>

    <select id="getHospitalByCode" resultMap="hospitalMap">
        select
        t1.hospital_code,
        t2.department_code,
        t2.department_abbr_name,
        t1.prefecture_level_code,
        (select t3.city_chinese_name from city_define t3
        where t1.prefecture_level_code=t3.city_code ) city_chinese_name
        from
        hospital_info t1,
        department_define t2
        where t1.department_code=t2.department_code
        and t1.IS_VALID='Y'
        and ( t2.invalidate_date is null or t2.invalidate_date > NOW() )
        and t1.hospital_code= #{hospitalCode}
    </select>

    <select id="getHospitalCodeByName" resultMap="hospitalMap">
        select
        t1.hospital_code,
        t1.hospital_name,
        t1.prefecture_level_code
        from
        hospital_info t1
        where
        t1.IS_VALID='Y'
        and t1.hospital_name= #{hospitalName}
        <if test="orgType != null and orgType != '' ">
            and t1.org_type = #{orgType}
        </if>
        limit 1
    </select>

    <select id="getHostpitalvo" resultType="com.paic.ncbs.claim.model.dto.duty.PersonHospitalDTO">
        select MEDICAL_STATUS medicalStatus,HOSPITAL_GRADE hospitalGrade,THERAPY_TYPE therapyType,HOSPITAL_NAME hospitalName,HOSPITAL_PROPERTY_DES hospitalPropertyDes
        from clms_person_hospital where ID_AHCS_CHANNEL_PROCESS=#{idAhcsChannelProcess}
        and TASK_ID=#{taskId} and `STATUS`=#{status}
    </select>


    <insert id="saveHospitalInfo" parameterType="com.paic.ncbs.claim.model.vo.checkloss.HospitalInfoVO">
        insert into HOSPITAL_INFO
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_HOSPITAL_INFO,
        HOSPITAL_CODE,
        HOSPITAL_NAME,
        HOSPITAL_GRADE,
        HOSPITAL_LEVEL,
        HOSPITAL_TYPE,
        IS_VALID,
        ORG_TYPE,
        PROVINCE_CODE,
        PREFECTURE_LEVEL_CODE,
        DISTRICT_CODE,
        ADDRESS_DESC
        )
        values
        (#{createdBy},
        now(),
        #{updatedBy},
        now(),
        #{idHospitalInfo},
        #{hospitalCode,jdbcType=VARCHAR},
        #{hospitalName,jdbcType=VARCHAR},
        #{grade,jdbcType=VARCHAR},
        #{hospitalLevel,jdbcType=VARCHAR},
        #{hospitalPropertyDes,jdbcType=VARCHAR},
        #{isValid,jdbcType=VARCHAR},
        #{orgType,jdbcType=VARCHAR},
        #{provinceCode,jdbcType=VARCHAR},
        #{prefectureLevelCode,jdbcType=VARCHAR},
        #{districtCode,jdbcType=VARCHAR},
        #{addressDesc,jdbcType=VARCHAR}
        )
    </insert>

    <update id="updateHospitalInfo" parameterType="com.paic.ncbs.claim.model.vo.checkloss.HospitalInfoVO">
        UPDATE
        HOSPITAL_INFO
        SET
        UPDATED_BY = #{updatedBy},
        UPDATED_DATE = now(),
        HOSPITAL_CODE = #{hospitalCode},
        HOSPITAL_NAME = #{hospitalName},
        HOSPITAL_GRADE = #{grade},
        HOSPITAL_LEVEL = #{hospitalLevel},
        HOSPITAL_TYPE = #{hospitalPropertyDes},
        IS_VALID = #{isValid},
        ORG_TYPE = #{orgType},
        PROVINCE_CODE = #{provinceCode},
        PREFECTURE_LEVEL_CODE = #{prefectureLevelCode},
        DISTRICT_CODE = #{districtCode},
        ADDRESS_DESC = #{addressDesc}
        WHERE
        ID_HOSPITAL_INFO = #{idHospitalInfo}
    </update>

    <select id="getHospitalByKey" resultMap="hospitalMap">
        select
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_HOSPITAL_INFO,
            HOSPITAL_CODE,
            HOSPITAL_NAME,
            HOSPITAL_GRADE,
            HOSPITAL_TYPE,
            PROVINCE_CODE,
            PREFECTURE_LEVEL_CODE,
            DISTRICT_CODE,
            ADDRESS_DESC,
            HOSPITAL_LEVEL,
            IS_VALID
        from
            hospital_info
        where
            ID_HOSPITAL_INFO= #{idHospitalInfo}
    </select>

    <select id="getHospitalInfoList" resultMap="hospitalMap">
        select
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_HOSPITAL_INFO,
        HOSPITAL_CODE,
        HOSPITAL_NAME,
        HOSPITAL_GRADE,
        HOSPITAL_LEVEL,
        HOSPITAL_TYPE,
        IS_VALID,
        ORG_TYPE,
        PROVINCE_CODE,
        PREFECTURE_LEVEL_CODE,
        DISTRICT_CODE,
        ADDRESS_DESC
        from
        hospital_info
        where 1=1
        <if test="hospitalName != null and hospitalName != '' ">
            and  hospital_name like CONCAT('%',#{hospitalName},'%')
        </if>
        <if test="hospitalFullName != null and hospitalFullName != '' ">
            and  hospital_name = #{hospitalFullName}
        </if>
        <if test="isValid != null and isValid != '' ">
            and IS_VALID = #{isValid}
        </if>
        <if test="maintainStartDate != null">
            and <![CDATA[ date_format(UPDATED_DATE,'%Y-%m-%d') >= date_format( #{maintainStartDate},'%Y-%m-%d') ]]>
        </if>
        <if test="maintainEndDate != null">
            and <![CDATA[ date_format(UPDATED_DATE,'%Y-%m-%d') <= date_format( #{maintainEndDate},'%Y-%m-%d') ]]>
        </if>
        <if test="grade != null and grade != '' ">
            and HOSPITAL_GRADE = #{grade}
        </if>
        <if test="hospitalCode != null and hospitalCode != '' ">
            and HOSPITAL_CODE = #{hospitalCode}
        </if>
        order by
        CASE
        WHEN HOSPITAL_GRADE = '三级' THEN 1
        WHEN HOSPITAL_GRADE = '二级' THEN 2
        WHEN HOSPITAL_GRADE = '一级' THEN 3
        ELSE 4
        END,HOSPITAL_CODE
    </select>

    <select id="getHospitalInfoCount" resultType="java.lang.Integer">
        SELECT MAX(CAST(HOSPITAL_CODE AS UNSIGNED))
        FROM hospital_info
        where 1=1
        <if test="hospitalCode != null and hospitalCode != '' ">
            and HOSPITAL_CODE != #{hospitalCode}
        </if>
    </select>
</mapper>