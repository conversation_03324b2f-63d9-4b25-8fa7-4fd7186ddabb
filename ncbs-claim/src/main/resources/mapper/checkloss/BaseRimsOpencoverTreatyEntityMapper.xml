<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.BaseRimsOpencoverTreatyEntityMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.checkloss.BaseRimsOpencoverTreatyEntity">
        <id column="ID_RIMS_OPENCOVER_TREATY" property="idRimsOpencoverTreaty" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="TREATY_NO" property="treatyNo" jdbcType="VARCHAR"/>
        <result column="TREATY_CODE" property="treatyCode" jdbcType="VARCHAR"/>
        <result column="TREATY_NAME" property="treatyName" jdbcType="VARCHAR"/>
        <result column="OPENCOVER_TYPE" property="opencoverType" jdbcType="VARCHAR"/>
        <result column="TREATY_BEGIN_DATE" property="treatyBeginDate" jdbcType="TIMESTAMP"/>
        <result column="TREATY_END_DATE" property="treatyEndDate" jdbcType="TIMESTAMP"/>
        <result column="SURPLUS_LIMIT" property="surplusLimit" jdbcType="DECIMAL"/>
        <result column="CURRENCY_CODE" property="currencyCode" jdbcType="VARCHAR"/>
        <result column="BUSI_SYSTEM_TYPE" property="busiSystemType" jdbcType="VARCHAR"/>
        <result column="IS_FOR_BATCH" property="isForBatch" jdbcType="VARCHAR"/>
        <result column="IS_USED" property="isUsed" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="TREATY_STATUS" property="treatyStatus" jdbcType="VARCHAR"/>
        <result column="IS_EFFECTIVE_BEFORE" property="isEffectiveBefore" jdbcType="VARCHAR"/>
        <result column="TREATY_CLASS" property="treatyClass" jdbcType="VARCHAR"/>
        <result column="GROUP_ID" property="groupId" jdbcType="VARCHAR"/>
        <result column="OWN_TYPE" property="ownType" jdbcType="VARCHAR"/>
        <result column="OWN_CURRENCY" property="ownCurrency" jdbcType="VARCHAR"/>
        <result column="OWN_AMOUNT" property="ownAmount" jdbcType="DECIMAL"/>
        <result column="OWN_AMOUNT_LIMIT" property="ownAmountLimit" jdbcType="DECIMAL"/>
        <result column="PARENT_TREATY_NO" property="parentTreatyNo" jdbcType="VARCHAR"/>
        <result column="CEDED_PROP" property="cededProp" jdbcType="DECIMAL"/>
        <result column="COMMISSION_RATE" property="commissionRate" jdbcType="DECIMAL"/>
        <result column="QS_CEDED_PROP" property="qsCededProp" jdbcType="DECIMAL"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID_RIMS_OPENCOVER_TREATY, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, TREATY_NO,
        TREATY_CODE, TREATY_NAME, OPENCOVER_TYPE, TREATY_BEGIN_DATE, TREATY_END_DATE, SURPLUS_LIMIT,
        CURRENCY_CODE, BUSI_SYSTEM_TYPE, IS_FOR_BATCH, IS_USED, REMARK, TREATY_STATUS, IS_EFFECTIVE_BEFORE,
        TREATY_CLASS, GROUP_ID, OWN_TYPE, OWN_CURRENCY, OWN_AMOUNT, OWN_AMOUNT_LIMIT, PARENT_TREATY_NO,
        CEDED_PROP, COMMISSION_RATE, QS_CEDED_PROP
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from BASE_RIMS_OPENCOVER_TREATY
        where ID_RIMS_OPENCOVER_TREATY = #{idRimsOpencoverTreaty,jdbcType=VARCHAR}
    </select>
    <select id="getInfoByTreatyNo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from BASE_RIMS_OPENCOVER_TREATY
        where TREATY_NO = #{treatyNo,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from BASE_RIMS_OPENCOVER_TREATY
        where ID_RIMS_OPENCOVER_TREATY = #{idRimsOpencoverTreaty,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.checkloss.BaseRimsOpencoverTreatyEntity">
        insert into BASE_RIMS_OPENCOVER_TREATY (ID_RIMS_OPENCOVER_TREATY, CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        TREATY_NO, TREATY_CODE, TREATY_NAME,
        OPENCOVER_TYPE, TREATY_BEGIN_DATE, TREATY_END_DATE,
        SURPLUS_LIMIT, CURRENCY_CODE, BUSI_SYSTEM_TYPE,
        IS_FOR_BATCH, IS_USED, REMARK,
        TREATY_STATUS, IS_EFFECTIVE_BEFORE, TREATY_CLASS,
        GROUP_ID, OWN_TYPE, OWN_CURRENCY,
        OWN_AMOUNT, OWN_AMOUNT_LIMIT, PARENT_TREATY_NO,
        CEDED_PROP, COMMISSION_RATE, QS_CEDED_PROP
        )
        values (#{idRimsOpencoverTreaty,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
        #{createdDate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP},
        #{treatyNo,jdbcType=VARCHAR}, #{treatyCode,jdbcType=VARCHAR}, #{treatyName,jdbcType=VARCHAR},
        #{opencoverType,jdbcType=VARCHAR}, #{treatyBeginDate,jdbcType=TIMESTAMP}, #{treatyEndDate,jdbcType=TIMESTAMP},
        #{surplusLimit,jdbcType=DECIMAL}, #{currencyCode,jdbcType=VARCHAR}, #{busiSystemType,jdbcType=VARCHAR},
        #{isForBatch,jdbcType=VARCHAR}, #{isUsed,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
        #{treatyStatus,jdbcType=VARCHAR}, #{isEffectiveBefore,jdbcType=VARCHAR}, #{treatyClass,jdbcType=VARCHAR},
        #{groupId,jdbcType=VARCHAR}, #{ownType,jdbcType=VARCHAR}, #{ownCurrency,jdbcType=VARCHAR},
        #{ownAmount,jdbcType=DECIMAL}, #{ownAmountLimit,jdbcType=DECIMAL}, #{parentTreatyNo,jdbcType=VARCHAR},
        #{cededProp,jdbcType=DECIMAL}, #{commissionRate,jdbcType=DECIMAL}, #{qsCededProp,jdbcType=DECIMAL}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.checkloss.BaseRimsOpencoverTreatyEntity">
        insert into BASE_RIMS_OPENCOVER_TREATY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idRimsOpencoverTreaty != null">
                ID_RIMS_OPENCOVER_TREATY,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="treatyNo != null">
                TREATY_NO,
            </if>
            <if test="treatyCode != null">
                TREATY_CODE,
            </if>
            <if test="treatyName != null">
                TREATY_NAME,
            </if>
            <if test="opencoverType != null">
                OPENCOVER_TYPE,
            </if>
            <if test="treatyBeginDate != null">
                TREATY_BEGIN_DATE,
            </if>
            <if test="treatyEndDate != null">
                TREATY_END_DATE,
            </if>
            <if test="surplusLimit != null">
                SURPLUS_LIMIT,
            </if>
            <if test="currencyCode != null">
                CURRENCY_CODE,
            </if>
            <if test="busiSystemType != null">
                BUSI_SYSTEM_TYPE,
            </if>
            <if test="isForBatch != null">
                IS_FOR_BATCH,
            </if>
            <if test="isUsed != null">
                IS_USED,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
            <if test="treatyStatus != null">
                TREATY_STATUS,
            </if>
            <if test="isEffectiveBefore != null">
                IS_EFFECTIVE_BEFORE,
            </if>
            <if test="treatyClass != null">
                TREATY_CLASS,
            </if>
            <if test="groupId != null">
                GROUP_ID,
            </if>
            <if test="ownType != null">
                OWN_TYPE,
            </if>
            <if test="ownCurrency != null">
                OWN_CURRENCY,
            </if>
            <if test="ownAmount != null">
                OWN_AMOUNT,
            </if>
            <if test="ownAmountLimit != null">
                OWN_AMOUNT_LIMIT,
            </if>
            <if test="parentTreatyNo != null">
                PARENT_TREATY_NO,
            </if>
            <if test="cededProp != null">
                CEDED_PROP,
            </if>
            <if test="commissionRate != null">
                COMMISSION_RATE,
            </if>
            <if test="qsCededProp != null">
                QS_CEDED_PROP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idRimsOpencoverTreaty != null">
                #{idRimsOpencoverTreaty,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="treatyNo != null">
                #{treatyNo,jdbcType=VARCHAR},
            </if>
            <if test="treatyCode != null">
                #{treatyCode,jdbcType=VARCHAR},
            </if>
            <if test="treatyName != null">
                #{treatyName,jdbcType=VARCHAR},
            </if>
            <if test="opencoverType != null">
                #{opencoverType,jdbcType=VARCHAR},
            </if>
            <if test="treatyBeginDate != null">
                #{treatyBeginDate,jdbcType=TIMESTAMP},
            </if>
            <if test="treatyEndDate != null">
                #{treatyEndDate,jdbcType=TIMESTAMP},
            </if>
            <if test="surplusLimit != null">
                #{surplusLimit,jdbcType=DECIMAL},
            </if>
            <if test="currencyCode != null">
                #{currencyCode,jdbcType=VARCHAR},
            </if>
            <if test="busiSystemType != null">
                #{busiSystemType,jdbcType=VARCHAR},
            </if>
            <if test="isForBatch != null">
                #{isForBatch,jdbcType=VARCHAR},
            </if>
            <if test="isUsed != null">
                #{isUsed,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="treatyStatus != null">
                #{treatyStatus,jdbcType=VARCHAR},
            </if>
            <if test="isEffectiveBefore != null">
                #{isEffectiveBefore,jdbcType=VARCHAR},
            </if>
            <if test="treatyClass != null">
                #{treatyClass,jdbcType=VARCHAR},
            </if>
            <if test="groupId != null">
                #{groupId,jdbcType=VARCHAR},
            </if>
            <if test="ownType != null">
                #{ownType,jdbcType=VARCHAR},
            </if>
            <if test="ownCurrency != null">
                #{ownCurrency,jdbcType=VARCHAR},
            </if>
            <if test="ownAmount != null">
                #{ownAmount,jdbcType=DECIMAL},
            </if>
            <if test="ownAmountLimit != null">
                #{ownAmountLimit,jdbcType=DECIMAL},
            </if>
            <if test="parentTreatyNo != null">
                #{parentTreatyNo,jdbcType=VARCHAR},
            </if>
            <if test="cededProp != null">
                #{cededProp,jdbcType=DECIMAL},
            </if>
            <if test="commissionRate != null">
                #{commissionRate,jdbcType=DECIMAL},
            </if>
            <if test="qsCededProp != null">
                #{qsCededProp,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.checkloss.BaseRimsOpencoverTreatyEntity">
        update BASE_RIMS_OPENCOVER_TREATY
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="treatyNo != null">
                TREATY_NO = #{treatyNo,jdbcType=VARCHAR},
            </if>
            <if test="treatyCode != null">
                TREATY_CODE = #{treatyCode,jdbcType=VARCHAR},
            </if>
            <if test="treatyName != null">
                TREATY_NAME = #{treatyName,jdbcType=VARCHAR},
            </if>
            <if test="opencoverType != null">
                OPENCOVER_TYPE = #{opencoverType,jdbcType=VARCHAR},
            </if>
            <if test="treatyBeginDate != null">
                TREATY_BEGIN_DATE = #{treatyBeginDate,jdbcType=TIMESTAMP},
            </if>
            <if test="treatyEndDate != null">
                TREATY_END_DATE = #{treatyEndDate,jdbcType=TIMESTAMP},
            </if>
            <if test="surplusLimit != null">
                SURPLUS_LIMIT = #{surplusLimit,jdbcType=DECIMAL},
            </if>
            <if test="currencyCode != null">
                CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR},
            </if>
            <if test="busiSystemType != null">
                BUSI_SYSTEM_TYPE = #{busiSystemType,jdbcType=VARCHAR},
            </if>
            <if test="isForBatch != null">
                IS_FOR_BATCH = #{isForBatch,jdbcType=VARCHAR},
            </if>
            <if test="isUsed != null">
                IS_USED = #{isUsed,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="treatyStatus != null">
                TREATY_STATUS = #{treatyStatus,jdbcType=VARCHAR},
            </if>
            <if test="isEffectiveBefore != null">
                IS_EFFECTIVE_BEFORE = #{isEffectiveBefore,jdbcType=VARCHAR},
            </if>
            <if test="treatyClass != null">
                TREATY_CLASS = #{treatyClass,jdbcType=VARCHAR},
            </if>
            <if test="groupId != null">
                GROUP_ID = #{groupId,jdbcType=VARCHAR},
            </if>
            <if test="ownType != null">
                OWN_TYPE = #{ownType,jdbcType=VARCHAR},
            </if>
            <if test="ownCurrency != null">
                OWN_CURRENCY = #{ownCurrency,jdbcType=VARCHAR},
            </if>
            <if test="ownAmount != null">
                OWN_AMOUNT = #{ownAmount,jdbcType=DECIMAL},
            </if>
            <if test="ownAmountLimit != null">
                OWN_AMOUNT_LIMIT = #{ownAmountLimit,jdbcType=DECIMAL},
            </if>
            <if test="parentTreatyNo != null">
                PARENT_TREATY_NO = #{parentTreatyNo,jdbcType=VARCHAR},
            </if>
            <if test="cededProp != null">
                CEDED_PROP = #{cededProp,jdbcType=DECIMAL},
            </if>
            <if test="commissionRate != null">
                COMMISSION_RATE = #{commissionRate,jdbcType=DECIMAL},
            </if>
            <if test="qsCededProp != null">
                QS_CEDED_PROP = #{qsCededProp,jdbcType=DECIMAL},
            </if>
        </set>
        where ID_RIMS_OPENCOVER_TREATY = #{idRimsOpencoverTreaty,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.checkloss.BaseRimsOpencoverTreatyEntity">
        update BASE_RIMS_OPENCOVER_TREATY
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        TREATY_NO = #{treatyNo,jdbcType=VARCHAR},
        TREATY_CODE = #{treatyCode,jdbcType=VARCHAR},
        TREATY_NAME = #{treatyName,jdbcType=VARCHAR},
        OPENCOVER_TYPE = #{opencoverType,jdbcType=VARCHAR},
        TREATY_BEGIN_DATE = #{treatyBeginDate,jdbcType=TIMESTAMP},
        TREATY_END_DATE = #{treatyEndDate,jdbcType=TIMESTAMP},
        SURPLUS_LIMIT = #{surplusLimit,jdbcType=DECIMAL},
        CURRENCY_CODE = #{currencyCode,jdbcType=VARCHAR},
        BUSI_SYSTEM_TYPE = #{busiSystemType,jdbcType=VARCHAR},
        IS_FOR_BATCH = #{isForBatch,jdbcType=VARCHAR},
        IS_USED = #{isUsed,jdbcType=VARCHAR},
        REMARK = #{remark,jdbcType=VARCHAR},
        TREATY_STATUS = #{treatyStatus,jdbcType=VARCHAR},
        IS_EFFECTIVE_BEFORE = #{isEffectiveBefore,jdbcType=VARCHAR},
        TREATY_CLASS = #{treatyClass,jdbcType=VARCHAR},
        GROUP_ID = #{groupId,jdbcType=VARCHAR},
        OWN_TYPE = #{ownType,jdbcType=VARCHAR},
        OWN_CURRENCY = #{ownCurrency,jdbcType=VARCHAR},
        OWN_AMOUNT = #{ownAmount,jdbcType=DECIMAL},
        OWN_AMOUNT_LIMIT = #{ownAmountLimit,jdbcType=DECIMAL},
        PARENT_TREATY_NO = #{parentTreatyNo,jdbcType=VARCHAR},
        CEDED_PROP = #{cededProp,jdbcType=DECIMAL},
        COMMISSION_RATE = #{commissionRate,jdbcType=DECIMAL},
        QS_CEDED_PROP = #{qsCededProp,jdbcType=DECIMAL}
        where ID_RIMS_OPENCOVER_TREATY = #{idRimsOpencoverTreaty,jdbcType=VARCHAR}
    </update>
</mapper>