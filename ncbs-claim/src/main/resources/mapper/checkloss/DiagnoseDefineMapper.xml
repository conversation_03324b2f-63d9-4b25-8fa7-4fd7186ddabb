<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.DiagnoseDefineMapper">
	<resultMap type="com.paic.ncbs.claim.model.dto.checkloss.DiagnoseDefineDTO" id="result">
		<id column="ID_AHCS_DIAGNOSE_DEFINE" property="diagnoseDefineId"/>
		<result column="DIAGNOSE_CODE" property="diagnoseCode"/>
		<result column="MNEMONIC_CODE" property="mnemonicCode"/>
		<result column="DIAGNOSE_NAME" property="diagnoseName"/>
		<result column="SEX_CONSTRAINT" property="sexConstraint"/>
		<result column="CURATIVE_CONSTRAINT" property="curativeConstraint"/>
		<result column="DIAGNOSE_TYPE" property="diagnoseType"/>
		<result column="CREATED_DATE" property="createdDate"/>
		<result column="UPDATED_DATE" property="updatedDate"/>
	</resultMap>
	 
	 <select id="getDiagnoseDefines" parameterType="string" resultMap="result">
		select CREATED_BY,
		       CREATED_DATE,
		       UPDATED_BY,
		       UPDATED_DATE,
		       ID_AHCS_DIAGNOSE_DEFINE,
		       DIAGNOSE_CODE,
		       MNEMONIC_CODE,
		       DIAGNOSE_NAME,
		       SEX_CONSTRAINT,
		       CURATIVE_CONSTRAINT
		  from CLMS_DIAGNOSE_DEFINE
		  where 1=1
		  <if test="searchStr != null and searchStr != ''.trim()">
		  AND  DIAGNOSE_NAME like concat('%',#{searchStr},'%')
		  </if>
		 <if test="orgType != null and orgType != ''">
			 AND  org_type = #{orgType}
		 </if>
		 limit 1000;
	</select>


	<select id="getDiagnoseCode" parameterType="java.lang.String" resultType="java.lang.String">
		select DIAGNOSE_CODE
		from  CLMS_DIAGNOSE_DEFINE
		where DIAGNOSE_CODE = #{diagnoseCode,jdbcType=VARCHAR}
		limit 1
	</select>

	<select id="getDiagnoseName" parameterType="java.lang.String" resultType="java.lang.String">
		select DIAGNOSE_NAME
		from  CLMS_DIAGNOSE_DEFINE
		where DIAGNOSE_CODE = #{diagnoseCode,jdbcType=VARCHAR}
	</select>

	<select id="getDiagnoseDefineList" parameterType="com.paic.ncbs.claim.model.dto.checkloss.DiagnoseDefineDTO" resultMap="result">
		select CREATED_BY,
		CREATED_DATE,
		UPDATED_BY,
		UPDATED_DATE,
		ID_AHCS_DIAGNOSE_DEFINE,
		DIAGNOSE_CODE,
		MNEMONIC_CODE,
		DIAGNOSE_NAME,
		SEX_CONSTRAINT,
		CURATIVE_CONSTRAINT,
		DIAGNOSE_TYPE
		from CLMS_DIAGNOSE_DEFINE
		where 1=1
		<if test="diagnoseName != null and diagnoseName != ''.trim()">
			AND  DIAGNOSE_NAME like concat('%',#{diagnoseName},'%')
		</if>
		<if test="diagnoseCode != null and diagnoseCode != ''.trim()">
			AND  DIAGNOSE_CODE like concat('',#{diagnoseCode},'%')
		</if>
	</select>

	<select id="getDiagnoseDefineDTOs" parameterType="string" resultMap="result">
		select CREATED_BY,
		CREATED_DATE,
		UPDATED_BY,
		UPDATED_DATE,
		ID_AHCS_DIAGNOSE_DEFINE,
		DIAGNOSE_CODE,
		MNEMONIC_CODE,
		DIAGNOSE_NAME,
		SEX_CONSTRAINT,
		CURATIVE_CONSTRAINT
		from CLMS_DIAGNOSE_DEFINE
		where 1=1
		<if test="searchStr != null and searchStr != ''.trim()">
			AND  DIAGNOSE_NAME like concat('%',#{searchStr},'%')
		</if>
		<if test="orgType != null and orgType != ''">
			AND  org_type = #{orgType}
		</if>
		<if test="diagnoseCode != null and diagnoseCode != ''">
			AND  DIAGNOSE_CODE like concat('',#{diagnoseCode},'%')
		</if>
		limit 1000;
	</select>

</mapper>