<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.BaseProdPifTermEntityMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.checkloss.BaseProdPifTermEntity">
        <id column="ID_PROD_PIF_TERM" property="idProdPifTerm" jdbcType="VARCHAR"/>
        <result column="SALES_REGION" property="salesRegion" jdbcType="VARCHAR"/>
        <result column="INSURED_TYPE" property="insuredType" jdbcType="VARCHAR"/>
        <result column="TERM_CODE" property="termCode" jdbcType="VARCHAR"/>
        <result column="TERM_NAME" property="termName" jdbcType="VARCHAR"/>
        <result column="INSURED_SPECIES" property="insuredSpecies" jdbcType="VARCHAR"/>
        <result column="INSURED_SPECIES_NAME" property="insuredSpeciesName" jdbcType="VARCHAR"/>
        <result column="TERM_URL" property="termUrl" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="DOCUMENT_GROUP_ID" property="documentGroupId" jdbcType="VARCHAR"/>
        <result column="FLAG" property="flag" jdbcType="CHAR"/>
        <result column="TERM_EN_NAME" property="termEnName" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID_PROD_PIF_TERM, SALES_REGION, INSURED_TYPE, TERM_CODE, TERM_NAME, INSURED_SPECIES,
        INSURED_SPECIES_NAME, TERM_URL, CREATED_DATE, CREATED_BY, UPDATED_DATE, UPDATED_BY,
        DOCUMENT_GROUP_ID, FLAG, TERM_EN_NAME
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from BASE_PROD_PIF_TERM
        where ID_PROD_PIF_TERM = #{idProdPifTerm,jdbcType=VARCHAR}
    </select>
    <select id="getDocumentGroupId" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from BASE_PROD_PIF_TERM
        where TERM_CODE = #{termCode,jdbcType=VARCHAR}
    </select>
    <select id="getTermNamesByTermCodes" resultType="com.paic.ncbs.claim.model.dto.checkloss.SmallTermDTO"
            parameterType="java.util.List">
        select TERM_NAME termName,TERM_CODE termCode
        from BASE_PROD_PIF_TERM
        where TERM_CODE in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from BASE_PROD_PIF_TERM
        where ID_PROD_PIF_TERM = #{idProdPifTerm,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.checkloss.BaseProdPifTermEntity">
        insert into BASE_PROD_PIF_TERM (ID_PROD_PIF_TERM, SALES_REGION, INSURED_TYPE,
        TERM_CODE, TERM_NAME, INSURED_SPECIES,
        INSURED_SPECIES_NAME, TERM_URL, CREATED_DATE,
        CREATED_BY, UPDATED_DATE, UPDATED_BY,
        DOCUMENT_GROUP_ID, FLAG, TERM_EN_NAME
        )
        values (#{idProdPifTerm,jdbcType=VARCHAR}, #{salesRegion,jdbcType=VARCHAR}, #{insuredType,jdbcType=VARCHAR},
        #{termCode,jdbcType=VARCHAR}, #{termName,jdbcType=VARCHAR}, #{insuredSpecies,jdbcType=VARCHAR},
        #{insuredSpeciesName,jdbcType=VARCHAR}, #{termUrl,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP},
        #{createdBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR},
        #{documentGroupId,jdbcType=VARCHAR}, #{flag,jdbcType=CHAR}, #{termEnName,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.checkloss.BaseProdPifTermEntity">
        insert into BASE_PROD_PIF_TERM
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idProdPifTerm != null">
                ID_PROD_PIF_TERM,
            </if>
            <if test="salesRegion != null">
                SALES_REGION,
            </if>
            <if test="insuredType != null">
                INSURED_TYPE,
            </if>
            <if test="termCode != null">
                TERM_CODE,
            </if>
            <if test="termName != null">
                TERM_NAME,
            </if>
            <if test="insuredSpecies != null">
                INSURED_SPECIES,
            </if>
            <if test="insuredSpeciesName != null">
                INSURED_SPECIES_NAME,
            </if>
            <if test="termUrl != null">
                TERM_URL,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="documentGroupId != null">
                DOCUMENT_GROUP_ID,
            </if>
            <if test="flag != null">
                FLAG,
            </if>
            <if test="termEnName != null">
                TERM_EN_NAME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idProdPifTerm != null">
                #{idProdPifTerm,jdbcType=VARCHAR},
            </if>
            <if test="salesRegion != null">
                #{salesRegion,jdbcType=VARCHAR},
            </if>
            <if test="insuredType != null">
                #{insuredType,jdbcType=VARCHAR},
            </if>
            <if test="termCode != null">
                #{termCode,jdbcType=VARCHAR},
            </if>
            <if test="termName != null">
                #{termName,jdbcType=VARCHAR},
            </if>
            <if test="insuredSpecies != null">
                #{insuredSpecies,jdbcType=VARCHAR},
            </if>
            <if test="insuredSpeciesName != null">
                #{insuredSpeciesName,jdbcType=VARCHAR},
            </if>
            <if test="termUrl != null">
                #{termUrl,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="documentGroupId != null">
                #{documentGroupId,jdbcType=VARCHAR},
            </if>
            <if test="flag != null">
                #{flag,jdbcType=CHAR},
            </if>
            <if test="termEnName != null">
                #{termEnName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.checkloss.BaseProdPifTermEntity">
        update BASE_PROD_PIF_TERM
        <set>
            <if test="salesRegion != null">
                SALES_REGION = #{salesRegion,jdbcType=VARCHAR},
            </if>
            <if test="insuredType != null">
                INSURED_TYPE = #{insuredType,jdbcType=VARCHAR},
            </if>
            <if test="termCode != null">
                TERM_CODE = #{termCode,jdbcType=VARCHAR},
            </if>
            <if test="termName != null">
                TERM_NAME = #{termName,jdbcType=VARCHAR},
            </if>
            <if test="insuredSpecies != null">
                INSURED_SPECIES = #{insuredSpecies,jdbcType=VARCHAR},
            </if>
            <if test="insuredSpeciesName != null">
                INSURED_SPECIES_NAME = #{insuredSpeciesName,jdbcType=VARCHAR},
            </if>
            <if test="termUrl != null">
                TERM_URL = #{termUrl,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="documentGroupId != null">
                DOCUMENT_GROUP_ID = #{documentGroupId,jdbcType=VARCHAR},
            </if>
            <if test="flag != null">
                FLAG = #{flag,jdbcType=CHAR},
            </if>
            <if test="termEnName != null">
                TERM_EN_NAME = #{termEnName,jdbcType=VARCHAR},
            </if>
        </set>
        where ID_PROD_PIF_TERM = #{idProdPifTerm,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.checkloss.BaseProdPifTermEntity">
        update BASE_PROD_PIF_TERM
        set SALES_REGION = #{salesRegion,jdbcType=VARCHAR},
        INSURED_TYPE = #{insuredType,jdbcType=VARCHAR},
        TERM_CODE = #{termCode,jdbcType=VARCHAR},
        TERM_NAME = #{termName,jdbcType=VARCHAR},
        INSURED_SPECIES = #{insuredSpecies,jdbcType=VARCHAR},
        INSURED_SPECIES_NAME = #{insuredSpeciesName,jdbcType=VARCHAR},
        TERM_URL = #{termUrl,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        DOCUMENT_GROUP_ID = #{documentGroupId,jdbcType=VARCHAR},
        FLAG = #{flag,jdbcType=CHAR},
        TERM_EN_NAME = #{termEnName,jdbcType=VARCHAR}
        where ID_PROD_PIF_TERM = #{idProdPifTerm,jdbcType=VARCHAR}
    </update>
</mapper>