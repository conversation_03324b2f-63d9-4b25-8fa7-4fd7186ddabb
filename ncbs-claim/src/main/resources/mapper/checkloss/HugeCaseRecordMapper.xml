<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.HugeCaseRecordMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.checkloss.HugeCaseRecordDTO" id="HugeCaseRecordMap">
        <id property="idAhcsHugeCaseRecord" column="ID_AHCS_HUGE_CASE_RECORD"/>
        <result property="createdBy" column="CREATED_BY"/>
        <result property="createdDate" column="CREATED_DATE"/>
        <result property="updatedBy" column="UPDATED_BY"/>
        <result property="updatedDate" column="UPDATED_DATE"/>
        <result property="reportNo" column="REPORT_NO"/>
        <result property="caseTimes" column="CASE_TIMES"/>
        <result property="departmentCode" column="DEPARTMENT_CODE"/>
        <result property="reportAmount" column="REPORT_AMOUNT"/>
        <result property="reportUm" column="REPORT_UM"/>
        <result property="reportContent" column="REPORT_CONTENT"/>
        <result property="departmentGuiderUm" column="DEPARTMENT_guider_UM"/>
        <result property="departmentAuditTime" column="DEPARTMENT_AUDIT_TIME"/>
        <result property="departmentAuditOpinion" column="DEPARTMENT_AUDIT_OPINION"/>
        <result property="centreGuiderUm" column="centre_guider_UM"/>
        <result property="centreAuditTime" column="centre_AUDIT_TIME"/>
        <result property="centreAuditOpinion" column="centre_AUDIT_OPINION"/>
        <result property="auditStatus" column="AUDIT_STATUS"/>
        <result property="mailAddressUms" column="MAIL_ADDRESS_UMS"/>
        <result property="departmentAuditLimit" column="DEPARTMENT_AUDIT_LIMIT"/>
    </resultMap>

    <!--	<resultMap type="com.paic.icore.ahcs.common.vo.checkloss.HugeCaseRecordVO" extends="HugeCaseRecordMap" id="HugeCaseRecordVOMap">-->
    <!--		-->
    <!--		<result property="departmentCodeName" column="DEPARTMENT_CODE_NAME" />-->
    <!--		<result property="reportUmName" column="REPORT_UM_NAME" />-->
    <!--		<result property="departmentGuiderUmName" column="DEPARTMENT_guider_UM_NAME" />-->
    <!--		<result property="centreGuiderUmName" column="centre_guider_UM_NAME" />-->
    <!--		-->
    <!--	</resultMap>-->


    <!--	<resultMap type="com.paic.icore.ahcs.common.vo.checkloss.HugeCaseRecordVOForSearch" extends="HugeCaseRecordMap"  id="HugeCaseRecordSearchVOMap">-->
    <!--		-->
    <!--		<result property="processStatus" column="PROCESS_STATUS" />-->
    <!--		<result property="insurancedMan" column="INSURANCED_MAN" />-->
    <!--		<result property="clientType" column="CLIENT_TYPE" />-->
    <!--		<result property="reportDate" column="REPORT_DATE" />-->
    <!--		-->
    <!--	</resultMap>-->
    <!--	-->
    <!--	<resultMap type="com.paic.icore.ahcs.common.vo.checkloss.PolicyInfoVOForHugeCase" id="PolicyInfoVOMap">-->
    <!--		<id property="idAhcsPolicyInfo" column="ID_AHCS_POLICY_INFO" />-->
    <!--		<result property="reportNo" column="REPORT_NO" />-->
    <!--		<result property="policyNo" column="POLICY_NO" />-->
    <!--		<result property="departmentCode" column="DEPARTMENT_CODE" />-->
    <!--		<result property="departmentCodeName" column="DEPARTMENT_CODE_NAME" />-->
    <!--		<result property="policyHolder" column="POLICY_HOLDER" />-->
    <!--		<result property="policyCerNo" column="POLICY_CER_NO" />-->

    <!--		<result property="reinsureScale" column="REINSURE_SCALE" />-->
    <!--		-->
    <!--		<collection property="plans" -->
    <!--			ofType="com.paic.icore.ahcs.common.vo.checkloss.PolicyPlanVOForHugeCase"  -->
    <!--	        select="getPlanDetailInfo"-->
    <!--	        column="{idAhcsPolicyInfo = ID_AHCS_POLICY_INFO }"> -->
    <!--	    </collection>-->
    <!--	</resultMap>-->
    <!--	<resultMap type="com.paic.icore.ahcs.common.vo.checkloss.PolicyPlanVOForHugeCase" id="PolicyPlanVOMap">-->
    <!--		<id property="idAhcsPolicyPlan" column="ID_AHCS_POLICY_PLAN" />-->
    <!--		<result property="planCode" column="PLAN_CODE" />-->
    <!--		<result property="planName" column="PLAN_NAME" />-->
    <!--		<result property="planAmount" column="PLAN_AMOUNT" />-->
    <!--	</resultMap>-->


    <insert id="addHugeCaseRecord" parameterType="com.paic.ncbs.claim.model.dto.checkloss.HugeCaseRecordDTO">
        INSERT INTO CLMS_HUGE_CASE_record (
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_HUGE_CASE_RECORD,
        REPORT_NO,
        CASE_TIMES,
        DEPARTMENT_CODE,
        REPORT_AMOUNT,
        REPORT_UM,
        REPORT_CONTENT,
        DEPARTMENT_guider_UM,
        DEPARTMENT_AUDIT_TIME,
        DEPARTMENT_AUDIT_OPINION,
        centre_guider_UM,
        centre_AUDIT_TIME,
        centre_AUDIT_OPINION,
        AUDIT_STATUS,
        MAIL_ADDRESS_UMS,
        DEPARTMENT_AUDIT_LIMIT
        )
        VALUES (
        #{createdBy ,jdbcType=VARCHAR},
        sysdate(),
        #{updatedBy ,jdbcType=VARCHAR},
        sysdate(),
        #{idAhcsHugeCaseRecord ,jdbcType=VARCHAR},
        #{reportNo ,jdbcType=VARCHAR},
        #{caseTimes ,jdbcType=NUMERIC},
        #{departmentCode ,jdbcType=VARCHAR},
        #{reportAmount ,jdbcType=NUMERIC},
        #{reportUm ,jdbcType=VARCHAR},
        #{reportContent ,jdbcType=VARCHAR},
        #{departmentGuiderUm ,jdbcType=VARCHAR},
        #{departmentAuditTime ,jdbcType=DATE},
        #{departmentAuditOpinion ,jdbcType=VARCHAR},
        #{centreGuiderUm ,jdbcType=VARCHAR},
        #{centreAuditTime ,jdbcType=DATE},
        #{centreAuditOpinion ,jdbcType=VARCHAR},
        #{auditStatus ,jdbcType=VARCHAR},
        #{mailAddressUms ,jdbcType=VARCHAR},
        #{departmentAuditLimit ,jdbcType=NUMERIC}
        )
    </insert>


    <insert id="addHugeCaseRecordList" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            INSERT INTO CLMS_HUGE_CASE_record (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_AHCS_HUGE_CASE_RECORD,
            REPORT_NO,
            CASE_TIMES,
            DEPARTMENT_CODE,
            REPORT_AMOUNT,
            REPORT_UM,
            REPORT_CONTENT,
            DEPARTMENT_guider_UM,
            DEPARTMENT_AUDIT_TIME,
            DEPARTMENT_AUDIT_OPINION,
            centre_guider_UM,
            centre_AUDIT_TIME,
            centre_AUDIT_OPINION,
            AUDIT_STATUS,
            MAIL_ADDRESS_UMS,
            DEPARTMENT_AUDIT_LIMIT
            )
            VALUES (
            #{createdBy ,jdbcType=VARCHAR},
            sysdate(),
            #{updatedBy ,jdbcType=VARCHAR},
            sysdate(),
            #{idAhcsHugeCaseRecord ,jdbcType=VARCHAR},
            #{reportNo ,jdbcType=VARCHAR},
            #{caseTimes ,jdbcType=NUMERIC},
            #{departmentCode ,jdbcType=VARCHAR},
            #{reportAmount ,jdbcType=NUMERIC},
            #{reportUm ,jdbcType=VARCHAR},
            #{reportContent ,jdbcType=VARCHAR},
            #{departmentGuiderUm ,jdbcType=VARCHAR},
            #{departmentAuditTime ,jdbcType=DATE},
            #{departmentAuditOpinion ,jdbcType=VARCHAR},
            #{centreGuiderUm ,jdbcType=VARCHAR},
            #{centreAuditTime ,jdbcType=DATE},
            #{centreAuditOpinion ,jdbcType=VARCHAR},
            #{auditStatus ,jdbcType=VARCHAR},
            #{mailAddressUms ,jdbcType=VARCHAR},
            #{departmentAuditLimit ,jdbcType=NUMERIC}
            )
        </foreach>
    </insert>


    <update id="modifyHugeCaseRecord" parameterType="com.paic.ncbs.claim.model.dto.checkloss.HugeCaseRecordDTO">
        UPDATE CLMS_HUGE_CASE_record
        <set>

            <if test="createdBy != null and createdBy != '' ">
                CREATED_BY = #{createdBy},
            </if>

            <if test="createdDate != null ">
                CREATED_DATE = #{createdDate},
            </if>

            <if test="updatedBy != null and updatedBy != '' ">
                UPDATED_BY = #{updatedBy},
            </if>
            UPDATED_DATE=sysdate(),

            <if test="reportNo != null and reportNo != '' ">
                REPORT_NO = #{reportNo},
            </if>

            <if test="caseTimes != null ">
                CASE_TIMES = #{caseTimes},
            </if>

            <if test="departmentCode != null and departmentCode != '' ">
                DEPARTMENT_CODE = #{departmentCode},
            </if>

            <if test="reportAmount != null ">
                REPORT_AMOUNT = #{reportAmount},
            </if>

            <if test="reportUm != null and reportUm != '' ">
                REPORT_UM = #{reportUm},
            </if>

            <if test="reportContent != null and reportContent != '' ">
                REPORT_CONTENT = #{reportContent},
            </if>

            <if test="departmentGuiderUm != null and departmentGuiderUm != '' ">
                DEPARTMENT_guider_UM = #{departmentGuiderUm},
            </if>

            <if test="departmentAuditTime != null ">
                DEPARTMENT_AUDIT_TIME = #{departmentAuditTime},
            </if>

            <if test="departmentAuditOpinion != null and departmentAuditOpinion != '' ">
                DEPARTMENT_AUDIT_OPINION = #{departmentAuditOpinion},
            </if>

            <if test="centreGuiderUm != null and centreGuiderUm != '' ">
                centre_guider_UM = #{centreGuiderUm},
            </if>

            <if test="centreAuditTime != null ">
                centre_AUDIT_TIME = #{centreAuditTime},
            </if>

            <if test="centreAuditOpinion != null and centreAuditOpinion != '' ">
                centre_AUDIT_OPINION = #{centreAuditOpinion},
            </if>

            <if test="auditStatus != null and auditStatus != '' ">
                AUDIT_STATUS = #{auditStatus},
            </if>

            <if test="mailAddressUms != null and mailAddressUms != '' ">
                MAIL_ADDRESS_UMS = #{mailAddressUms},
            </if>
        </set>
        WHERE ID_AHCS_HUGE_CASE_RECORD=#{idAhcsHugeCaseRecord}
    </update>


    <delete id="removeHugeCaseRecordById" parameterType="String">
        DELETE FROM CLMS_HUGE_CASE_record
        where ID_AHCS_HUGE_CASE_RECORD=#{idAhcsHugeCaseRecord}
    </delete>


    <delete id="deleteHugeCaseRecord" parameterType="com.paic.ncbs.claim.model.dto.checkloss.HugeCaseRecordDTO">
        DELETE FROM CLMS_HUGE_CASE_record
        where 1=1
        <if test="idAhcsHugeCaseRecord != null and idAhcsHugeCaseRecord != '' ">
            AND ID_AHCS_HUGE_CASE_RECORD = #{idAhcsHugeCaseRecord,jdbcType=VARCHAR}
        </if>
        <if test="reportNo != null and reportNo != '' ">
            AND REPORT_NO = #{reportNo,jdbcType=VARCHAR}
        </if>
        <if test="caseTimes != null ">
            AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC},
        </if>
        <if test="departmentCode != null and departmentCode != '' ">
            AND DEPARTMENT_CODE = #{departmentCode,jdbcType=VARCHAR}
        </if>
        <if test="reportAmount != null ">
            AND REPORT_AMOUNT = #{reportAmount,jdbcType=NUMERIC},
        </if>
        <if test="reportUm != null and reportUm != '' ">
            AND REPORT_UM = #{reportUm,jdbcType=VARCHAR}
        </if>
        <if test="reportContent != null and reportContent != '' ">
            AND REPORT_CONTENT = #{reportContent,jdbcType=VARCHAR}
        </if>
        <if test="departmentGuiderUm != null and departmentGuiderUm != '' ">
            AND DEPARTMENT_guider_UM = #{departmentGuiderUm,jdbcType=VARCHAR}
        </if>
        <if test="departmentAuditTime != null ">
            AND DEPARTMENT_AUDIT_TIME = #{departmentAuditTime,jdbcType=DATE},
        </if>
        <if test="departmentAuditOpinion != null and departmentAuditOpinion != '' ">
            AND DEPARTMENT_AUDIT_OPINION = #{departmentAuditOpinion,jdbcType=VARCHAR}
        </if>
        <if test="centreGuiderUm != null and centreGuiderUm != '' ">
            AND centre_guider_UM = #{centreGuiderUm,jdbcType=VARCHAR}
        </if>
        <if test="centreAuditTime != null ">
            AND centre_AUDIT_TIME = #{centreAuditTime,jdbcType=DATE},
        </if>
        <if test="centreAuditOpinion != null and centreAuditOpinion != '' ">
            AND centre_AUDIT_OPINION = #{centreAuditOpinion,jdbcType=VARCHAR}
        </if>
        <if test="auditStatus != null and auditStatus != '' ">
            AND AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR}
        </if>
        <if test="mailAddressUms != null and mailAddressUms != '' ">
            AND MAIL_ADDRESS_UMS = #{mailAddressUms,jdbcType=VARCHAR}
        </if>
    </delete>


    <select id="getHugeCaseRecordById" resultMap="HugeCaseRecordMap">
        select
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_HUGE_CASE_RECORD,
        REPORT_NO,
        CASE_TIMES,
        DEPARTMENT_CODE,
        REPORT_AMOUNT,
        REPORT_UM,
        REPORT_CONTENT,
        DEPARTMENT_guider_UM,
        DEPARTMENT_AUDIT_TIME,
        DEPARTMENT_AUDIT_OPINION,
        centre_guider_UM,
        centre_AUDIT_TIME,
        centre_AUDIT_OPINION,
        AUDIT_STATUS,
        MAIL_ADDRESS_UMS,
        DEPARTMENT_AUDIT_LIMIT
        from CLMS_HUGE_CASE_record
        where ID_AHCS_HUGE_CASE_RECORD=#{idAhcsHugeCaseRecord}
    </select>


    <select id="getHugeCaseRecord" resultMap="HugeCaseRecordMap">
        select
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_HUGE_CASE_RECORD,
        REPORT_NO,
        CASE_TIMES,
        DEPARTMENT_CODE,
        REPORT_AMOUNT,
        REPORT_UM,
        REPORT_CONTENT,
        DEPARTMENT_guider_UM,
        DEPARTMENT_AUDIT_TIME,
        DEPARTMENT_AUDIT_OPINION,
        centre_guider_UM,
        centre_AUDIT_TIME,
        centre_AUDIT_OPINION,
        AUDIT_STATUS,
        MAIL_ADDRESS_UMS,
        DEPARTMENT_AUDIT_LIMIT
        from CLMS_HUGE_CASE_record
        where 1=1
        <if test="idAhcsHugeCaseRecord != null and idAhcsHugeCaseRecord != '' ">
            AND ID_AHCS_HUGE_CASE_RECORD = #{idAhcsHugeCaseRecord,jdbcType=VARCHAR}
        </if>
        <if test="reportNo != null and reportNo != '' ">
            AND REPORT_NO = #{reportNo,jdbcType=VARCHAR}
        </if>
        <if test="caseTimes != null ">
            AND CASE_TIMES = #{caseTimes,jdbcType=NUMERIC},
        </if>
        <if test="departmentCode != null and departmentCode != '' ">
            AND DEPARTMENT_CODE = #{departmentCode,jdbcType=VARCHAR}
        </if>
        <if test="reportAmount != null ">
            AND REPORT_AMOUNT = #{reportAmount,jdbcType=NUMERIC},
        </if>
        <if test="reportUm != null and reportUm != '' ">
            AND REPORT_UM = #{reportUm,jdbcType=VARCHAR}
        </if>
        <if test="reportContent != null and reportContent != '' ">
            AND REPORT_CONTENT = #{reportContent,jdbcType=VARCHAR}
        </if>
        <if test="departmentGuiderUm != null and departmentGuiderUm != '' ">
            AND DEPARTMENT_guider_UM = #{departmentGuiderUm,jdbcType=VARCHAR}
        </if>
        <if test="departmentAuditTime != null ">
            AND DEPARTMENT_AUDIT_TIME = #{departmentAuditTime,jdbcType=DATE},
        </if>
        <if test="departmentAuditOpinion != null and departmentAuditOpinion != '' ">
            AND DEPARTMENT_AUDIT_OPINION = #{departmentAuditOpinion,jdbcType=VARCHAR}
        </if>
        <if test="centreGuiderUm != null and centreGuiderUm != '' ">
            AND centre_guider_UM = #{centreGuiderUm,jdbcType=VARCHAR}
        </if>
        <if test="centreAuditTime != null ">
            AND centre_AUDIT_TIME = #{centreAuditTime,jdbcType=DATE},
        </if>
        <if test="centreAuditOpinion != null and centreAuditOpinion != '' ">
            AND centre_AUDIT_OPINION = #{centreAuditOpinion,jdbcType=VARCHAR}
        </if>
        <if test="auditStatus != null and auditStatus != '' ">
            AND AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR}
        </if>
        <if test="mailAddressUms != null and mailAddressUms != '' ">
            AND MAIL_ADDRESS_UMS = #{mailAddressUms,jdbcType=VARCHAR}
        </if>
    </select>


    <!--	<select id="getHugeCaseRecordByReportNo" resultMap="HugeCaseRecordVOMap">-->
    <!--		select -->
    <!--		CREATED_BY,-->
    <!--		CREATED_DATE,-->
    <!--		UPDATED_BY,-->
    <!--		UPDATED_DATE,-->
    <!--		ID_AHCS_HUGE_CASE_RECORD,-->
    <!--		REPORT_NO,-->
    <!--		CASE_TIMES,-->
    <!--		DEPARTMENT_CODE,-->
    <!--		REPORT_AMOUNT,-->
    <!--		REPORT_UM,-->
    <!--		REPORT_CONTENT,-->
    <!--		DEPARTMENT_guider_UM,-->
    <!--		DEPARTMENT_AUDIT_TIME,-->
    <!--		DEPARTMENT_AUDIT_OPINION,-->
    <!--		centre_guider_UM,-->
    <!--		centre_AUDIT_TIME,-->
    <!--		centre_AUDIT_OPINION,-->
    <!--		AUDIT_STATUS,-->
    <!--		MAIL_ADDRESS_UMS,-->
    <!--		DEPARTMENT_AUDIT_LIMIT,-->
    <!--		-->
    <!--		(select user_name from CLMS_user_info  where user_id=t.REPORT_UM limit 1) as REPORT_UM_NAME,-->
    <!--		(select user_name from CLMS_user_info  where user_id=t.centre_guider_UM limit 1) as centre_guider_UM_NAME,-->
    <!--		(select user_name from CLMS_user_info  where user_id=t.DEPARTMENT_guider_UM limit 1) as DEPARTMENT_guider_UM_NAME,-->
    <!--		(select a.DEPARTMENT_CHINESE_NAME from department_define a where a.department_code=t.DEPARTMENT_CODE limit 1) as DEPARTMENT_CODE_NAME-->
    <!--		-->
    <!--		from CLMS_HUGE_CASE_record t-->
    <!--		where  REPORT_NO  = #{reportNo,jdbcType=VARCHAR}-->
    <!--		AND CASE_TIMES  = #{caseTimes,jdbcType=NUMERIC} -->
    <!--	</select>-->

    <select id="getHugeCaseRecordCount" resultType="int">
        select count(*)
        from CLMS_huge_case_record t
        where report_no = #{reportNo,jdbcType=VARCHAR}
        and case_times = #{caseTimes,jdbcType=NUMERIC}
    </select>


    <!--	<select id="getHugeCaseRecordInPageByParma" resultMap="HugeCaseRecordSearchVOMap">-->
    <!--		select -->
    <!--		t.CREATED_BY,-->
    <!--		t.CREATED_DATE,-->
    <!--		t.UPDATED_BY,-->
    <!--		t.UPDATED_DATE,-->
    <!--		t.ID_AHCS_HUGE_CASE_RECORD,-->
    <!--		t.REPORT_NO,-->
    <!--		t.CASE_TIMES,-->
    <!--		t.DEPARTMENT_CODE,-->
    <!--		t.REPORT_AMOUNT,-->
    <!--		t.REPORT_UM,-->
    <!--		t.REPORT_CONTENT,-->
    <!--		t.DEPARTMENT_guider_UM,-->
    <!--		t.DEPARTMENT_AUDIT_TIME,-->
    <!--		t.DEPARTMENT_AUDIT_OPINION,-->
    <!--		t.centre_guider_UM,-->
    <!--		t.centre_AUDIT_TIME,-->
    <!--		t.centre_AUDIT_OPINION,-->
    <!--		t.AUDIT_STATUS,-->
    <!--		MAIL_ADDRESS_UMS,-->
    <!--		DEPARTMENT_AUDIT_LIMIT-->
    <!--		-->

    <!--		-->
    <!--		-->
    <!--		 from CLMS_HUGE_CASE_record t -->
    <!--		where  1=1-->
    <!--			<if test="startSubmitDate != null">-->
    <!--				and CREATED_DATE&gt;=#{startSubmitDate,jdbcType=TIMESTAMP}-->
    <!--			</if>-->
    <!--			<if test="endSubmitDate != null">-->
    <!--				and CREATED_DATE&lt;=#{endSubmitDate,jdbcType=TIMESTAMP}-->
    <!--			</if>-->
    <!--			-->
    <!--			<if test="reportNo != null and reportNo != '' ">-->
    <!--				AND REPORT_NO  = #{reportNo,jdbcType=VARCHAR} -->
    <!--			</if>-->
    <!--			<if test="departmentCode != null and departmentCode != '' ">-->
    <!--				AND DEPARTMENT_CODE  = #{departmentCode,jdbcType=VARCHAR} -->
    <!--			</if>-->
    <!--			<if test="reportNos != null and reportNos.size() > 0 ">-->
    <!--				AND REPORT_NO  in  -->
    <!--				<foreach collection="reportNos" item="item" index="index" separator="," open="(" close=")">-->
    <!--					#{item,jdbcType=VARCHAR} -->
    <!--				</foreach>-->
    <!--			</if>-->
    <!--			-->
    <!--	</select>-->


    <select id="getReportNoListByParma" resultType="java.lang.String">
        select a.report_no from CLMS_policy_info a where a.id_ahcs_policy_info in (
        select t.id_ahcs_policy_info
        from CLMS_insured_person t where 1=1
        <if test="name != null and name != '' ">
            and UPPER(t.name)=UPPER(#{name,jdbcType=VARCHAR})
        </if>
        <if test="cardNo != null and cardNo != '' ">
            and UPPER(t.CERTIFICATE_NO)=UPPER(#{cardNo,jdbcType=VARCHAR})
        </if>
        )
        <if test="policyNo != null and policyNo != '' ">
            and a.policy_no=#{policyNo,jdbcType=VARCHAR}
        </if>
    </select>


    <select id="getAcceptInsuranceFlagByReportNo" resultType="java.lang.Integer">
        select count(1)
        from CLMS_COINSURE t
        where t.id_ahcs_policy_info in
        (select a.id_ahcs_policy_info
        from CLMS_policy_info a
        where a.report_no = #{reportNo, jdbcType = VARCHAR})
        and t.accept_insurance_flag = '0'
        and t.reinsure_company_code = '3005'
    </select>


    <!--	<select id="getPolicyInfoForMail" resultMap="PolicyInfoVOMap">-->
    <!--		select -->
    <!--		ID_AHCS_POLICY_INFO,-->
    <!--		REPORT_NO,-->
    <!--		POLICY_NO,-->
    <!--		DEPARTMENT_CODE,-->
    <!--		POLICY_CER_NO,-->
    <!--		(select a.DEPARTMENT_CHINESE_NAME from department_define a where a.department_code=t.DEPARTMENT_CODE limit 1) as DEPARTMENT_CODE_NAME,-->
    <!--		(select NAME from CLMS_policy_holder a where a.id_ahcs_policy_info=t.ID_AHCS_POLICY_INFO limit 1) POLICY_HOLDER,-->
    <!--		(select c.REINSURE_SCALE from CLMS_COINSURE c where c.ID_AHCS_POLICY_INFO = t.ID_AHCS_POLICY_INFO and c.REINSURE_COMPANY_CODE = '3005' and (c.COINSURANCE_TYPE = '0' or c.COINSURANCE_TYPE is null)) REINSURE_SCALE-->
    <!--		from CLMS_policy_info t -->
    <!--		where t.report_no=#{reportNo,jdbcType=VARCHAR} -->
    <!--	</select>	-->


    <!--	<select id="getPlanDetailInfo" parameterType="java.util.Map" resultMap="PolicyPlanVOMap">-->
    <!--		select -->
    <!--		ID_AHCS_POLICY_PLAN,-->
    <!--		PLAN_CODE,-->
    <!--		PLAN_NAME,-->
    <!--		(select sum(a.duty_amount) from CLMS_policy_duty a where a.id_ahcs_policy_plan=t.ID_AHCS_POLICY_PLAN) PLAN_AMOUNT-->
    <!--		from CLMS_policy_plan t -->
    <!--		where t.id_ahcs_policy_info=#{idAhcsPolicyInfo,jdbcType=VARCHAR}-->
    <!--	</select>	-->


</mapper>