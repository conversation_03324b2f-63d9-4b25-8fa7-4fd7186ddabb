<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.PersonOtherLossMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.duty.PersonOtherLossDTO" id="personOtherLossListResult">
		<id column="ID_AHCS_PERSON_DISABILITY" property="personDisabilityId" />
		<result column="REPORT_NO" property="reportNo" />
		<result column="CASE_TIMES" property="caseTimes" />
		<result column="ID_AHCS_CHANNEL_PROCESS" property="idAhcsChannelProcess" />
		<result column="CASE_TYPE" property="caseType" />
		<result column="LOSS_AMOUNT" property="lossAmount" />
		<result column="REMARK" property="remark" />
		<result column="TASK_ID" property="taskId" />
		<result column="STATUS" property="status" />
		<result column="ARCHIVE_TIME"  property="archiveTime"/>
	</resultMap>

	<insert id="addPersonOtherLoss">		
		INSERT INTO CLMS_PERSON_OTHER_LOSS(
			ID_AHCS_PERSON_OTHER_LOSS,
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			REPORT_NO,
			CASE_TIMES,
			ID_AHCS_CHANNEL_PROCESS,
			CASE_TYPE,
			LOSS_AMOUNT,
			REMARK,
			TASK_ID,
			STATUS,
			ARCHIVE_TIME
			)
			<foreach collection="personOtherLossList" index="index" item="item" open="(" close=")" separator="union all">	
				 SELECT
				    md5(uuid()),
					 #{item.createdBy},
					SYSDATE(),
					 #{item.updatedBy},
					SYSDATE(),
					 #{item.reportNo},
					 #{item.caseTimes},
					 #{item.idAhcsChannelProcess},
					 #{item.caseType},
					 #{item.lossAmount},
					 #{item.remark},
					 #{item.taskId},
					 #{item.status},
					 <if test="item.archiveTime != null ">
				 		#{item.archiveTime}
					</if>
					<if test="item.archiveTime == null ">
						 SYSDATE()
					 </if>	
				  FROM DUAL
			</foreach>
	</insert>
	
	<select id="getPersonOtherLoss" resultType="com.paic.ncbs.claim.model.dto.duty.PersonOtherLossDTO">
		SELECT REPORT_NO reportNo,
		       CASE_TIMES caseTimes,
		       ID_AHCS_CHANNEL_PROCESS idAhcsChannelProcess,
		       CASE_TYPE caseType,
		       (select t.value_chinese_name
		          FROM CLM_COMMON_PARAMETER T
		         where t.collection_code = 'AHCS_OL_CASE_TYPE'
		           and t.value_code = CASE_TYPE) caseTypeName,
		       LOSS_AMOUNT lossAmount,
		       REMARK remark,
		       TASK_ID taskId,
		       STATUS status
		  FROM CLMS_PERSON_OTHER_LOSS
		  WHERE REPORT_NO = #{reportNo}
		  	AND CASE_TIMES = #{caseTimes}
	  	<if test="status != null and status != '' ">
	    	AND STATUS = #{status}
	    </if>
	    <if test="taskId != null and taskId != '' ">
	    	AND TASK_ID = #{taskId}
	    </if>
		<if test="channelProcessId != null and channelProcessId != '' ">
			AND ID_AHCS_CHANNEL_PROCESS = #{channelProcessId}
		</if>
	</select>
	
	<delete id="removePersonOtherLoss">
		DELETE FROM CLMS_PERSON_OTHER_LOSS T
		 WHERE T.REPORT_NO = #{reportNo}
		   AND T.CASE_TIMES = #{caseTimes}
	    <if test="taskId != null and taskId != '' ">
	    	AND T.TASK_ID = #{taskId}
	    </if>
		<if test="idAhcsChannelProcess != null and idAhcsChannelProcess != '' ">
			AND T.ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
		</if>
	</delete>
	
	<!-- 根据通道号、环节号获取其他人伤信息 -->
	<select id="getPersonOtherLossList" resultMap="personOtherLossListResult">
		SELECT REPORT_NO,
		       CASE_TIMES,
		       ID_AHCS_CHANNEL_PROCESS,
		       CASE_TYPE,
		       LOSS_AMOUNT,
		       REMARK,
		       TASK_ID,
		       STATUS,
			   ARCHIVE_TIME 
		  FROM CLMS_PERSON_OTHER_LOSS pol
		 WHERE pol.ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess} 
	    	   AND pol.STATUS = '1' 
	    	   AND pol.TASK_ID = #{taskId} 
	</select>
	
	<!-- 新增多条 其他人伤信息 -->
	<insert id="addPersonOtherLossList">		
		INSERT INTO CLMS_PERSON_OTHER_LOSS(
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			REPORT_NO,
			CASE_TIMES,
			ID_AHCS_CHANNEL_PROCESS,
			CASE_TYPE,
			LOSS_AMOUNT,
			REMARK,
			TASK_ID,
			STATUS,
			archive_time
			)
		<foreach collection="personOtherLossList" index="index" item="item" open="(" close=")" separator="union all">	
		  SELECT #{userId},
				 SYSDATE(),
				 #{userId},
				 SYSDATE(),
				 #{item.reportNo},
				 #{caseTimes},
				 #{channelProcessId},
				 #{item.caseType},
				 #{item.lossAmount},
				 #{item.remark},
				 #{item.taskId},
				 #{item.status},
				 <if test="item.archiveTime != null ">
				 #{item.archiveTime}
				</if>
				<if test="item.archiveTime == null ">
					 SYSDATE()
				 </if>	
			FROM DUAL
		</foreach>
    </insert>
	
	<update id="updatePersonOtherLoss" parameterType="com.paic.ncbs.claim.model.dto.duty.PersonOtherLossDTO">
		update CLMS_PERSON_OTHER_LOSS
		<trim prefix="set" suffixOverrides=",">
		  <if test="updatedBy != null">UPDATED_BY=#{updatedBy},</if>
		  UPDATED_DATE=SYSDATE(),
		  <if test="caseType != null">CASE_TYPE=#{caseType},</if>
		  <if test="lossAmount != null">LOSS_AMOUNT=#{lossAmount},</if>
		 </trim>
	   WHERE REPORT_NO = #{reportNo}
  		AND CASE_TIMES = #{caseTimes}
  		AND TASK_ID = #{taskId}
  	</update>

	<insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
		INSERT INTO CLMS_PERSON_OTHER_LOSS (
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_PERSON_OTHER_LOSS,
			REPORT_NO,
			CASE_TIMES,
			ID_AHCS_CHANNEL_PROCESS,
			CASE_TYPE,
			LOSS_AMOUNT,
			REMARK,
			TASK_ID,
			STATUS,
			ARCHIVE_TIME,
			ID_AHCS_ADDITIONAL_SURVEY
		)
		SELECT
			#{userId},
			NOW(),
			#{userId},
			NOW(),
			MD5(UUID()),
			REPORT_NO,
			#{reopenCaseTimes},
			#{idClmChannelProcess},
			CASE_TYPE,
			LOSS_AMOUNT,
			REMARK,
			TASK_ID,
			STATUS,
			NOW(),
			ID_AHCS_ADDITIONAL_SURVEY
		FROM CLMS_PERSON_OTHER_LOSS
		WHERE REPORT_NO=#{reportNo}
		AND CASE_TIMES=#{caseTimes}
	</insert>
</mapper>