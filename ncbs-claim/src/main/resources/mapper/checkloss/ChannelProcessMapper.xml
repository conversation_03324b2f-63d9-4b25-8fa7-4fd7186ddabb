<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.ChannelProcessMapper">
	<resultMap type="com.paic.ncbs.claim.model.dto.checkloss.ChannelProcessDTO" id="result1">
		<result column="ID_AHCS_CHANNEL_PROCESS" property="idAhcsChannelProcess"/>
	</resultMap>
	
	<resultMap type="com.paic.ncbs.claim.model.dto.checkloss.ChannelProcessDTO" id="result2">
		<id column="ID_AHCS_CHANNEL_PROCESS" property="idAhcsChannelProcess"/>
		<result column="REPORT_NO" property="reportNo"/>
		<result column="CASE_TIMES" property="caseTimes"/>
		<result column="CHANNEL_TYPE" property="channelType"/>
		<result column="ASSESS_UM" property="assessUm"/>
		<result column="ASSESS_DATE" property="assessDate"/>
		<result column="LOSS_OBJECT_NO" property="lossObjectNo"/>
		<result column="ARCHIVE_TIME"  property="archiveTime"/>
		<result column="ID_AHCS_ADDITIONAL_SURVEY"  property="idAhcsAdditionalSurvey"/>
	</resultMap>
	
	<insert id="saveChannelProcess" parameterType="com.paic.ncbs.claim.model.dto.checkloss.ChannelProcessDTO">
		insert into CLMS_CHANNEL_PROCESS
		  (CREATED_BY,
		   CREATED_DATE,
		   UPDATED_BY,
		   UPDATED_DATE,
		   ID_AHCS_CHANNEL_PROCESS,
		   REPORT_NO,
		   CASE_TIMES,
		   CHANNEL_TYPE,
		   ASSESS_UM,
		   ASSESS_DATE,
		   LOSS_OBJECT_NO,
	  	   id_ahcs_additional_survey,
		   archive_time)
		values
		  (#{createdBy},
		   now(),
		   #{updatedBy},
		   now(),
		   #{idAhcsChannelProcess},
		   #{reportNo},
		   #{caseTimes},
		   #{channelType},
		   #{assessUm},
		   now(),
		   #{lossObjectNo},
		   #{idAhcsAdditionalSurvey,jdbcType=VARCHAR},
		   <if test="archiveTime != null ">
			 #{archiveTime}
		  </if>
		  <if test="archiveTime == null ">
			 now()
		  </if>
		  )
	</insert>
	 
	 <select id="getChannelProcessId" resultType="string">
		select  
				t1.ID_AHCS_CHANNEL_PROCESS
		from CLMS_CHANNEL_PROCESS t1
	   where t1.CHANNEL_TYPE =#{channelType}
		     and t1.REPORT_NO = #{reportNo}
		     and t1.CASE_TIMES = #{caseTimes}

	</select>
	
	<select id="getChannelProcessIds" resultMap="result2"  flushCache="true">
		select t1.ID_AHCS_CHANNEL_PROCESS,
		       t1.REPORT_NO,
		       t1.CASE_TIMES,
		       t1.CHANNEL_TYPE,
		       t1.ASSESS_UM,
		       t1.ASSESS_DATE,
		       t1.LOSS_OBJECT_NO,
			   t1.ID_AHCS_ADDITIONAL_SURVEY
		  from CLMS_CHANNEL_PROCESS t1
		 where t1.REPORT_NO = #{reportNo}
		   and t1.CASE_TIMES = #{caseTimes}
	</select>

	<select id="getChannelProcessIdsBySurveyId" resultMap="result2"  flushCache="true">
		select t.*
		  from CLMS_CHANNEL_PROCESS t
		 where t.report_no = #{reportNo,jdbcType=VARCHAR}
		   and t.case_times = #{caseTimes,jdbcType=NUMERIC}
		<if test="idAhcsAdditionalSurvey != null and idAhcsAdditionalSurvey != '' ">
			AND ID_AHCS_ADDITIONAL_SURVEY = #{idAhcsAdditionalSurvey,jdbcType=VARCHAR}
		</if>

	</select>
	
	<select id="getChannelProcessIdByreportNo" resultMap="result1">
		select t.ID_AHCS_CHANNEL_PROCESS
		  from CLMS_CHANNEL_PROCESS t
		 where t.report_no = #{reportNo}
		   and t.case_times = #{caseTimes}
	</select>
	
	

	<select id="getChannelProcess" resultMap="result2">
		select cp.ID_AHCS_CHANNEL_PROCESS,
		       cp.REPORT_NO,
		       cp.CHANNEL_TYPE,
		       cp.ASSESS_UM,
		       cp.ASSESS_DATE,
		       cp.LOSS_OBJECT_NO,
		       cp.ARCHIVE_TIME  
		  from CLMS_CHANNEL_PROCESS cp
		 where cp.REPORT_NO = #{reportNo}
		   and cp.CASE_TIMES = #{caseTimes}
	</select>
	

	<insert id="addChannelProcess" parameterType="com.paic.ncbs.claim.model.dto.checkloss.ChannelProcessDTO">
		insert into CLMS_CHANNEL_PROCESS
		  (CREATED_BY,
		   CREATED_DATE,
		   UPDATED_BY,
		   UPDATED_DATE,
		   ID_AHCS_CHANNEL_PROCESS,
		   REPORT_NO,
		   CASE_TIMES,
		   CHANNEL_TYPE,
		   ASSESS_UM,
		   ASSESS_DATE,
		   LOSS_OBJECT_NO,
		   ARCHIVE_TIME)
		values
		  (#{createdBy},
		   now(),
		   #{updatedBy},
		   now(),
		   #{idAhcsChannelProcess},
		   #{reportNo},
		   #{caseTimes},
		   #{channelType},
		   #{assessUm,jdbcType=VARCHAR},
		   #{assessDate,jdbcType=VARCHAR},
		   #{lossObjectNo,jdbcType=VARCHAR},
		   <if test="archiveTime != null ">
			 #{archiveTime,jdbcType=TIMESTAMP}
		  </if>
		  <if test="archiveTime == null ">
			now()
		  </if>
		   )
	</insert>

	<insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
		INSERT INTO CLMS_CHANNEL_PROCESS (
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_CHANNEL_PROCESS,
			REPORT_NO,
			CASE_TIMES,
			CHANNEL_TYPE,
			ASSESS_UM,
			ASSESS_DATE,
			LOSS_OBJECT_NO,
			ARCHIVE_TIME,
			ID_AHCS_ADDITIONAL_SURVEY
		)
		SELECT
			#{userId},
			NOW(),
			#{userId},
			NOW(),
			#{idClmChannelProcess},
			REPORT_NO,
			#{reopenCaseTimes},
			CHANNEL_TYPE,
			ASSESS_UM,
			ASSESS_DATE,
			LOSS_OBJECT_NO,
			NOW(),
			ID_AHCS_ADDITIONAL_SURVEY
		FROM CLMS_CHANNEL_PROCESS
		WHERE REPORT_NO=#{reportNo}
		AND CASE_TIMES=#{caseTimes}
		AND CHANNEL_TYPE=#{channelType}
	</insert>
</mapper>