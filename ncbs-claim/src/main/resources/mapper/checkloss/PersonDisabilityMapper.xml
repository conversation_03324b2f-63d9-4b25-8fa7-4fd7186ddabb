<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.PersonDisabilityMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.duty.PersonDisabilityDTO" id="result">
        <id column="ID_AHCS_PERSON_DISABILITY" property="personDisabilityId"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="ID_AHCS_CHANNEL_PROCESS" property="idAhcsChannelProcess"/>
        <result column="DISABILITY_NO" property="disabilityNo"/>
        <result column="DISABILITY_CODE" property="disabilityCode"/>
        <result column="DISABILITY_NAME" property="disabilityName"/>
        <result column="DISABILITY_CLAUSE" property="disabilityClause"/>
        <result column="DISABILITY_GRADE" property="disabilityGrade"/>
        <result column="ASSESSMENT_DATE" property="assessmentDate"/>
        <result column="APPRAISAL_SITUATION" property="appraisalSituation"/>
        <result column="APPRAISAL_DEPARTMENT" property="appraisalDepartment"/>
        <result column="APPRAISER_NAME" property="appraiserName"/>
        <result column="APPRAISER_NAME_SECOND" property="appraiserNameSecond"/>
        <result column="IS_COOPERATE" property="isCooperate"/>
        <result column="TASK_ID" property="taskId"/>
        <result column="STATUS" property="status"/>
        <result column="ARCHIVE_TIME" property="archiveTime"/>
        <result column="IS_APPRAISAL" property="isAppraisal"/>
        <result column="PROVICE" property="provice"/>
        <result column="CITY" property="city"/>
        <result column="PAY_RATE" property="payRate"/>
        <result column="ISNO_PLACEAPPRAISAL" property="isnoPlaceAppraisal"/>
        <result column="DISABILITIES" property="disAbilities"/>
        <result column="AGENCY_ID_TYPE" property="agencyIdType"/>
        <result column="AGENCY_ID_NO" property="agencyIdNo"/>
        <result column="AGENCY_CERTIFY_NO" property="agencyCertifyNo"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.vo.checkloss.DisabilityVO" id="result1">
        <result column="disabilityName" property="disabilityName"/>
        <result column="disabilityClause" property="disabilityClause"/>
        <result column="disabilityGrade" property="disabilityGrade"/>
    </resultMap>

    <!-- 新增多条 伤残信息 -->
    <insert id="addPersonDisabilityList">
        insert into CLMS_person_disability
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        DISABILITY_NO,
        DISABILITY_CODE,
        DISABILITY_CLAUSE,
        DISABILITY_GRADE,
        ASSESSMENT_DATE,
        APPRAISAL_SITUATION,
        APPRAISAL_DEPARTMENT,
        APPRAISER_NAME,
        APPRAISER_NAME_SECOND,
        IS_COOPERATE,
        TASK_ID,
        STATUS,
        ARCHIVE_TIME,
        IS_APPRAISAL,
        PROVICE,
        CITY,
        PAY_RATE,
        ISNO_PLACEAPPRAISAL,
        DISABILITIES,
        AGENCY_ID_TYPE,
        AGENCY_ID_NO,
        AGENCY_CERTIFY_NO
        )
        <foreach collection="personDisabilityList" index="index" item="item" open="(" close=")" separator="union all">
            select #{userId},
            SYSDATE(),
            #{userId},
            SYSDATE(),
            #{item.reportNo},
            #{caseTimes},
            #{channelProcessId},
            #{item.disabilityNo},
            #{item.disabilityCode},
            #{item.disabilityClause},
            #{item.disabilityGrade},
            #{item.assessmentDate},
            #{item.appraisalSituation},
            #{item.appraisalDepartment},
            #{item.appraiserName},
            #{item.appraiserNameSecond},
            #{item.isCooperate},
            #{item.taskId} ,
            #{item.status} ,
            #{item.isAppraisal} ,
            #{item.provice} ,
            #{item.city} ,
            #{item.payRate} ,
            #{item.isnoPlaceAppraisal} ,
            #{item.disAbilities} ,
            #{item.agencyIdType} ,
            #{item.agencyIdNo} ,
            #{item.agencyCertifyNo} ,
            <if test="item.archiveTime != null ">
                #{item.archiveTime}
            </if>
            <if test="item.archiveTime == null ">
                SYSDATE()
            </if>
            from DUAL
        </foreach>
    </insert>

    <insert id="savePersonDisability" parameterType="com.paic.ncbs.claim.model.dto.duty.PersonDisabilityDTO">
        insert into CLMS_person_disability
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        DISABILITY_NO,
        DISABILITY_CODE,
        DISABILITY_CLAUSE,
        DISABILITY_GRADE,
        ASSESSMENT_DATE,
        APPRAISAL_SITUATION,
        APPRAISAL_DEPARTMENT,
        APPRAISER_NAME,
        APPRAISER_NAME_SECOND,
        IS_COOPERATE,
        TASK_ID,
        STATUS,
        archive_time,
        ID_AHCS_PERSON_DISABILITY,
		IS_EFFECTIVE,
        is_appraisal,
        provice,
        city,
        pay_rate,
        isno_placeappraisal,
        disabilities,
        agency_id_type,
        agency_id_no,
        agency_certify_no
        )
        values
        (#{createdBy},
        SYSDATE(),
        #{createdBy},
        SYSDATE(),
        #{reportNo},
        #{caseTimes},
        #{idAhcsChannelProcess},
        #{disabilityNo},
        #{disabilityCode},
        #{disabilityClause},
        #{disabilityGrade},
        #{assessmentDate},
        #{appraisalSituation},
        #{appraisalDepartment},
        #{appraiserName},
        #{appraiserNameSecond},
        #{isCooperate},
        #{taskId},
        #{status},
        <if test="archiveTime != null ">
            #{archiveTime},
        </if>
        <if test="archiveTime == null ">
            SYSDATE(),
        </if>
		left(hex(uuid()),32),
		'Y',
        #{isAppraisal} ,
        #{provice} ,
        #{city} ,
        #{payRate} ,
        #{isnoPlaceAppraisal} ,
        #{disAbilities} ,
        #{agencyIdType} ,
        #{agencyIdNo} ,
        #{agencyCertifyNo}
        )
    </insert>

    <delete id="removePersonDisability">
        delete from CLMS_person_disability where ID_AHCS_CHANNEL_PROCESS=#{idAhcsChannelProcess}
        <if test="taskId != null and taskId != '' ">
            and TASK_ID = #{taskId}
        </if>
    </delete>

    <update id="updateEffective" parameterType="com.paic.ncbs.claim.model.dto.duty.PersonDisabilityDTO">
        UPDATE
        CLMS_PERSON_DISABILITY
        SET
        UPDATED_BY = #{updatedBy},
        UPDATED_DATE = SYSDATE(),
        IS_EFFECTIVE = 'N'
        WHERE
        ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        <if test="taskId != null and taskId != '' ">
            and TASK_ID = #{taskId}
        </if>
        AND IS_EFFECTIVE = 'Y'
    </update>

    <!-- 根据通道号、环节号获取最新环节的伤残信息 -->
    <select id="getPersonDisabilityList" parameterType="string" resultMap="result">
        select t.ID_AHCS_PERSON_DISABILITY,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.ID_AHCS_CHANNEL_PROCESS,
        t.DISABILITY_NO,
        t.DISABILITY_CODE,
        t.DISABILITY_CLAUSE,
        t.DISABILITY_GRADE,
        t.ASSESSMENT_DATE,
        t.APPRAISAL_SITUATION,
        t.APPRAISAL_DEPARTMENT,
        t.APPRAISER_NAME,
        t.APPRAISER_NAME_SECOND,
        t.IS_COOPERATE,
        t.TASK_ID,
        t.STATUS,
        t.is_appraisal,
        t.provice,
        t.city,
        t.pay_rate,
        t.isno_placeappraisal,
        t.disabilities,
        t.agency_id_type,
        t.agency_id_no,
        t.agency_certify_no
        from CLMS_person_disability t
        where t.id_ahcs_channel_process = #{idAhcsChannelProcess}
        AND t.IS_EFFECTIVE = 'Y'
        <if test="status != null and status != '' ">
            and t.STATUS = #{status}
        </if>
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        and t.task_id =
        (select * from
        (select t1.TASK_ID from CLMS_person_disability t1 where
        t1.ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        AND t1.IS_EFFECTIVE = 'Y'
        <if test="status != null and status != '' ">
            and t1.STATUS = #{status}
        </if>
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        order by t1.CREATED_DATE desc)
        as temp limit 1
        )
    </select>

    <select id="getList" parameterType="com.paic.ncbs.claim.model.dto.duty.PersonDisabilityDTO" resultMap="result">
        select t.ID_AHCS_PERSON_DISABILITY,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.ID_AHCS_CHANNEL_PROCESS,
        t.DISABILITY_NO,
        t.DISABILITY_CODE,
        t.DISABILITY_CLAUSE,
        t.DISABILITY_GRADE,
        t.ASSESSMENT_DATE,
        t.APPRAISAL_SITUATION,
        t.APPRAISAL_DEPARTMENT,
        t.APPRAISER_NAME,
        t.APPRAISER_NAME_SECOND,
        t.IS_COOPERATE,
        t.is_appraisal,
        t.provice,
        t.city,
        t.pay_rate,
        t.isno_placeappraisal,
        t.disabilities,
        t.agency_id_type,
        t.agency_id_no,
        t.agency_certify_no
        from CLMS_person_disability t
        where t.report_no = #{personDisability.reportNo}
        and t.case_times = #{personDisability.caseTimes}
        AND t.IS_EFFECTIVE = 'Y'
        <if test="personDisability.taskId != null and personDisability.taskId != '' ">
            and t.TASK_ID = #{personDisability.taskId}
        </if>
        and t.task_id =
        (select a.* from
        (select t1.task_id from CLMS_person_disability t1 where
        t1.report_no = #{personDisability.reportNo}
        and t1.case_times = #{personDisability.caseTimes}
        AND t1.IS_EFFECTIVE = 'Y'
        <if test="personDisability.taskId != null and personDisability.taskId != '' ">
            and t1.TASK_ID = #{personDisability.taskId}
        </if>
        order by t1.created_date desc) a
        limit 1
        )
        order by t.DISABILITY_GRADE
    </select>

    <select id="getDisabilityClassList" parameterType="com.paic.ncbs.claim.model.dto.duty.PersonDisabilityDTO"
            resultType="String">
        select d.disability_class
        from CLMS_person_disability t,
        CLMS_DISABILITY_STANDARD d
        where t.disability_code = d.disability_code
        and t.report_no = #{personDisability.reportNo}
        and t.case_times = #{personDisability.caseTimes}
        AND t.IS_EFFECTIVE = 'Y'
        <if test="personDisability.taskId != null and personDisability.taskId != '' ">
            and t.TASK_ID = #{personDisability.taskId}
        </if>
        and t.task_id =
        (select * from
        (select t1.task_id from CLMS_person_disability t1 where
        t1.report_no = #{personDisability.reportNo}
        and t1.case_times = #{personDisability.caseTimes}
        AND t1.IS_EFFECTIVE = 'Y'
        <if test="personDisability.taskId != null and personDisability.taskId != '' ">
            and t1.TASK_ID = #{personDisability.taskId}
        </if>
        order by t1.created_date desc)
        as temp limit 1
        )
    </select>

    <select id="getDisabilityList" parameterType="string" resultMap="result1">
        select (select t2.disability_class
        from CLMS_disability_standard t2
        where t1.disability_code = t2.disability_code) disabilityName,
        t1.disability_clause  disabilityClause,
        t1.disability_grade disabilityGrade
        from CLMS_person_disability t1
        where t1.id_ahcs_channel_process = #{idAhcsChannelProcess}
        AND t1.IS_EFFECTIVE = 'Y'
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        <if test="status != null and status != '' ">
            and t1.STATUS = #{status}
        </if>
        and t1.task_id =
        (select * from
        (select t.task_id from CLMS_person_disability t where
        t.id_ahcs_channel_process = #{idAhcsChannelProcess}
        AND t.IS_EFFECTIVE = 'Y'
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        <if test="status != null and status != '' ">
            and t.STATUS = #{status}
        </if>
        order by t.created_date desc)
        as temp limit 1
        )
    </select>

    <!-- 根据通道号、环节号获取伤残信息 -->
    <select id="getPersonDisabilityDTOList" parameterType="string" resultMap="result">
        select t.ID_AHCS_PERSON_DISABILITY,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.ID_AHCS_CHANNEL_PROCESS,
        t.DISABILITY_NO,
        t.DISABILITY_CODE,
        t.DISABILITY_CLAUSE,
        t.DISABILITY_GRADE,
        t.ASSESSMENT_DATE,
        t.APPRAISAL_SITUATION,
        t.APPRAISAL_DEPARTMENT,
        t.APPRAISER_NAME,
        t.APPRAISER_NAME_SECOND,
        t.IS_COOPERATE,
        t.TASK_ID,
        t.STATUS,
        t.ARCHIVE_TIME,
        t.is_appraisal,
        t.provice,
        t.city,
        t.pay_rate,
        t.isno_placeappraisal,
        t.disabilities,
        t.agency_id_type,
        t.agency_id_no,
        t.agency_certify_no
        from CLMS_person_disability t
        where t.ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        and t.STATUS = '1'
        and t.TASK_ID = #{taskId}
        AND t.IS_EFFECTIVE = 'Y'
    </select>

    <!-- 获取伤残时间 -->
    <select id="getDisabilityDateByReportNo" resultType="string">
        SELECT date_format(APD.ASSESSMENT_DATE,'%Y%m%d') ASSESSMENT_DATE
        FROM CLMS_PERSON_DISABILITY APD
        WHERE APD.REPORT_NO = #{reportNo}
        AND APD.CASE_TIMES = #{caseTimes}
        AND APD.ASSESSMENT_DATE is not null
        AND APD.IS_EFFECTIVE = 'Y'
        AND APD.TASK_ID =
        (select * from
        (select t1.TASK_ID from CLMS_PERSON_DISABILITY t1
        where t1.REPORT_NO = #{reportNo}
        and t1.CASE_TIMES = #{caseTimes}
        and t1.ASSESSMENT_DATE is not null
        AND t1.IS_EFFECTIVE = 'Y'
        order by t1.CREATED_DATE desc)
        as temp limit 1
        )
        limit 1
    </select>

    <!-- 根据通道号、环节号获取最新环节的伤残信息 -->
    <select id="getDisabilityListByReportNo" parameterType="string" resultMap="result">
        select t.ID_AHCS_PERSON_DISABILITY,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.ID_AHCS_CHANNEL_PROCESS,
        t.DISABILITY_NO,
        t.DISABILITY_CODE,
        t.DISABILITY_CLAUSE,
        t.DISABILITY_GRADE,
        t.ASSESSMENT_DATE,
        t.APPRAISAL_SITUATION,
        t.APPRAISAL_DEPARTMENT,
        t.APPRAISER_NAME,
        t.APPRAISER_NAME_SECOND,
        t.IS_COOPERATE,
        t.TASK_ID,
        t.STATUS,
        t.is_appraisal,
        t.provice,
        t.city,
        t.pay_rate,
        t.isno_placeappraisal,
        t.disabilities,
        t.agency_id_type,
        t.agency_id_no,
        t.agency_certify_no
        from CLMS_person_disability t
        where t.REPORT_NO = #{reportNo} AND t.CASE_TIMES = #{caseTimes}
        AND t.IS_EFFECTIVE = 'Y'
        <if test="status != null and status != '' ">
            and t.STATUS = #{status}
        </if>
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
    </select>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        INSERT INTO CLMS_PERSON_DISABILITY (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_AHCS_PERSON_DISABILITY,
            REPORT_NO,
            CASE_TIMES,
            ID_AHCS_CHANNEL_PROCESS,
            DISABILITY_NO,
            DISABILITY_CODE,
            DISABILITY_CLAUSE,
            DISABILITY_GRADE,
            ASSESSMENT_DATE,
            APPRAISAL_SITUATION,
            APPRAISAL_DEPARTMENT,
            APPRAISER_NAME,
            APPRAISER_NAME_SECOND,
            IS_COOPERATE,
            TASK_ID,
            STATUS,
            ARCHIVE_TIME,
            IS_EFFECTIVE,
            ID_AHCS_ADDITIONAL_SURVEY,
            is_appraisal,
            provice,
            city,
            pay_rate,
            isno_placeappraisal,
            disabilities,
            agency_id_type,
            agency_id_no,
            agency_certify_no
        )
        SELECT
            #{userId},
            NOW(),
            #{userId},
            NOW(),
            LEFT(HEX(UUID()), 32),
            REPORT_NO,
            #{reopenCaseTimes},
            #{idClmChannelProcess},
            DISABILITY_NO,
            DISABILITY_CODE,
            DISABILITY_CLAUSE,
            DISABILITY_GRADE,
            ASSESSMENT_DATE,
            APPRAISAL_SITUATION,
            APPRAISAL_DEPARTMENT,
            APPRAISER_NAME,
            APPRAISER_NAME_SECOND,
            IS_COOPERATE,
            TASK_ID,
            STATUS,
            NOW(),
            IS_EFFECTIVE,
            ID_AHCS_ADDITIONAL_SURVEY,
            is_appraisal,
            provice,
            city,
            pay_rate,
            isno_placeappraisal,
            disabilities,
            agency_id_type,
            agency_id_no,
            agency_certify_no
        FROM CLMS_PERSON_DISABILITY
        WHERE REPORT_NO=#{reportNo}
        AND CASE_TIMES=#{caseTimes}
        AND IS_EFFECTIVE='Y'
    </insert>
    <!--根据报案号，赔付次数，taskId查询数据-->
    <select id="selectPersonDisabilityDTO" parameterType="com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo" resultMap="result">
        select t.ID_AHCS_PERSON_DISABILITY,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.ID_AHCS_CHANNEL_PROCESS,
        t.DISABILITY_NO,
        t.DISABILITY_CODE,
        t.DISABILITY_CLAUSE,
        t.DISABILITY_GRADE,
        t.ASSESSMENT_DATE,
        t.APPRAISAL_SITUATION,
        t.APPRAISAL_DEPARTMENT,
        t.APPRAISER_NAME,
        t.APPRAISER_NAME_SECOND,
        t.IS_COOPERATE,
        t.TASK_ID,
        t.STATUS,
        t.ARCHIVE_TIME,
        t.is_appraisal,
        t.provice,
        t.city,
        t.pay_rate,
        t.isno_placeappraisal,
        t.disabilities,
        t.agency_id_type,
        t.agency_id_no,
        t.agency_certify_no
        from CLMS_person_disability t
        where 1=1
        <if test="taskId != null and taskId!=''" >
            and t.TASK_ID = #{taskId}
        </if>
        <if test="reportNo != null and reportNo!=''" >
            and t.report_no=#{reportNo}
        </if>
        <if test="caseTimes != null" >
            and t.case_times=#{caseTimes}
        </if>
           and t.IS_EFFECTIVE='Y'
    </select>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.model.dto.duty.PersonDisabilityDTO">
        update CLMS_person_disability
        <set>
            <if test="reportNo != null and reportNo!=''">
                report_no = #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="caseTimes != null">
                case_times = #{caseTimes},
            </if>
            <if test="idAhcsChannelProcess != null and idAhcsChannelProcess!=''">
                ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess,jdbcType=VARCHAR},
            </if>
            <if test="disabilityNo != null">
                DISABILITY_NO = #{disabilityNo},
            </if>
            <if test="disabilityCode != null and disabilityCode!=''">
                DISABILITY_CODE = #{disabilityCode,jdbcType=VARCHAR},
            </if>
            <if test="disabilityName != null and disabilityName!=''">
                DISABILITY_NAME = #{disabilityName,jdbcType=VARCHAR},
            </if>
            <if test="disabilityClause != null and disabilityClause!=''">
                DISABILITY_CLAUSE = #{disabilityClause,jdbcType=VARCHAR},
            </if>
            <if test="disabilityGrade != null and disabilityGrade!=''">
                DISABILITY_GRADE = #{disabilityGrade,jdbcType=VARCHAR},
            </if>
            <if test="assessmentDate != null">
                ASSESSMENT_DATE = #{assessmentDate},
            </if>
            <if test="appraisalSituation != null and appraisalSituation!=''">
                APPRAISAL_SITUATION = #{appraisalSituation,jdbcType=VARCHAR},
            </if>
            <if test="appraisalDepartment != null and appraisalDepartment!=''">
                APPRAISAL_DEPARTMENT = #{appraisalDepartment,jdbcType=VARCHAR},
            </if>
            <if test="appraiserName != null and appraiserName!=''">
                APPRAISER_NAME = #{appraiserName,jdbcType=VARCHAR},
            </if>
            <if test="appraiserNameSecond != null and appraiserNameSecond!=''">
                APPRAISER_NAME_SECOND = #{appraiserNameSecond,jdbcType=VARCHAR},
            </if>
            <if test="isCooperate != null and isCooperate!=''">
                IS_COOPERATE = #{isCooperate},
            </if>
            <if test="taskId != null and taskId!=''">
                TASK_ID = #{taskId,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status!=''">
                STATUS = #{status,jdbcType=VARCHAR},
            </if>
            <if test="archiveTime != null">
                ARCHIVE_TIME = #{archiveTime},
            </if>
            <if test="isAppraisal != null and isAppraisal!=''">
                IS_APPRAISAL = #{isAppraisal},
            </if>
            <if test="provice != null and provice!=''">
                PROVICE = #{provice},
            </if>
            <if test="city != null and city!=''">
                CITY = #{city},
            </if>
            <if test="payRate != null and payRate!=''">
                PAY_RATE = #{payRate},
            </if>
            <if test="isnoPlaceAppraisal != null and isnoPlaceAppraisal!=''">
                ISNO_PLACEAPPRAISAL = #{isnoPlaceAppraisal},
            </if>
            <if test="disAbilities != null and disAbilities!=''">
                DISABILITIES = #{disAbilities},
            </if>
            <if test="agencyIdType != null and agencyIdType!=''">
                AGENCY_ID_TYPE = #{agencyIdType},
            </if>
            <if test="agencyIdNo != null and agencyIdNo!=''">
                AGENCY_ID_NO = #{agencyIdNo},
            </if>
            <if test="agencyCertifyNo != null and agencyCertifyNo!=''">
                AGENCY_CERTIFY_NO = #{agencyCertifyNo},
            </if>
            <if test="isEffective != null and isEffective!=''">
                IS_EFFECTIVE = #{isEffective},
            </if>
            <if test="createdDate != null">
                created_Date = #{createdDate},
            </if>
            <if test="updatedDate != null">
                updated_Date = #{updatedDate},
            </if>
            <if test="createdBy != null and createdBy!=''">
                created_By = #{createdBy},
            </if>
            <if test="updatedBy != null and updatedBy!=''">
                updated_By = #{updatedBy},
            </if>
        </set>
        where ID_AHCS_PERSON_DISABILITY = #{personDisabilityId,jdbcType=VARCHAR}
    </update>

    <delete id="deletePersonDisability">
        delete from CLMS_person_disability
        where 1=1
        <if test="reportNo != null and reportNo!=''" >
            and report_no=#{reportNo}
        </if>
        <if test="caseTimes != null" >
            and case_times=#{caseTimes}
        </if>
    </delete>
</mapper>