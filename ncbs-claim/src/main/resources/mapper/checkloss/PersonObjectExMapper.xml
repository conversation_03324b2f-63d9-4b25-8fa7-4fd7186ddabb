<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.PersonObjectExMapper">
    <select id="getPersonOtherInfoByIdChannelProcess" parameterType="string"
            resultType="com.paic.ncbs.claim.model.dto.duty.PersonObjectExDTO">
        SELECT
        ID_AHCS_PERSON_OBJEC_EX idAhcsPersonOtherInfo,
        REPORT_NO reportNo,
        ID_AHCS_CHANNEL_PROCESS idAhcsChannelProcess,
        RESIDE_PROVINCE_CODE resideProvinceCode,
        RESIDE_CITY_CODE resideCityCode,
        RESIDE_COUNTY_CODE resideCountyCode,
        RESIDE_ADDRESS resideAddress,
        RESIDE_YEARS resideYears,
        WORK_PROVINCE_CODE workProvinceCode,
        WORK_CITY_CODE workCityCode,
        WORK_COUNTY_CODE workCountyCode,
        WORK_ADDRESS workAddress,
        COMPANY_NAME companyName,
        WORK_YEARS workYears,
        FOLLOW_UP_TREATMENT_DESC followUpTreatmentDesc
        FROM CLMS_PERSON_OBJEC_EX
        WHERE ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        limit 1
    </select>

    <insert id="savePersonOtherInfo" parameterType="com.paic.ncbs.claim.model.dto.duty.PersonObjectExDTO">
        insert into CLMS_PERSON_OBJEC_EX
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        RESIDE_PROVINCE_CODE,
        RESIDE_CITY_CODE,
        RESIDE_COUNTY_CODE,
        RESIDE_ADDRESS,
        RESIDE_YEARS,
        WORK_PROVINCE_CODE,
        WORK_CITY_CODE,
        WORK_COUNTY_CODE,
        WORK_ADDRESS,
        COMPANY_NAME,
        WORK_YEARS,
        FOLLOW_UP_TREATMENT_DESC,
        status,
        archive_time,
        ID_AHCS_PERSON_OBJEC_EX)
        values
        (#{createdBy},
        SYSDATE(),
        #{updatedBy},
        SYSDATE(),
        #{reportNo},
        #{caseTimes},
        #{idAhcsChannelProcess},
        #{resideProvinceCode},
        #{resideCityCode},
        #{resideCountyCode},
        #{resideAddress},
        #{resideYears},
        #{workProvinceCode},
        #{workCityCode},
        #{workCountyCode},
        #{workAddress},
        #{companyName},
        #{workYears},
        #{followUpTreatmentDesc},
        #{status},
        SYSDATE(),
        left(hex(uuid()),32)
        )
    </insert>

    <delete id="removePersonOtherInfo">
        DELETE FROM CLMS_PERSON_OBJEC_EX WHERE ID_AHCS_CHANNEL_PROCESS=#{idAhcsChannelProcess}
    </delete>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        INSERT INTO CLMS_PERSON_OBJEC_EX (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_AHCS_PERSON_OBJEC_EX,
            REPORT_NO,
            CASE_TIMES,
            ID_AHCS_CHANNEL_PROCESS,
            RESIDE_PROVINCE_CODE,
            RESIDE_CITY_CODE,
            RESIDE_COUNTY_CODE,
            RESIDE_ADDRESS,
            RESIDE_YEARS,
            WORK_PROVINCE_CODE,
            WORK_CITY_CODE,
            WORK_COUNTY_CODE,
            WORK_ADDRESS,
            COMPANY_NAME,
            WORK_YEARS,
            FOLLOW_UP_TREATMENT_DESC,
            STATUS,
            ARCHIVE_TIME,
            ID_AHCS_ADDITIONAL_SURVEY
        )
        SELECT
            #{userId},
            NOW(),
            #{userId},
            NOW(),
            LEFT(HEX(UUID()), 32),
            REPORT_NO,
            #{reopenCaseTimes},
            #{idClmChannelProcess},
            RESIDE_PROVINCE_CODE,
            RESIDE_CITY_CODE,
            RESIDE_COUNTY_CODE,
            RESIDE_ADDRESS,
            RESIDE_YEARS,
            WORK_PROVINCE_CODE,
            WORK_CITY_CODE,
            WORK_COUNTY_CODE,
            WORK_ADDRESS,
            COMPANY_NAME,
            WORK_YEARS,
            FOLLOW_UP_TREATMENT_DESC,
            STATUS,
            NOW(),
            ID_AHCS_ADDITIONAL_SURVEY
        FROM CLMS_PERSON_OBJEC_EX
        WHERE REPORT_NO=#{reportNo}
        AND CASE_TIMES=#{caseTimes}
    </insert>
</mapper>