<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.BigDiseaseDefineMapper">
	<resultMap type="com.paic.ncbs.claim.model.dto.checkloss.BigDiseaseDefineDTO" id="result2">
		<id column="ID_AHCS_BIG_DISEASE_DEFINE" property="bigDiseaseDefineId"/>
		<result column="BIG_DISEASE_CODE" property="bigDiseaseCode"/>
		<result column="BIG_DISEASE_NAME" property="bigDiseaseName"/>
		<result column="STANDARD_RULE" property="standardRule"/>
		<result column="EXCETION_DESC" property="excetionDesc"/>
		<collection property="bigDiseaseDetailDefs" ofType="com.paic.ncbs.claim.model.dto.checkloss.BigDiseaseDetailDefDTO" column="ID_AHCS_BIG_DISEASE_DEFINE" select="getBigDeseaseDetailDef">
		</collection>
	</resultMap>
	
	<resultMap type="com.paic.ncbs.claim.model.dto.checkloss.BigDiseaseDetailDefDTO" id="result1">
		<id column="ID_AHCS_BIG_DISEASE_DETAIL_DEF" property="bigDiseaseDetailDefId"/>
		<result column="ID_AHCS_BIG_DISEASE_DEFINE" property="bigDiseaseDefineId"/>
		<result column="BIG_DISEASE_DETAIL_CODE" property="bigDiseaseDetailCode"/>
		<result column="BIG_DISEASE_DETAIL_REMARK" property="bigDiseaseDetailRemark"/>
	</resultMap>

	<select id="getBigDiseaseDefines" resultMap="result2">
		select t.id_ahcs_big_disease_define,
		       t.big_disease_code,
		       t.big_disease_name,
		       t.standard_rule,
		       t.excetion_desc
		  from CLMS_big_disease_define t
	</select>
	
	<select id="getBigDeseaseDetailDef" parameterType="string" resultMap="result1">
		select t.id_ahcs_big_disease_detail_def,
		       t.id_ahcs_big_disease_define,
		       t.big_disease_detail_code,
		       t.big_disease_detail_remark
		  from CLMS_big_disease_detail_def t
		  where t.id_ahcs_big_disease_define = #{bigDiseaseDefineId}
	</select>
</mapper>