<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.DisabilityStandardMapper">
	<resultMap type="com.paic.ncbs.claim.model.vo.checkloss.DisabilityStandardVO" id="result2">
		<result column="DISABILITY_CODE" property="disabilityCode"/>
		<result column="DISABILITY_CLASS" property="disabilityClass"/>
		<collection property="subDisabilityStandards" ofType="com.paic.ncbs.claim.model.vo.checkloss.SubDisabilityStandardVO" column="DISABILITY_CODE" select="getDisabilityStandards">
		</collection>
	</resultMap>
	
	<resultMap type="com.paic.ncbs.claim.model.vo.checkloss.SubDisabilityStandardVO" id="result1">
		<result column="DISABILITY_CODE" property="disabilityCode"/>
		<result column="DISABILITY_GRADE" property="disabilityGrade"/>
		<result column="DISABILITY_CLAUSE" property="disabilityClause"/>
		<result column="DIS_PAY_RATE" property="dispayRate"/>
	</resultMap>
	
	<select id="selectDisabilityStandards" resultMap="result2">
		select DISABILITY_CODE,
		       DISABILITY_CLASS
		  from CLMS_DISABILITY_STANDARD
		 where DISABILITY_GRADE = '-1'
	</select>
	
	<select id="getDisabilityStandards" parameterType="string" resultMap="result1">
		select DISABILITY_CODE,
		       DISABILITY_GRADE,
		       DISABILITY_CLAUSE,
		       DIS_PAY_RATE
		  from CLMS_DISABILITY_STANDARD
		 where DISABILITY_CLASS = #{disabilityCode}
	</select>
	
	<select id="selectDisabilityGrade" parameterType="string" resultType="com.paic.ncbs.claim.model.vo.checkloss.SubDisabilityStandardVO">
		select distinct DISABILITY_GRADE disabilityGrade,
                case DISABILITY_GRADE
                  when '1' then
                   '一级伤残'
                  when '2' then
                   '二级伤残'
                  when '3' then
                   '三级伤残'
                  when '4' then
                   '四级伤残'
                  when '5' then
                   '五级伤残'
                  when '6' then
                   '六级伤残'
                  when '7' then
                   '七级伤残'
                  when '8' then
                   '八级伤残'
                  when '9' then
                   '九级伤残'
                  when '10' then
                   '十级伤残'
                  when '0' then
                   '零级伤残'
                end disabilityGradeName
			  from CLMS_DISABILITY_STANDARD
			 where DISABILITY_CLASS =  #{disabilityClass}
			 order by to_number(DISABILITY_GRADE)
	</select>
	
</mapper>