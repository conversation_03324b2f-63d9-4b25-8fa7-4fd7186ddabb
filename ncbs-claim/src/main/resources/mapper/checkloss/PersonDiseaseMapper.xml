<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.PersonDiseaseMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.duty.PersonDiseaseDTO" id="result">
        <id column="ID_AHCS_PERSON_DISEASE" property="personDiseaseId"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="ID_AHCS_CHANNEL_PROCESS" property="idAhcsChannelProcess"/>
        <result column="IS_WAIT_DISEASE" property="isWaitDisease"/>
        <result column="MEDICAL_HISTORY" property="medicalHistory"/>
        <result column="IS_INFORM_INSURANT" property="isInformInsurant"/>
        <result column="INFORM_REASON" property="informReason"/>
        <result column="TASK_ID" property="taskId"/>
        <result column="STATUS" property="status"/>
        <result column="ARCHIVE_TIME" property="archiveTime"/>
    </resultMap>

    <insert id="savePersonDisease" parameterType="com.paic.ncbs.claim.model.dto.duty.PersonDiseaseDTO">
        insert into CLMS_person_disease
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        IS_WAIT_DISEASE,
        MEDICAL_HISTORY,
        IS_INFORM_INSURANT,
        INFORM_REASON,
        TASK_ID,
        STATUS,
        ARCHIVE_TIME,
        IS_EFFECTIVE,
        ID_AHCS_PERSON_DISEASE)
        values
        (#{createdBy},
        SYSDATE(),
        #{updatedBy},
        SYSDATE(),
        #{reportNo},
        #{caseTimes},
        #{idAhcsChannelProcess},
        #{isWaitDisease},
        #{medicalHistory},
        #{isInformInsurant},
        #{informReason},
        #{taskId},
        #{status},
        <if test="archiveTime != null ">
            #{archiveTime},
        </if>
        <if test="archiveTime == null ">
           SYSDATE(),
        </if>
        'Y',
        left(hex(uuid()),32)
        )
    </insert>

    <delete id="removePersonDisease">
        delete from CLMS_person_disease where ID_AHCS_CHANNEL_PROCESS=#{idAhcsChannelProcess}
        <if test="taskId != null and taskId != '' ">
            and TASK_ID = #{taskId}
        </if>
    </delete>

    <update id="updateEffective" parameterType="com.paic.ncbs.claim.model.dto.duty.PersonDiseaseDTO">
        UPDATE
        CLMS_PERSON_DISEASE
        SET
        UPDATED_BY = #{updatedBy},
        UPDATED_DATE = SYSDATE(),
        IS_EFFECTIVE = 'N'
        WHERE
        ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        <if test="taskId != null and taskId != '' ">
            and TASK_ID = #{taskId}
        </if>
        AND IS_EFFECTIVE = 'Y'
    </update>

    <!-- 根据通道号、环节号获取最新环节的疾病信息 -->
    <select id="getPersonDisease" parameterType="string" resultMap="result">
        select t.CREATED_BY,
        t.CREATED_DATE,
        t.UPDATED_BY,
        t.UPDATED_DATE,
        t.ID_AHCS_PERSON_DISEASE,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.ID_AHCS_CHANNEL_PROCESS,
        t.IS_WAIT_DISEASE,
        t.MEDICAL_HISTORY,
        t.IS_INFORM_INSURANT,
        t.INFORM_REASON,
        t.TASK_ID,
        t.STATUS
        from CLMS_person_disease t
        where t.id_ahcs_channel_process = #{idAhcsChannelProcess}
        AND t.IS_EFFECTIVE = 'Y'
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        <if test="status != null and status != '' ">
            and t.STATUS = #{status}
        </if>
        and t.task_id =
        (select * from
        (select t1.task_id from CLMS_person_disease t1 where
        t1.id_ahcs_channel_process = #{idAhcsChannelProcess}
        AND t1.IS_EFFECTIVE = 'Y'
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        <if test="status != null and status != '' ">
            and t1.STATUS = #{status}
        </if>
        order by t1.created_date desc)
        as temp limit 1
        )
    </select>

    <!-- 根据通道号、环节号获取疾病信息 -->
    <select id="getPersonDiseaseDTO" parameterType="string" resultMap="result">
        select t.CREATED_BY,
        t.CREATED_DATE,
        t.UPDATED_BY,
        t.UPDATED_DATE,
        t.ID_AHCS_PERSON_DISEASE,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.ID_AHCS_CHANNEL_PROCESS,
        t.IS_WAIT_DISEASE,
        t.MEDICAL_HISTORY,
        t.IS_INFORM_INSURANT,
        t.INFORM_REASON,
        t.TASK_ID,
        t.STATUS,
        t.ARCHIVE_TIME
        from CLMS_person_disease t
        where t.id_ahcs_channel_process = #{idAhcsChannelProcess}
        and t.TASK_ID = #{taskId}
        and t.STATUS = '1'
        AND t.IS_EFFECTIVE = 'Y'
    </select>

    <select id="getPersonDiseaseByReportNo" resultMap="result">
        select * from
        (select CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_PERSON_DISEASE,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        IS_WAIT_DISEASE,
        MEDICAL_HISTORY,
        IS_INFORM_INSURANT,
        INFORM_REASON,
        TASK_ID,
        STATUS,
        ARCHIVE_TIME
        FROM CLMS_person_disease
        WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}
        AND IS_EFFECTIVE = 'Y'
        <if test="status != null and status != '' ">
            AND STATUS = #{status}
        </if>
        <if test="taskCode != null and taskCode != '' ">
            AND TASK_ID = #{taskCode}
        </if>
        order by CREATED_DATE desc
        )
        limit 1
    </select>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        INSERT INTO CLMS_PERSON_DISEASE (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_AHCS_PERSON_DISEASE,
            REPORT_NO,
            CASE_TIMES,
            ID_AHCS_CHANNEL_PROCESS,
            IS_WAIT_DISEASE,
            MEDICAL_HISTORY,
            IS_INFORM_INSURANT,
            INFORM_REASON,
            TASK_ID,
            STATUS,
            ARCHIVE_TIME,
            IS_EFFECTIVE,
            ID_AHCS_ADDITIONAL_SURVEY
        )
        SELECT
            #{userId},
            NOW(),
            #{userId},
            NOW(),
            LEFT(HEX(UUID()), 32),
            REPORT_NO,
            #{reopenCaseTimes},
            #{idClmChannelProcess},
            IS_WAIT_DISEASE,
            MEDICAL_HISTORY,
            IS_INFORM_INSURANT,
            INFORM_REASON,
            TASK_ID,
            STATUS,
            NOW(),
            IS_EFFECTIVE,
            ID_AHCS_ADDITIONAL_SURVEY
        FROM CLMS_PERSON_DISEASE
        WHERE REPORT_NO=#{reportNo}
        AND CASE_TIMES=#{caseTimes}
        AND IS_EFFECTIVE='Y'
    </insert>
</mapper>