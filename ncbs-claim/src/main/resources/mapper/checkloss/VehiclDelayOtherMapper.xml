<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.VehiclDelayOtherMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.checkloss.VehiclDelayOtherDTO" id="vehiclDelayOtherListResult">
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="ID_AHCS_CHANNEL_PROCESS" property="idAhcsChannelProcess"/>
        <result column="VEHICL_TYPE" property="vehiclType"/>
        <result column="ORIGINAL_DEPART_TIME" property="originalDepartTime"/>
        <result column="ORIGINAL_ARRIVAL_TIME" property="originalArrivalTime"/>
        <result column="CLASS_NO" property="classNo"/>
        <result column="REAL_DEPART_TIME" property="realDepartTime"/>
        <result column="REAL_ARRIVAL_TIME" property="realArrivalTime"/>
        <result column="DEPART_PLACE" property="departPlace"/>
        <result column="ARRIVAL_PLACE" property="arrivalPlace"/>
        <result column="DELAY_DURATION" property="delayDuration"/>
        <result column="DELAY_DURATION_UNIT" property="delayDurationUnit"/>
        <result column="PORT_INFO" property="portInfo"/>
        <result column="CANCEL_PORT_COUNT" property="cancelPortCount"/>
        <result column="CRUISES_DELAY_DESC" property="cruisesDelayDesc"/>
        <result column="ALERT_DURATION" property="alertDuration"/>
        <result column="ALERT_DURATION_UNIT" property="alertDurationUnit"/>
        <result column="TASK_CODE" property="taskCode"/>
        <result column="STATUS" property="status"/>
        <result column="ARCHIVE_TIME" property="archiveTime"/>
    </resultMap>

    <insert id="addVehiclDelayOther">
        INSERT INTO CLMS_VEHICL_DELAY_OTHER
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        VEHICL_TYPE,
        ORIGINAL_DEPART_TIME,
        ORIGINAL_ARRIVAL_TIME,
        CLASS_NO,
        REAL_DEPART_TIME,
        REAL_ARRIVAL_TIME,
        DEPART_PLACE,
        ARRIVAL_PLACE,
        DELAY_DURATION,
        DELAY_DURATION_UNIT,
        PORT_INFO,
        CANCEL_PORT_COUNT,
        CRUISES_DELAY_DESC,
        ALERT_DURATION,
        ALERT_DURATION_UNIT,
        TASK_CODE,
        STATUS,
        ARCHIVE_TIME,
        IS_EFFECTIVE,
        ID_AHCS_VEHICL_DELAY_OTHER)
        VALUES(
        #{createdBy,jdbcType=VARCHAR},
        NOW(),
        #{updatedBy,jdbcType=VARCHAR},
        NOW(),
        #{reportNo,jdbcType=VARCHAR},
        #{caseTimes,jdbcType=NUMERIC},
        #{idAhcsChannelProcess,jdbcType=VARCHAR},
        #{vehiclType,jdbcType=VARCHAR},
        #{originalDepartTime,jdbcType=TIMESTAMP},
        #{originalArrivalTime,jdbcType=TIMESTAMP},
        #{classNo,jdbcType=VARCHAR},
        #{realDepartTime,jdbcType=TIMESTAMP},
        #{realArrivalTime,jdbcType=TIMESTAMP},
        #{departPlace,jdbcType=VARCHAR},
        #{arrivalPlace,jdbcType=VARCHAR},
        #{delayDuration,jdbcType=VARCHAR},
        #{delayDurationUnit,jdbcType=VARCHAR},
        #{portInfo,jdbcType=VARCHAR},
        #{cancelPortCount,jdbcType=NUMERIC},
        #{cruisesDelayDesc,jdbcType=VARCHAR},
        #{alertDuration,jdbcType=VARCHAR},
        #{alertDurationUnit,jdbcType=VARCHAR},
        #{taskCode,jdbcType=VARCHAR},
        #{status,jdbcType=VARCHAR},
        <if test="archiveTime != null ">
            #{archiveTime,jdbcType=TIMESTAMP},
        </if>
        <if test="archiveTime == null ">
            NOW(),
        </if>
        'Y',
        left(hex(uuid()),32)
        )
    </insert>

    <delete id="removeVehiclDelayOther">
        DELETE CLMS_VEHICL_DELAY_OTHER WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}
        <if test="taskCode != null and taskCode != '' ">
            AND TASK_CODE = #{taskCode}
        </if>
        <if test="channelProcessId != null and channelProcessId != '' ">
            AND ID_AHCS_CHANNEL_PROCESS = #{channelProcessId}
        </if>
    </delete>

    <update id="updateEffective" parameterType="com.paic.ncbs.claim.model.dto.checkloss.VehiclDelayOtherDTO">
        UPDATE
        CLMS_VEHICL_DELAY_OTHER
        SET
        UPDATED_BY = #{updatedBy},
        UPDATED_DATE = NOW(),
        IS_EFFECTIVE = 'N'
        WHERE
        REPORT_NO = #{reportNo}
        AND CASE_TIMES = #{caseTimes}
        <if test="taskCode != null and taskCode != '' ">
            AND TASK_CODE = #{taskCode}
        </if>
        <if test=" idAhcsChannelProcess != null and idAhcsChannelProcess !='' ">
            AND ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        </if>
        AND IS_EFFECTIVE = 'Y'
    </update>

    <select id="getVehiclDelayOther" resultType="com.paic.ncbs.claim.model.dto.checkloss.VehiclDelayOtherDTO">
        SELECT REPORT_NO reportNo,
        CASE_TIMES caseTimes,
        ID_AHCS_CHANNEL_PROCESS idAhcsChannelProcess,
        VEHICL_TYPE vehiclType,
        ORIGINAL_DEPART_TIME originalDepartTime,
        ORIGINAL_ARRIVAL_TIME originalArrivalTime,
        CLASS_NO classNo,
        REAL_DEPART_TIME realDepartTime,
        REAL_ARRIVAL_TIME realArrivalTime,
        DEPART_PLACE departPlace,
        ARRIVAL_PLACE arrivalPlace,
        DELAY_DURATION delayDuration,
        DELAY_DURATION_UNIT delayDurationUnit,
        PORT_INFO portInfo,
        CANCEL_PORT_COUNT cancelPortCount,
        CRUISES_DELAY_DESC cruisesDelayDesc,
        ALERT_DURATION alertDuration,
        ALERT_DURATION_UNIT alertDurationUnit,
        TASK_CODE taskCode,
        STATUS status
        FROM CLMS_VEHICL_DELAY_OTHER
        WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}
        AND IS_EFFECTIVE = 'Y'
        <if test="status != null and status != '' ">
            AND STATUS = #{status}
        </if>
        <if test="taskCode != null and taskCode != '' ">
            AND TASK_CODE = #{taskCode}
        </if>
        <if test="channelProcessId != null and channelProcessId != '' ">
            AND ID_AHCS_CHANNEL_PROCESS = #{channelProcessId}
        </if>
        limit 1
    </select>

    <select id="getSettleVehiclDelayOther" resultType="com.paic.ncbs.claim.model.vo.settle.SettleVehiclDelayOtherVO">
        SELECT REPORT_NO reportNo,
        CASE_TIMES caseTimes,
        ID_AHCS_CHANNEL_PROCESS idAhcsChannelProcess,
        VEHICL_TYPE vehiclType,
        ORIGINAL_DEPART_TIME originalDepartTime,
        ORIGINAL_ARRIVAL_TIME originalArrivalTime,
        CLASS_NO classNo,
        REAL_DEPART_TIME realDepartTime,
        REAL_ARRIVAL_TIME realArrivalTime,
        DEPART_PLACE departPlace,
        ARRIVAL_PLACE arrivalPlace,
        DELAY_DURATION delayDuration,
        DELAY_DURATION_UNIT delayDurationUnit,
        PORT_INFO portInfo,
        CANCEL_PORT_COUNT cancelPortCount,
        CRUISES_DELAY_DESC cruisesDelayDesc,
        ALERT_DURATION alertDuration,
        ALERT_DURATION_UNIT alertDurationUnit,
        TASK_CODE taskCode,
        STATUS status,
        (SELECT CP.VALUE_CHINESE_NAME
        FROM CLM_COMMON_PARAMETER CP
        WHERE CP.COLLECTION_CODE = 'AHCS_DURATION_UNIT'
        AND DELAY_DURATION_UNIT = CP.VALUE_CODE) delayDurationUnitName,
        (SELECT CP.VALUE_CHINESE_NAME
        FROM CLM_COMMON_PARAMETER CP
        WHERE CP.COLLECTION_CODE = 'AHCS_DURATION_UNIT'
        AND ALERT_DURATION_UNIT = CP.VALUE_CODE) alertDurationUnitName,
        (SELECT CP.VALUE_CHINESE_NAME
        FROM CLM_COMMON_PARAMETER CP
        WHERE CP.COLLECTION_CODE = 'AHCS_VEHIC_TYPE'
        AND VEHICL_TYPE = CP.VALUE_CODE) vehiclTypeName,
        (SELECT CP.VALUE_CHINESE_NAME
        FROM CLM_COMMON_PARAMETER CP
        WHERE CP.COLLECTION_CODE = 'AHCS_CRUISES_DELAY'
        AND CRUISES_DELAY_DESC = CP.VALUE_CODE) cruisesDelayDescName
        FROM CLMS_VEHICL_DELAY_OTHER
        WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}
        AND IS_EFFECTIVE = 'Y'
        <if test="status != null and status != '' ">
            AND STATUS = #{status}
        </if>
        <if test="taskCode != null and taskCode != '' ">
            AND TASK_CODE = #{taskCode}
        </if>
        <if test="idAhcsChannelProcess != null and idAhcsChannelProcess != '' ">
            AND ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        </if>
        limit 1
    </select>


    <!-- 根据通道号、环节号获取 其他公交延误信息-->
    <select id="getVehiclDelayOtherList" parameterType="string" resultMap="vehiclDelayOtherListResult">
        select REPORT_NO,
        VEHICL_TYPE,
        ORIGINAL_DEPART_TIME,
        ORIGINAL_ARRIVAL_TIME,
        CLASS_NO,
        REAL_DEPART_TIME,
        REAL_ARRIVAL_TIME,
        DEPART_PLACE,
        ARRIVAL_PLACE,
        DELAY_DURATION,
        DELAY_DURATION_UNIT,
        PORT_INFO,
        CANCEL_PORT_COUNT,
        CRUISES_DELAY_DESC,
        ALERT_DURATION,
        ALERT_DURATION_UNIT,
        TASK_CODE,
        STATUS,
        ARCHIVE_TIME
        from CLMS_VEHICL_DELAY_OTHER vdo
        where vdo.ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        and vdo.STATUS = '1'
        and vdo.TASK_CODE = #{taskCode}
        AND vdo.IS_EFFECTIVE = 'Y'
    </select>

    <!-- 新增多条 其他公交延误信息 -->
    <insert id="addVehiclDelayOtherList">
        insert into CLMS_VEHICL_DELAY_OTHER
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        VEHICL_TYPE,
        ORIGINAL_DEPART_TIME,
        ORIGINAL_ARRIVAL_TIME,
        CLASS_NO,
        REAL_DEPART_TIME,
        REAL_ARRIVAL_TIME,
        DEPART_PLACE,
        ARRIVAL_PLACE,
        DELAY_DURATION,
        DELAY_DURATION_UNIT,
        PORT_INFO,
        CANCEL_PORT_COUNT,
        CRUISES_DELAY_DESC,
        ALERT_DURATION,
        ALERT_DURATION_UNIT,
        TASK_CODE,
        STATUS,
        archive_time)
        <foreach collection="vehiclDelayOtherList" index="index" item="item" open="(" close=")" separator="union all">
            select #{userId},
            NOW(),
            #{userId},
            NOW(),
            #{item.reportNo},
            #{caseTimes},
            #{channelProcessId,jdbcType=VARCHAR},
            #{item.vehiclType,jdbcType=VARCHAR},
            #{item.originalDepartTime,jdbcType=TIMESTAMP},
            #{item.originalArrivalTime,jdbcType=TIMESTAMP},
            #{item.classNo,jdbcType=VARCHAR},
            #{item.realDepartTime,jdbcType=TIMESTAMP},
            #{item.realArrivalTime,jdbcType=TIMESTAMP},
            #{item.departPlace,jdbcType=VARCHAR},
            #{item.arrivalPlace,jdbcType=VARCHAR},
            #{item.delayDuration,jdbcType=VARCHAR},
            #{item.delayDurationUnit,jdbcType=VARCHAR},
            #{item.portInfo,jdbcType=VARCHAR},
            #{item.cancelPortCount,jdbcType=NUMERIC},
            #{item.cruisesDelayDesc,jdbcType=VARCHAR},
            #{item.alertDuration,jdbcType=NUMERIC},
            #{item.alertDurationUnit,jdbcType=VARCHAR},
            #{item.taskCode,jdbcType=VARCHAR},
            #{item.status,jdbcType=VARCHAR},
            <if test="item.archiveTime != null ">
                #{item.archiveTime,jdbcType=TIMESTAMP}
            </if>
            <if test="item.archiveTime == null ">
                NOW()
            </if>
            from DUAL
        </foreach>
    </insert>

    <update id="updateVehiclDelayOther" parameterType="com.paic.ncbs.claim.model.dto.checkloss.VehiclDelayOtherDTO">
        update CLMS_VEHICL_DELAY_OTHER
        <trim prefix="set" suffixOverrides=",">
            <if test="updatedBy != null">UPDATED_BY=#{updatedBy},</if>
            UPDATED_DATE=NOW(),
            <if test="delayDuration != null">DELAY_DURATION=#{delayDuration},</if>
            <if test="delayDurationUnit != null">DELAY_DURATION_UNIT=#{delayDurationUnit},</if>
        </trim>
        WHERE REPORT_NO = #{reportNo}
        AND CASE_TIMES = #{caseTimes}
        AND TASK_CODE = #{taskCode}
        AND IS_EFFECTIVE = 'Y'
    </update>
</mapper>