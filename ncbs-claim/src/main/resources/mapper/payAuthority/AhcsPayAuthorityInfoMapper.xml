<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.payAuthority.AhcsPayAuthorityInfoMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.ahcs.AhcsPayAuthorityInfoDTO" id="AhcsPayAuthorityInfoDTOMap">
        <result column="CREATED_BY" property="createdBy"/>
        <result column="CREATED_DATE" property="createdDate"/>
        <result column="UPDATED_BY" property="updatedBy"/>
        <result column="UPDATED_DATE" property="updatedDate"/>
        <result column="ID_AHCS_PAY_AUTHORITY_INFO" property="idAhcsPayAuthorityInfo"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="ID_CLM_PAYMENT_INFO" property="idClmPaymentInfo"/>
        <result column="CHANNEL_SOURCE" property="channelSource"/>
        <result column="CLIENT_TYPE" property="clientType"/>
        <result column="CLIENT_NAME" property="clientName"/>
        <result column="CLIENT_BANK_ACCOUNT" property="clientBankAccount"/>
        <result column="CLIENT_CERTIFICATE_NO" property="clientCertificateNo"/>
        <result column="ORGANIZE_CODE" property="organizeCode"/>
        <result column="PAYMENT_USAGE" property="paymentUsage"/>
    </resultMap>

    <select id="getAhcsPayAuthorityInfoCount" resultType="java.lang.Integer">
        SELECT
             COUNT(1)
        FROM CLMS_PAY_AUTHORITY_INFO
        WHERE REPORT_NO = #{reportNo}
        AND CASE_TIMES = #{caseTimes}
        AND IS_EFFECTIVE = 'Y'
        <if test="idClmPaymentInfo != null and idClmPaymentInfo !=''">
            AND ID_CLM_PAYMENT_INFO = #{idClmPaymentInfo}
        </if>
    </select>

    <insert id="saveAhcsPayAuthorityInfo" parameterType="com.paic.ncbs.claim.model.dto.ahcs.AhcsPayAuthorityInfoDTO">
        INSERT INTO CLMS_PAY_AUTHORITY_INFO
          (  CREATED_BY,
		     CREATED_DATE,
		     UPDATED_BY,
		     UPDATED_DATE,
		     REPORT_NO,
		     CASE_TIMES,
		     ID_CLM_PAYMENT_INFO,
		     CHANNEL_SOURCE,
		     IS_EFFECTIVE,
		     CLIENT_TYPE,
		     CLIENT_NAME,
		     CLIENT_BANK_ACCOUNT,
		     CLIENT_CERTIFICATE_NO,
		     ORGANIZE_CODE,
		     PAYMENT_USAGE
		   ) VALUES
		   (
		     #{createdBy,jdbcType=VARCHAR},
             sysdate(),
		     #{updatedBy,jdbcType=VARCHAR},
             sysdate(),
		     #{reportNo,jdbcType=VARCHAR},
		     #{caseTimes,jdbcType=NUMERIC},
		     #{idClmPaymentInfo,jdbcType=VARCHAR},
		     #{channelSource,jdbcType=VARCHAR},
		     'Y',
		     #{clientType,jdbcType=VARCHAR},
		     #{clientName,jdbcType=VARCHAR},
		     #{clientBankAccount,jdbcType=VARCHAR},
		     #{clientCertificateNo,jdbcType=VARCHAR},
		     #{organizeCode,jdbcType=VARCHAR},
		     #{paymentUsage,jdbcType=VARCHAR}
		   )
    </insert>
    

</mapper>