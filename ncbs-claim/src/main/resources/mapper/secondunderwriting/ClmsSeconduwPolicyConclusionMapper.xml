<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.secondunderwriting.ClmsSeconduwPolicyConclusionMapper">

    <resultMap type="com.paic.ncbs.claim.dao.entity.clms.ClmsSeconduwPolicyConclusionEntity" id="ClmsSeconduwPolicyConclusionMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="idClmsSecondUnderwriting" column="id_clms_second_underwriting" jdbcType="VARCHAR"/>
        <result property="reportNo" column="report_no" jdbcType="VARCHAR"/>
        <result property="caseTimes" column="case_times" jdbcType="INTEGER"/>
        <result property="policyNo" column="policy_no" jdbcType="VARCHAR"/>
        <result property="applyName" column="apply_name" jdbcType="VARCHAR"/>
        <result property="insuredName" column="insured_name" jdbcType="VARCHAR"/>
        <result property="productName" column="product_name" jdbcType="VARCHAR"/>
        <result property="schemeName" column="scheme_name" jdbcType="VARCHAR"/>
        <result property="insuranceBeginDate" column="insurance_begin_date" jdbcType="TIMESTAMP"/>
        <result property="insuranceEndDate" column="insurance_end_date" jdbcType="TIMESTAMP"/>
        <result property="policyStatus" column="policy_status" jdbcType="VARCHAR"/>
        <result property="uwConclusion" column="uw_conclusion" jdbcType="VARCHAR"/>
        <result property="uwExceptions" column="uw_exceptions" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDate" column="updated_date" jdbcType="TIMESTAMP"/>
    </resultMap>
    <resultMap type="com.paic.ncbs.claim.model.vo.senconduw.ClmsSeconduwPolicyConclusionVO" id="result">
        <result property="reportNo" column="report_no" jdbcType="VARCHAR"/>
        <result property="caseTimes" column="case_times" jdbcType="INTEGER"/>
        <result property="policyNo" column="policy_no" jdbcType="VARCHAR"/>
        <result property="applyName" column="apply_name" jdbcType="VARCHAR"/>
        <result property="insuredName" column="insured_name" jdbcType="VARCHAR"/>
        <result property="productName" column="product_name" jdbcType="VARCHAR"/>
        <result property="schemeName" column="scheme_name" jdbcType="VARCHAR"/>
        <result property="insuranceBeginDate" column="insurance_begin_date" jdbcType="TIMESTAMP"/>
        <result property="insuranceEndDate" column="insurance_end_date" jdbcType="TIMESTAMP"/>
        <result property="policyStatus" column="policy_status" jdbcType="VARCHAR"/>
        <result property="uwConclusion" column="uw_conclusion" jdbcType="VARCHAR"/>
        <result property="uwExceptions" column="uw_exceptions" jdbcType="VARCHAR"/>
        <collection property="riskConclusionList"  ofType="com.paic.ncbs.claim.model.vo.senconduw.RiskConclusionVO">
            <result column="plan_name" property="planName"></result>
            <result column="risk_insurance_begin_date" property="insuranceBeginDate"></result>
            <result column="risk_insurance_end_date" property="insuranceEndDate"></result>
            <result column="plan_status" property="planStatus"></result>
            <result column="uw_decisions" property="uwDecisions"></result>
            <result column="risk_uw_exceptions" property="riskUwExceptions"></result>
        </collection>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ClmsSeconduwPolicyConclusionMap">
        select id,
               id_clms_second_underwriting,
               report_no,
               case_times,
               policy_no,
               apply_name,
               insured_name,
               product_name,
               scheme_name,
               insurance_begin_date,
               insurance_end_date,
               policy_status,
               uw_conclusion,
               uw_exceptions,
               created_by,
               created_date,
               updated_by,
               updated_date
        from clms_seconduw_policy_conclusion
        where id = #{id}
    </select>

    <!--根据报案号查询单个-->
    <select id="queryByReportNo" resultMap="ClmsSeconduwPolicyConclusionMap">
        select id,
               id_clms_second_underwriting,
               report_no,
               case_times,
               policy_no,
               apply_name,
               insured_name,
               product_name,
               scheme_name,
               insurance_begin_date,
               insurance_end_date,
               policy_status,
               uw_conclusion,
               uw_exceptions,
               created_by,
               created_date,
               updated_by,
               updated_date
        from clms_seconduw_policy_conclusion
        where report_no = #{reportNo}
          and case_times = #{caseTimes}
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into clms_seconduw_policy_conclusion(id,id_clms_second_underwriting, report_no, case_times, policy_no,
                                                    apply_name, insured_name, product_name, scheme_name,
                                                    insurance_begin_date, insurance_end_date, policy_status,
                                                    uw_conclusion, uw_exceptions, created_by, created_date, updated_by,
                                                    updated_date)
        values (#{id},#{idClmsSecondUnderwriting}, #{reportNo}, #{caseTimes}, #{policyNo}, #{applyName}, #{insuredName},
                #{productName}, #{schemeName}, #{insuranceBeginDate}, #{insuranceEndDate}, #{policyStatus},
                #{uwConclusion}, #{uwExceptions}, #{createdBy}, #{createdDate}, #{updatedBy}, #{updatedDate})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into clms_seconduw_policy_conclusion(id,id_clms_second_underwriting, report_no, case_times, policy_no,
        apply_name, insured_name, product_name, scheme_name, insurance_begin_date, insurance_end_date, policy_status,
        uw_conclusion, uw_exceptions, created_by, created_date, updated_by, updated_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.idClmsSecondUnderwriting}, #{entity.reportNo}, #{entity.caseTimes}, #{entity.policyNo},
            #{entity.applyName}, #{entity.insuredName}, #{entity.productName}, #{entity.schemeName},
            #{entity.insuranceBeginDate}, #{entity.insuranceEndDate}, #{entity.policyStatus}, #{entity.uwConclusion},
            #{entity.uwExceptions}, #{entity.createdBy}, #{entity.createdDate}, #{entity.updatedBy},
            #{entity.updatedDate})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update clms_seconduw_policy_conclusion
        <set>
            <if test="idClmsSecondUnderwriting != null and idClmsSecondUnderwriting != ''">
                id_clms_second_underwriting = #{idClmsSecondUnderwriting},
            </if>
            <if test="reportNo != null and reportNo != ''">
                report_no = #{reportNo},
            </if>
            <if test="caseTimes != null">
                case_times = #{caseTimes},
            </if>
            <if test="policyNo != null and policyNo != ''">
                policy_no = #{policyNo},
            </if>
            <if test="applyName != null and applyName != ''">
                apply_name = #{applyName},
            </if>
            <if test="insuredName != null and insuredName != ''">
                insured_name = #{insuredName},
            </if>
            <if test="productName != null and productName != ''">
                product_name = #{productName},
            </if>
            <if test="schemeName != null and schemeName != ''">
                scheme_name = #{schemeName},
            </if>
            <if test="insuranceBeginDate != null">
                insurance_begin_date = #{insuranceBeginDate},
            </if>
            <if test="insuranceEndDate != null">
                insurance_end_date = #{insuranceEndDate},
            </if>
            <if test="policyStatus != null and policyStatus != ''">
                policy_status = #{policyStatus},
            </if>
            <if test="uwConclusion != null and uwConclusion != ''">
                uw_conclusion = #{uwConclusion},
            </if>
            <if test="uwExceptions != null and uwExceptions != ''">
                uw_exceptions = #{uwExceptions},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdDate != null">
                created_date = #{createdDate},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDate != null">
                updated_date = #{updatedDate},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过报案号修改数据-->
    <update id="updateByReportNo">
        update clms_seconduw_policy_conclusion
        <set>
            <if test="idClmsSecondUnderwriting != null and idClmsSecondUnderwriting != ''">
                id_clms_second_underwriting = #{idClmsSecondUnderwriting},
            </if>
            <if test="reportNo != null and reportNo != ''">
                report_no = #{reportNo},
            </if>
            <if test="caseTimes != null">
                case_times = #{caseTimes},
            </if>
            <if test="policyNo != null and policyNo != ''">
                policy_no = #{policyNo},
            </if>
            <if test="applyName != null and applyName != ''">
                apply_name = #{applyName},
            </if>
            <if test="insuredName != null and insuredName != ''">
                insured_name = #{insuredName},
            </if>
            <if test="productName != null and productName != ''">
                product_name = #{productName},
            </if>
            <if test="schemeName != null and schemeName != ''">
                scheme_name = #{schemeName},
            </if>
            <if test="insuranceBeginDate != null">
                insurance_begin_date = #{insuranceBeginDate},
            </if>
            <if test="insuranceEndDate != null">
                insurance_end_date = #{insuranceEndDate},
            </if>
            <if test="policyStatus != null and policyStatus != ''">
                policy_status = #{policyStatus},
            </if>
            <if test="uwConclusion != null and uwConclusion != ''">
                uw_conclusion = #{uwConclusion},
            </if>
            <if test="uwExceptions != null and uwExceptions != ''">
                uw_exceptions = #{uwExceptions},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdDate != null">
                created_date = #{createdDate},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDate != null">
                updated_date = #{updatedDate},
            </if>
        </set>
        where report_no = #{reportno} and case_times = #{casetimes}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from clms_seconduw_policy_conclusion
        where id = #{id}
    </delete>

    <select id="getPolicyConclusionVOList" parameterType="java.lang.String" resultMap="result">
        select  policy.policy_no ,policy.apply_name ,policy.insured_name ,policy.insurance_begin_date ,policy.insurance_end_date,
        policy.policy_status,policy.product_name ,policy.scheme_name ,policy.uw_conclusion ,policy.uw_exceptions ,
        risk.plan_name ,risk.insurance_begin_date  risk_insurance_begin_date,risk.insurance_end_date  risk_insurance_end_date,
        risk.plan_status ,risk.uw_decisions ,risk.uw_exceptions  risk_uw_exceptions
        from clms_seconduw_policy_conclusion policy,clms_seconduw_plan_conclusion risk
        where policy.policy_no  =risk.policy_no
        and policy.id = risk.id_clms_seconduw_policy_conclusion
        and  policy.id_clms_second_underwriting =#{idClmsSecondUnderwriting}
    </select>

</mapper>

