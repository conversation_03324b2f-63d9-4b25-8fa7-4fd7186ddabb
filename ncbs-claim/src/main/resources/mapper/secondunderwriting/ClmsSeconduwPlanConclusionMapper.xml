<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.secondunderwriting.ClmsSeconduwPlanConclusionMapper">

    <resultMap type="com.paic.ncbs.claim.dao.entity.clms.ClmsSeconduwPlanConclusionEntity" id="ClmsSeconduwPlanConclusionMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="idClmsSeconduwPolicyConclusion" column="id_clms_seconduw_policy_conclusion"
                jdbcType="VARCHAR"/>
        <result property="policyNo" column="policy_no" jdbcType="VARCHAR"/>
        <result property="planCode" column="plan_code" jdbcType="VARCHAR"/>
        <result property="planName" column="plan_name" jdbcType="VARCHAR"/>
        <result property="insuranceBeginDate" column="insurance_begin_date" jdbcType="TIMESTAMP"/>
        <result property="insuranceEndDate" column="insurance_end_date" jdbcType="TIMESTAMP"/>
        <result property="planStatus" column="plan_status" jdbcType="VARCHAR"/>
        <result property="uwDecisions" column="uw_decisions" jdbcType="VARCHAR"/>
        <result property="uwExceptions" column="uw_exceptions" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDate" column="updated_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ClmsSeconduwPlanConclusionMap">
        select id,
               id_clms_seconduw_policy_conclusion,
               policy_no,
               plan_code,
               plan_name,
               insurance_begin_date,
               insurance_end_date,
               plan_status,
               uw_decisions,
               uw_exceptions,
               created_by,
               created_date,
               updated_by,
               updated_date
        from clms_seconduw_plan_conclusion
        where id = #{id}
    </select>



    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into clms_seconduw_plan_conclusion(id,id_clms_seconduw_policy_conclusion, report_no,case_times,policy_no, plan_code, plan_name,
                                                  insurance_begin_date, insurance_end_date, plan_status, uw_decisions,
                                                  uw_exceptions, created_by, created_date, updated_by, updated_date)
        values (#{id},#{idClmsSeconduwPolicyConclusion},#{reportNo},#{caseTimes}, #{policyNo}, #{planCode}, #{planName}, #{insuranceBeginDate},
                #{insuranceEndDate}, #{planStatus}, #{uwDecisions}, #{uwExceptions}, #{createdBy}, #{createdDate},
                #{updatedBy}, #{updatedDate})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into clms_seconduw_plan_conclusion(id,id_clms_seconduw_policy_conclusion, report_no,case_times,policy_no, plan_code, plan_name,
        insurance_begin_date, insurance_end_date, plan_status, uw_decisions, uw_exceptions, created_by, created_date,
        updated_by, updated_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.idClmsSeconduwPolicyConclusion},#{entity.reportNo},#{entity.caseTimes}, #{entity.policyNo}, #{entity.planCode}, #{entity.planName},
            #{entity.insuranceBeginDate}, #{entity.insuranceEndDate}, #{entity.planStatus}, #{entity.uwDecisions},
            #{entity.uwExceptions}, #{entity.createdBy}, #{entity.createdDate}, #{entity.updatedBy},
            #{entity.updatedDate})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update clms_seconduw_plan_conclusion
        <set>
            <if test="idClmsSeconduwPolicyConclusion != null and idClmsSeconduwPolicyConclusion != ''">
                id_clms_seconduw_policy_conclusion = #{idClmsSeconduwPolicyConclusion},
            </if>
            <if test="policyNo != null and policyNo != ''">
                policy_no = #{policyNo},
            </if>
            <if test="planCode != null and planCode != ''">
                plan_code = #{planCode},
            </if>
            <if test="planName != null and planName != ''">
                plan_name = #{planName},
            </if>
            <if test="insuranceBeginDate != null">
                insurance_begin_date = #{insuranceBeginDate},
            </if>
            <if test="insuranceEndDate != null">
                insurance_end_date = #{insuranceEndDate},
            </if>
            <if test="planStatus != null and planStatus != ''">
                plan_status = #{planStatus},
            </if>
            <if test="uwDecisions != null and uwDecisions != ''">
                uw_decisions = #{uwDecisions},
            </if>
            <if test="uwExceptions != null and uwExceptions != ''">
                uw_exceptions = #{uwExceptions},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdDate != null">
                created_date = #{createdDate},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDate != null">
                updated_date = #{updatedDate},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过报案号修改数据-->
    <update id="updateByReportNo">
        update clms_seconduw_plan_conclusion
        <set>
            <if test="idClmsSeconduwPolicyConclusion != null and idClmsSeconduwPolicyConclusion != ''">
                id_clms_seconduw_policy_conclusion = #{idClmsSeconduwPolicyConclusion},
            </if>
            <if test="policyNo != null and policyNo != ''">
                policy_no = #{policyNo},
            </if>
            <if test="planCode != null and planCode != ''">
                plan_code = #{planCode},
            </if>
            <if test="planName != null and planName != ''">
                plan_name = #{planName},
            </if>
            <if test="insuranceBeginDate != null">
                insurance_begin_date = #{insuranceBeginDate},
            </if>
            <if test="insuranceEndDate != null">
                insurance_end_date = #{insuranceEndDate},
            </if>
            <if test="planStatus != null and planStatus != ''">
                plan_status = #{planStatus},
            </if>
            <if test="uwDecisions != null and uwDecisions != ''">
                uw_decisions = #{uwDecisions},
            </if>
            <if test="uwExceptions != null and uwExceptions != ''">
                uw_exceptions = #{uwExceptions},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdDate != null">
                created_date = #{createdDate},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDate != null">
                updated_date = #{updatedDate},
            </if>
        </set>
        where report_no = #{reportno} and case_times = #{casetimes}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from clms_seconduw_plan_conclusion
        where id = #{id}
    </delete>


</mapper>

