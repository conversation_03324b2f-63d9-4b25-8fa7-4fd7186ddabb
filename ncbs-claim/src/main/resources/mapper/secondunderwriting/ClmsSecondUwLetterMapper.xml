<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.secondunderwriting.ClmsSecondUwLetterMapper">

    <resultMap type="com.paic.ncbs.claim.dao.entity.clms.ClmsSecondUwLetterEntity" id="ClmsSecondUwLetterMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="reportNo" column="report_no" jdbcType="VARCHAR"/>
        <result property="caseTimes" column="case_times" jdbcType="INTEGER"/>
        <result property="fileId" column="file_id" jdbcType="VARCHAR"/>
        <result property="fileType" column="file_type" jdbcType="TIMESTAMP"/>
        <result property="idLetterInfo" column="id_letter_info" jdbcType="VARCHAR"/>
        <result property="idClmsSecondUnderwriting" column="id_clms_second_underwriting" jdbcType="VARCHAR"/>
        <result property="letterConclusion" column="letter_conclusion" jdbcType="VARCHAR"/>
        <result property="letterExplain" column="letter_explain" jdbcType="VARCHAR"/>
        <result property="uploadFileId" column="upload_file_id" jdbcType="VARCHAR"/>
        <result property="letterCancelOperator" column="letter_cancel_operator" jdbcType="VARCHAR"/>
        <result property="letterSendDate" column="letter_send_date" jdbcType="TIMESTAMP"/>
        <result property="letterCancelDate" column="letter_cancel_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--新增所有列-->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into clms_second_uw_letter(
        id,
        report_no,
        case_times,
        file_id,
        file_type,
        id_letter_info,
        id_clms_second_underwriting,
        letter_conclusion,
        letter_explain,
        upload_file_id,
        letter_cancel_operator,
        letter_send_date,
        letter_cancel_date,
        created_by,
        created_date,
        updated_by,
        updated_date)
        values
        <foreach collection="entitys" item="entity" separator=",">
            (#{entity.id},
            #{entity.reportNo},
            #{entity.caseTimes},
            #{entity.fileId},
            #{entity.fileType},
            #{entity.idLetterInfo},
            #{entity.idClmsSecondUnderwriting},
            #{entity.letterConclusion},
            #{entity.letterExplain},
            #{entity.uploadFileId},
            #{entity.letterCancelOperator},
            #{entity.letterSendDate},
            #{entity.letterCancelDate},
            #{entity.createdBy},
            #{entity.createdDate},
            #{entity.updatedBy},
            #{entity.updatedDate})
        </foreach>

    </insert>

    <select id="getLists" parameterType="java.lang.String" resultMap="ClmsSecondUwLetterMap">
        select id,report_no,case_times,file_id,file_type,id_letter_info,letter_conclusion,letter_explain,upload_file_id,
        letter_cancel_operator,letter_send_date,letter_cancel_date
        from clms_second_uw_letter
        where id_clms_second_underwriting=#{idClmsSecondUnderwriting}

    </select>
    <!--通过主键修改数据-->
    <update id="update">
        update clms_second_uw_letter
        <set>
            <if test="letterConclusion != null and letterConclusion != ''">
                letter_conclusion = #{letterConclusion},
            </if>
            <if test="letterExplain != null and letterExplain != ''">
                letter_explain = #{letterExplain},
            </if>
            <if test="uploadFileId != null and uploadFileId != ''">
                upload_file_id = #{uploadFileId},
            </if>
            <if test="letterCancelOperator != null and letterCancelOperator !=''">
                letter_cancel_operator=#{letterCancelOperator},
            </if>
            <if test="letterCancelDate != null">
                letter_cancel_date=#{letterCancelDate},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDate != null">
                updated_date = #{updatedDate}
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateBatch">
        <foreach collection="updateEntityList" index="index" item="entity" open="" separator=";" close=";">
            update clms_second_uw_letter
            <set>
                <if test="entity.letterConclusion != null and entity.letterConclusion != ''">
                    letter_conclusion = #{entity.letterConclusion},
                </if>
                <if test="entity.letterExplain != null and entity.letterExplain != ''">
                    letter_explain = #{entity.letterExplain},
                </if>
                <if test="entity.uploadFileId != null and entity.uploadFileId != ''">
                    upload_file_id = #{entity.uploadFileId},
                </if>
                <if test="entity.letterCancelOperator != null and entity.letterCancelOperator !=''">
                    letter_cancel_operator=#{entity.letterCancelOperator},
                </if>
                <if test="entity.letterCancelDate != null">
                    letter_cancel_date=#{entity.letterCancelDate},
                </if>
                <if test="entity.updatedBy != null and entity.updatedBy != ''">
                    updated_by = #{entity.updatedBy},
                </if>
                <if test="entity.updatedDate != null">
                    updated_date = #{entity.updatedDate}
                </if>
            </set>
            where id = #{entity.id}
        </foreach>

    </update>

    <select id="getUwConclusionList" parameterType="java.lang.String" resultType="com.paic.ncbs.claim.model.dto.secondunderwriting.UwConclusionDTO">
        SELECT pc.policy_no policyNo,
               pc.uw_conclusion uwConclusion,
               pc.uw_exceptions uwExceptions,
               (SELECT c.client_no FROM clms_report_customer c WHERE c.report_no = uw.report_no LIMIT 1) clientNo
        FROM clms_second_uw_letter ul
        INNER JOIN clms_second_underwriting uw ON ul.id_clms_second_underwriting = uw.id
        INNER JOIN clms_seconduw_policy_conclusion pc ON pc.id_clms_second_underwriting = uw.id
        WHERE uw.letters_cancel_status = '03' AND ul.id = #{idSecondUWLetter}
    </select>
</mapper>

