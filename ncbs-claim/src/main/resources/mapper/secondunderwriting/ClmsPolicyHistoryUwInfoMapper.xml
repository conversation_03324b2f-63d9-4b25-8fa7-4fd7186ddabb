<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.secondunderwriting.ClmsPolicyHistoryUwInfoMapper">

    <resultMap type="com.paic.ncbs.claim.dao.entity.clms.ClmsPolicyHistoryUwInfoEntity" id="ClmsPolicyHistoryUwInfoMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="policyNo" column="policy_no" jdbcType="VARCHAR"/>
        <result property="productName" column="product_name" jdbcType="VARCHAR"/>
        <result property="schemeName" column="scheme_name" jdbcType="VARCHAR"/>
        <result property="planName" column="plan_name" jdbcType="VARCHAR"/>
        <result property="insuranceBeginDate" column="insurance_begin_date" jdbcType="TIMESTAMP"/>
        <result property="insuranceEndDate" column="insurance_end_date" jdbcType="TIMESTAMP"/>
        <result property="planStatus" column="plan_status" jdbcType="VARCHAR"/>
        <result property="uwConclusion" column="uw_conclusion" jdbcType="VARCHAR"/>
        <result property="uwExceptions" column="uw_exceptions" jdbcType="VARCHAR"/>
        <result property="uwCompleteDate" column="uw_complete_date" jdbcType="TIMESTAMP"/>
        <result property="uwDataSource" column="uw_data_source" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDate" column="updated_date" jdbcType="TIMESTAMP"/>
        <result property="uwDataSource" column="uwDataSource"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, policy_no, product_name, scheme_name, plan_name, insurance_begin_date, insurance_end_date, plan_status, uw_conclusion, uw_exceptions, uw_complete_date, uw_data_source, created_by, created_date, updated_by, updated_date
    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="ClmsPolicyHistoryUwInfoMap">
        select
        <include refid="Base_Column_List"/>

        from clms_policy_history_uw_info
        where id = #{id}
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into clms_policy_history_uw_info(id, report_no,policy_no, product_name, scheme_name, plan_name,
                                                insurance_begin_date, insurance_end_date, plan_status, uw_conclusion,
                                                uw_exceptions, uw_complete_date, uw_data_source,business_type, created_by,
                                                created_date, updated_by, updated_date)
        values (#{id},#{reportNo}, #{policyNo}, #{productName}, #{schemeName}, #{planName}, #{insuranceBeginDate},
                #{insuranceEndDate}, #{planStatus}, #{uwConclusion}, #{uwExceptions}, #{uwCompleteDate},
                #{uwDataSource},#{businessType}, #{createdBy}, #{createdDate}, #{updatedBy}, #{updatedDate})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into clms_policy_history_uw_info(id, report_no,policy_no, product_name, scheme_name, plan_name,
        insurance_begin_date, insurance_end_date, plan_status, uw_conclusion, uw_exceptions, uw_complete_date,
        uw_data_source,business_type, created_by, created_date, updated_by, updated_date, insured_name, client_no)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.reportNo}, #{entity.policyNo}, #{entity.productName}, #{entity.schemeName}, #{entity.planName},
            #{entity.insuranceBeginDate}, #{entity.insuranceEndDate}, #{entity.planStatus}, #{entity.uwConclusion},
            #{entity.uwExceptions}, #{entity.uwCompleteDate}, #{entity.uwDataSource},#{entity.businessType}, #{entity.createdBy},
            #{entity.createdDate}, #{entity.updatedBy}, #{entity.updatedDate}, #{entity.insuredName}, #{entity.clientNo})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update clms_policy_history_uw_info
        <set>
            <if test="policyNo != null and policyNo != ''">
                policy_no = #{policyNo},
            </if>
            <if test="productName != null and productName != ''">
                product_name = #{productName},
            </if>
            <if test="schemeName != null and schemeName != ''">
                scheme_name = #{schemeName},
            </if>
            <if test="planName != null and planName != ''">
                plan_name = #{planName},
            </if>
            <if test="insuranceBeginDate != null">
                insurance_begin_date = #{insuranceBeginDate},
            </if>
            <if test="insuranceEndDate != null">
                insurance_end_date = #{insuranceEndDate},
            </if>
            <if test="planStatus != null and planStatus != ''">
                plan_status = #{planStatus},
            </if>
            <if test="uwConclusion != null and uwConclusion != ''">
                uw_conclusion = #{uwConclusion},
            </if>
            <if test="uwExceptions != null and uwExceptions != ''">
                uw_exceptions = #{uwExceptions},
            </if>
            <if test="uwCompleteDate != null">
                uw_complete_date = #{uwCompleteDate},
            </if>
            <if test="uwDataSource != null and uwDataSource != ''">
                uw_data_source = #{uwDataSource},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdDate != null">
                created_date = #{createdDate},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDate != null">
                updated_date = #{updatedDate},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from clms_policy_history_uw_info
        where id = #{id}
    </delete>
    <select id="getClmsPolicyHistoryUwInfoList" parameterType="java.lang.String" resultType="com.paic.ncbs.claim.dao.entity.clms.ClmsPolicyHistoryUwInfoEntity">
        select distinct a.policy_no,product_name,scheme_name,plan_name,insurance_begin_date,insurance_end_date,plan_status,uw_conclusion,uw_exceptions,
        uw_complete_date,uw_data_source,insured_name,client_no
        from clms_policy_history_uw_info a
        where a.REPORT_NO =#{reportNo}
    </select>
    <select id="getPolicyNoUwConclusionInfo" resultType="com.paic.ncbs.claim.dao.entity.clms.ClmsPolicyHistoryUwInfoEntity">
        select distinct a.policy_no,product_name,scheme_name,plan_name,insurance_begin_date,insurance_end_date,plan_status,uw_conclusion,uw_exceptions,
        uw_complete_date,uw_data_source,insured_name,client_no
        from clms_policy_history_uw_info a
        where a.policy_no = #{policyNo}
        and a.scheme_name = #{schemeName}
    </select>
    <select id="getPlanUwPlanConclusionInfo"  resultType="com.paic.ncbs.claim.dao.entity.clms.ClmsPolicyHistoryUwInfoEntity">
        select cspc.REPORT_NO,cspc.policy_no,csp.product_name,csp.scheme_name,cspc.plan_name,cspc.insurance_begin_date,cspc.insurance_end_date, cspc.plan_status,cspc.uw_decisions uwConclusion,cspc.uw_exceptions,cspc.created_date uwCompleteDate,'0' uwDataSource
        from clms_seconduw_plan_conclusion cspc ,clms_seconduw_policy_conclusion csp
        where cspc.id_clms_seconduw_policy_conclusion =csp.id
        and cspc.policy_no in
        <foreach collection="list" separator="," open="(" close=")" item="policyNo">
            #{policyNo}
        </foreach>
        and exists (select 1 from clm_whole_case_base a where a.REPORT_NO= cspc.REPORT_NO and a.WHOLE_CASE_STATUS='0' )
    </select>

</mapper>

