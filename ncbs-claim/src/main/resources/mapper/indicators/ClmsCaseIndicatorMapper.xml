<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.indicators.ClmsCaseIndicatorMapper">

    <!-- 通用查询映射结果
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.indicators.ClmsCaseIndicator">
        <id column="id" property="id" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
        <result column="report_no" property="reportNo" />
        <result column="case_times" property="caseTimes" />
        <result column="id_linked" property="idLinked" />
        <result column="indicator_code" property="indicatorCode" />
        <result column="indicator_name" property="indicatorName" />
        <result column="indicator_value" property="indicatorValue" />
        <result column="indicator_unit" property="indicatorUnit" />
        <result column="indicator_cal" property="indicatorCal" />
        <result column="indicator_cal_des" property="indicatorCalDes" />
        <result column="value_stable" property="valueStable" />
        <result column="remark" property="remark" />
    </resultMap-->

    <insert id="stable4firstEndCase" parameterType="map">
        insert into clms_case_indicator(created_by, updated_by, report_no, case_times, id_linked, indicator_code,
                                        indicator_name,indicator_value, indicator_unit, indicator_cal, indicator_cal_des,value_stable)
        with case_range as (
            select
                #{lastLog.createdBy} created_by,
                #{lastLog.updatedBy} updated_by,
                cwcb.report_no,
                cwcb.case_times,
                null id_linked,
                #{lastLog.indicatorCode} indicator_code,
                #{lastLog.indicatorName} indicator_name,
                timestampdiff(second, cri.report_date , cwcb.END_CASE_DATE) indicator_value,
                'M' indicator_unit,
                concat(date_format(cwcb.END_CASE_DATE,'%Y-%m-%d %H:%i:%s'), '-', date_format(cri.report_date , '%Y-%m-%d %H:%i:%s')) indicator_cal,
                '结案时间-报案时间' indicator_cal_des,
                '1' value_stable
            from
                clm_whole_case_base cwcb,
                clm_report_info cri
            where cwcb.REPORT_NO = cri.REPORT_NO
                AND cwcb.CASE_TIMES = 1
                and cri.report_date > str_to_date('2025-4-1 00:00:00', '%Y-%m-%d %H:%i:%s')
                and cwcb.END_CASE_DATE >= #{lastLog.endTime}
                and cwcb.END_CASE_DATE &lt; #{endTime}
        ),
        supplyments as(
            select
                report_no,
                case_times,
                sum(TIMESTAMPDIFF(SECOND, t.CREATED_TIME, IFNULL(t.COMPLETE_TIME, now()))) differ
            FROM clms_task_info t
            WHERE exists (select 1 from case_range r
                           where t.REPORT_NO = r.REPORT_NO
                                AND t.CASE_TIMES = r.CASE_TIMES
                                AND t.TASK_DEFINITION_BPM_KEY = 'OC_WAIT_CUSTOMER_SUPPLEMENTS')
            group by report_no, case_times
        )
        select
            r.created_by,
            r.updated_by,
            r.report_no,
            r.case_times,
            r.id_linked,
            r.indicator_code,
            r.indicator_name,
            (case when s.differ is null then r.indicator_value else r.indicator_value-s.differ end),
            indicator_unit,
            (case when s.differ is null then r.indicator_cal else concat(r.indicator_cal, '-', s.differ) end),
            (case when s.differ is null then r.indicator_cal_des else concat(r.indicator_cal_des, '-', '补材经过时间') end),
            value_stable
        from case_range r left join supplyments s on r.report_no = s.report_no and r.case_times = s.case_times
    </insert>

    <insert id="unstable4firstEndCase" parameterType="map">
        insert into clms_case_indicator(created_by, updated_by, report_no, case_times, id_linked, indicator_code,
                                        indicator_name,indicator_value, indicator_unit, indicator_cal, indicator_cal_des,value_stable)
        with case_range as (
            select
                #{lastLog.createdBy} created_by,
                #{lastLog.updatedBy} updated_by,
                cwcb.report_no,
                cwcb.case_times,
                null id_linked,
                #{lastLog.indicatorCode} indicator_code,
                #{lastLog.indicatorName} indicator_name,
                timestampdiff(second, cri.report_date, now()) indicator_value,
                'M' indicator_unit,
                concat(date_format(now(),'%Y-%m-%d %H:%i:%s'), '-', date_format(cri.report_date , '%Y-%m-%d %H:%i:%s')) indicator_cal,
                '当前时间-报案时间' indicator_cal_des,
                '0' value_stable
            from
                clm_whole_case_base cwcb,
                clm_report_info cri
            where cwcb.REPORT_NO = cri.REPORT_NO
                and cwcb.end_case_date is null
                AND cwcb.CASE_TIMES = 1
        ),
        supplyments as(
            select
                report_no,
                case_times,
                sum(TIMESTAMPDIFF(SECOND, t.CREATED_TIME, IFNULL(t.COMPLETE_TIME, now()))) differ
            FROM clms_task_info t
            WHERE exists (select 1 from case_range r
                        where t.REPORT_NO = r.REPORT_NO
                        AND t.CASE_TIMES = r.CASE_TIMES
                        AND t.TASK_DEFINITION_BPM_KEY = 'OC_WAIT_CUSTOMER_SUPPLEMENTS')
            group by report_no, case_times
        )
        select
            r.created_by,
            r.updated_by,
            r.report_no,
            r.case_times,
            r.id_linked,
            r.indicator_code,
            r.indicator_name,
            (case when s.differ is null then r.indicator_value else r.indicator_value-s.differ end),
            indicator_unit,
            (case when s.differ is null then r.indicator_cal else concat(r.indicator_cal,'-', s.differ) end),
            (case when s.differ is null then r.indicator_cal_des else concat(r.indicator_cal_des, '-', '补材经过时间') end),
            value_stable
        from case_range r left join supplyments s on r.report_no = s.report_no and r.case_times = s.case_times
    </insert>

    <insert id="stable4claimRegistration" parameterType="map">
        insert into clms_case_indicator(created_by, updated_by, report_no, case_times, id_linked, indicator_code,
                                        indicator_name,indicator_value, indicator_unit, indicator_cal, indicator_cal_des,value_stable)
        select
            #{lastLog.createdBy},
            #{lastLog.updatedBy},
            cwcb.report_no,
            cwcb.case_times,
            null,
            #{lastLog.indicatorCode},
            #{lastLog.indicatorName},
            timestampdiff(second, cri.report_date , cwcb.REGISTER_DATE),
            'M',
            concat(date_format(cwcb.REGISTER_DATE,'%Y-%m-%d %H:%i:%s'), '-', date_format(cri.report_date , '%Y-%m-%d %H:%i:%s')),
            '立案时间-报案时间',
            '1'
        from
            clm_whole_case_base cwcb,
            clm_report_info cri
        where cwcb.REPORT_NO = cri.REPORT_NO
            and cri.report_date > str_to_date('2025-4-1 00:00:00', '%Y-%m-%d %H:%i:%s')
            and cwcb.REGISTER_DATE >= #{lastLog.endTime}
            and cwcb.REGISTER_DATE &lt; #{endTime}
            AND cwcb.CASE_TIMES = 1
    </insert>

    <insert id="unstable4claimRegistration" parameterType="map">
        insert into clms_case_indicator(created_by, updated_by, report_no, case_times, id_linked, indicator_code,
                                        indicator_name,indicator_value, indicator_unit, indicator_cal, indicator_cal_des,value_stable)
        select
            #{lastLog.createdBy},
            #{lastLog.updatedBy},
            cwcb.report_no,
            cwcb.case_times,
            null,
            #{lastLog.indicatorCode},
            #{lastLog.indicatorName},
            timestampdiff(second, cri.report_date, now()),
            'M',
            concat(date_format(now(),'%Y-%m-%d %H:%i:%s'), '-', date_format(cri.report_date , '%Y-%m-%d %H:%i:%s')),
            '系统当前时间-报案时间',
            '0'
        from
            clm_whole_case_base cwcb,
            clm_report_info cri
        where cwcb.REPORT_NO = cri.REPORT_NO
            and cwcb.REGISTER_DATE is null
            AND cwcb.CASE_TIMES = 1
    </insert>


    <insert id="reopenEndCase" parameterType="map">
        insert into clms_case_indicator(created_by, updated_by, report_no, case_times, id_linked, indicator_code,
                                        indicator_name,indicator_value, indicator_unit, indicator_cal, indicator_cal_des,value_stable)
        with reopen_range as(
            select
                #{lastLog.createdBy} created_by,
                #{lastLog.updatedBy} updated_by,
                cwcb.report_no,
                cwcb.case_times,
                crcr.id_clm_restart_case_record id_linked,
                #{lastLog.indicatorCode} indicator_code,
                #{lastLog.indicatorName} indicator_name,
                case
                    when cwcb.END_CASE_DATE is null then timestampdiff(second, crcr.updated_date , now())
                    else  timestampdiff(second, crcr.updated_date , cwcb.END_CASE_DATE)
                end indicator_value,
                'M' indicator_unit,
                concat(date_format(ifnull(cwcb.END_CASE_DATE, now()),'%Y-%m-%d %H:%i:%s'), '-', date_format(cri.updated_date , '%Y-%m-%d %H:%i:%s')) indicator_cal,
                case
                    when cwcb.END_CASE_DATE is null then '当前时间-重开审批通过时间'
                    else '重开结案时间-重开审批通过时间'
                end indicator_cal_des,
                case when cwcb.END_CASE_DATE is null then '0' else '1' end value_stable
            from
                clm_restart_case_record crcr,
                clm_whole_case_base cwcb,
                clm_report_info cri
            where crcr.REPORT_NO = cwcb.REPORT_NO
                and crcr.report_no=cri.REPORT_NO
                and crcr.case_times+1=cwcb.case_times
                and crcr.APPROVAL_OPINIONS='0'
                and cri.report_date > str_to_date('2025-4-1 00:00:00', '%Y-%m-%d %H:%i:%s')
                and ((cwcb.END_CASE_DATE >= #{lastLog.endTime}
                and cwcb.END_CASE_DATE &lt; #{endTime}) or cwcb.END_CASE_DATE is null)
        ),
        supplyments as(
            select
                report_no,
                case_times,
                sum(TIMESTAMPDIFF(SECOND, t.CREATED_TIME, IFNULL(t.COMPLETE_TIME, now()))) differ
            FROM clms_task_info t
            WHERE exists (select 1 from  reopen_range r
                        where t.REPORT_NO = r.REPORT_NO
                        AND t.CASE_TIMES = r.CASE_TIMES
                        AND t.TASK_DEFINITION_BPM_KEY = 'OC_WAIT_CUSTOMER_SUPPLEMENTS')
            group by report_no, case_times
        )
        select
            r.created_by,
            r.updated_by,
            r.report_no,
            r.case_times,
            r.id_linked,
            r.indicator_code,
            r.indicator_name,
            (case when s.differ is null then r.indicator_value else r.indicator_value-s.differ end),
            indicator_unit,
            (case when s.differ is null then r.indicator_value else concat(r.indicator_value, '-', s.differ) end),
            (case when s.differ is null then r.indicator_cal_des else concat(r.indicator_cal_des,'-补材经过时间') end),
            value_stable
        from reopen_range r left join supplyments s on r.report_no = s.report_no and r.case_times = s.case_times
    </insert>

    <insert id="stable4outInvestigate" parameterType="map">
        insert into clms_case_indicator(created_by, updated_by, report_no, case_times, id_linked, indicator_code,
                                        indicator_name,indicator_value, indicator_unit, indicator_cal, indicator_cal_des,value_stable)
        select
            #{lastLog.createdBy},
            #{lastLog.updatedBy},
            ci.report_no,
            ci.case_times,
            ci.ID_AHCS_INVESTIGATE,
            #{lastLog.indicatorCode},
            #{lastLog.indicatorName},
            timestampdiff(second, cia.updated_date , ci.updated_date),
            'M',
            concat(date_format(ci.updated_date,'%Y-%m-%d %H:%i:%s'), '-', date_format(cia.updated_date, '%Y-%m-%d %H:%i:%s')),
            '调查审批通过时间-提调审批通过时间',
            '1' value_stable
        from
            clms_investigate ci,
            clms_investigate_audit cia
        where
            ci.ID_AHCS_INVESTIGATE = cia.ID_AHCS_INVESTIGATE
            and exists(select 1 from clm_report_info cri where ci.report_no = cri.report_no and cri.report_date > str_to_date('2025-4-1 00:00:00', '%Y-%m-%d %H:%i:%s'))
            and ci.investigate_status = 4
            and ci.init_mode = '02'
            and ci.updated_date >= #{lastLog.endTime}
            and ci.updated_date &lt; #{endTime}
    </insert>

    <insert id="unstable4outInvestigate" parameterType="map">
        insert into clms_case_indicator(created_by, updated_by, report_no, case_times, id_linked, indicator_code,
                                        indicator_name,indicator_value, indicator_unit, indicator_cal, indicator_cal_des,value_stable)
        select
            #{lastLog.createdBy},
            #{lastLog.updatedBy},
            ci.report_no,
            ci.case_times,
            ci.ID_AHCS_INVESTIGATE,
            #{lastLog.indicatorCode},
            #{lastLog.indicatorName},
            timestampdiff(second, cia.updated_date , now()),
            'M',
            concat(date_format(now(),'%Y-%m-%d %H:%i:%s'), '-', date_format(cia.updated_date, '%Y-%m-%d %H:%i:%s')),
            '系统当前时间-提调审批通过时间',
            '0'
        from
            clms_investigate ci,
            clms_investigate_audit cia
        where
            ci.ID_AHCS_INVESTIGATE = cia.ID_AHCS_INVESTIGATE
            and ci.investigate_status in ('2','3')
            and ci.init_mode = '02'
    </insert>

    <insert id="stable4communicate" parameterType="map">
        insert into clms_case_indicator(created_by, updated_by, report_no, case_times, id_linked, indicator_code,
                                        indicator_name,indicator_value, indicator_unit, indicator_cal, indicator_cal_des,value_stable)
        select
            #{lastLog.createdBy},
            #{lastLog.updatedBy},
            cab.report_no,
            cab.case_times,
            cab.id_ahcs_communicate_base,
            #{lastLog.indicatorCode},
            #{lastLog.indicatorName},
            timestampdiff(second, cab.created_date, cab.finish_date),
            'M',
            concat(date_format(cab.finish_date,'%Y-%m-%d %H:%i:%s'), '-', date_format(cab.created_date, '%Y-%m-%d %H:%i:%s')),
            '沟通回销时间-沟通发起时间',
            '1'
        from
            clms_communicate_base cab
        where
            exists(select 1 from clm_report_info cri where cab.report_no = cri.report_no and cri.report_date > str_to_date('2025-4-1 00:00:00', '%Y-%m-%d %H:%i:%s'))
            and cab.communicate_status = '2'
            and cab.updated_date >= #{lastLog.endTime}
            and cab.updated_date &lt; #{endTime}
    </insert>

    <insert id="unstable4communicate" parameterType="map">
        insert into clms_case_indicator(created_by, updated_by, report_no, case_times, id_linked, indicator_code,
                                        indicator_name,indicator_value, indicator_unit, indicator_cal, indicator_cal_des,value_stable)

        select
            #{lastLog.createdBy},
            #{lastLog.updatedBy},
            cab.report_no,
            cab.case_times,
            cab.id_ahcs_communicate_base,
            #{lastLog.indicatorCode},
            #{lastLog.indicatorName},
            timestampdiff(second, cab.created_date , now()),
            'M',
            concat(date_format(now(),'%Y-%m-%d %H:%i:%s'), '-', date_format(cab.created_date, '%Y-%m-%d %H:%i:%s')),
            '系统当前时间-沟通发起时间',
            '0'
        from
            clms_communicate_base cab
        where
            cab.communicate_status in ('0','1')
    </insert>

    <insert id="stable4secondUnderwriting" parameterType="map">
        insert into clms_case_indicator(created_by, updated_by, report_no, case_times, id_linked, indicator_code,
                                        indicator_name,indicator_value, indicator_unit, indicator_cal, indicator_cal_des,value_stable)
        select
            #{lastLog.createdBy},
            #{lastLog.updatedBy},
            csu.report_no,
            csu.case_times,
            csu.id,
            #{lastLog.indicatorCode},
            #{lastLog.indicatorName},
            timestampdiff(second, csu.uw_start_date, csu.uw_complete_date),
            'M',
            concat(date_format(csu.uw_complete_date,'%Y-%m-%d %H:%i:%s'), '-', date_format(csu.uw_start_date, '%Y-%m-%d %H:%i:%s')),
            '二核回销时间-二核发起时间',
            '1'
        from
            clms_second_underwriting csu
        where
            exists(select 1 from clm_report_info cri where csu.report_no = cri.report_no and cri.report_date > str_to_date('2025-4-1 00:00:00', '%Y-%m-%d %H:%i:%s'))
            and csu.underwriting_status = '02'
            and csu.updated_date >= #{lastLog.endTime}
            and csu.updated_date &lt; #{endTime}
    </insert>

    <insert id="unstable4secondUnderwriting" parameterType="map">
        insert into clms_case_indicator(created_by, updated_by, report_no, case_times, id_linked, indicator_code,
                                        indicator_name,indicator_value, indicator_unit, indicator_cal, indicator_cal_des,value_stable)
        select
            #{lastLog.createdBy},
            #{lastLog.updatedBy},
            csu.report_no,
            csu.case_times,
            csu.id,
            #{lastLog.indicatorCode},
            #{lastLog.indicatorName},
            timestampdiff(second, csu.uw_start_date, now()),
            'M',
            concat(date_format(now(),'%Y-%m-%d %H:%i:%s'), '-', date_format(csu.uw_start_date, '%Y-%m-%d %H:%i:%s')),
            '系统当前时间-二核发起时间',
            '0'
        from
            clms_second_underwriting csu
        where
            csu.underwriting_status = '01'
    </insert>
    <select id="getTimeOutCaseList" resultType="com.paic.ncbs.claim.model.vo.doc.TimeOutCaseExcelDataVo"
            parameterType="com.paic.ncbs.claim.model.vo.doc.TimeOutExportVo">
        select cci.indicator_code AS timeOutType,
        (select dd.DEPARTMENT_ABBR_NAME  from department_define dd where DEPARTMENT_CODE =cti.DEPARTMENT_CODE) AS departmentCode,
        cci.report_no AS reportNo,
        cti.ASSIGNEE_NAME  AS dealCode,
        cci.indicator_value/ 86400.0 AS timeoutDay,
        '新非车' AS systemCode
        from clms_case_indicator cci
        inner join clm_whole_case_base cwcb on cwcb.REPORT_NO =cci.report_no and cwcb.CASE_TIMES =cci.case_times
        left join clms_task_info cti on cti.REPORT_NO = cci.report_no and cci.case_times = cti.CASE_TIMES
        where ((cci.indicator_code = 'E002' and cci.indicator_value > 10*24*60*60 and cwcb.REGISTER_UM is null and cti.TASK_DEFINITION_BPM_KEY ='OC_REPORT_TRACK')
        or (cci.indicator_code = 'E001' and cci.indicator_value > 25*24*60*60 and cwcb.END_CASE_DATE  is null and cti.TASK_DEFINITION_BPM_KEY in('OC_CHECK_DUTY','OC_MANUAL_SETTLE','OC_SETTLE_REVIEW')))
        and STATUS in ('0','3')
        and (cti.ASSIGNER = #{requestData.dealCode}
        or cti.ASSIGNER is null
        or cti.ASSIGNER ='')
        AND cti.DEPARTMENT_CODE IN
        <foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
          order by cti.DEPARTMENT_CODE,cci.indicator_code;
    </select>
</mapper>