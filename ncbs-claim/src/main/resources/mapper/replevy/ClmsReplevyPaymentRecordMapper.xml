<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.paic.ncbs.claim.replevy.dao.ClmsReplevyPaymentRecordMapper">
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="com.paic.ncbs.claim.replevy.entity.ClmsReplevyPaymentRecord">
		 <id column="id" property="id"/> 
		 <result column="report_no" property="reportNo"/> 
		 <result column="replevy_no" property="replevyNo"/> 
		 <result column="replevy_times" property="replevyTimes"/>
		 <result column="case_times" property="caseTimes"/>
		 <result column="request_type" property="requestType"/> 
		 <result column="business_no" property="businessNo"/> 
		 <result column="trans_date" property="transDate"/> 
		 <result column="direction_type" property="directionType"/> 
		 <result column="trans_amount" property="transAmount"/> 
		 <result column="trans_purpose" property="transPurpose"/> 
		 <result column="trans_abstract" property="transAbstract"/> 
		 <result column="payment_flowNo" property="paymentFlowno"/> 
		 <result column="banktrans_flow_no" property="banktransFlowNo"/> 
		 <result column="ourBank_account" property="ourbankAccount"/> 
		 <result column="partner_bank_account" property="partnerBankAccount"/> 
		 <result column="partner_bank_name" property="partnerBankName"/> 
		 <result column="partner_bank_branch_name" property="partnerBankBranchName"/> 
		 <result column="partner_bank_account_name" property="partnerBankAccountName"/> 
		 <result column="receipt_file_cosurl" property="receiptFileCosurl"/> 
		 <result column="receipt_file_cosid" property="receiptFileCosid"/> 
		 <result column="post_script" property="postScript"/> 
		 <result column="write_off_remain_amount" property="writeOffRemainAmount"/>
		 <result column="freeze_flag" property="freezeFlag"/>
		 <result column="valid_flag" property="validFlag"/>
		 <result column="flag" property="flag"/> 
		 <result column="request_param" property="requestParam"/> 
		 <result column="response_param" property="responseParam"/> 
		 <result column="response_code" property="responseCode"/> 
		 <result column="created_by" property="createdBy"/> 
		 <result column="sys_ctime" property="sysCtime"/> 
		 <result column="updated_by" property="updatedBy"/> 
		 <result column="sys_utime" property="sysUtime"/> 
	</resultMap>

	<!-- 通用查询结果列-->
	<sql id="Base_Column_List">
		 id, report_no, replevy_no, replevy_times,case_times, request_type,
		 business_no, trans_date, direction_type, trans_amount, trans_purpose,
		 trans_abstract, payment_flowNo, banktrans_flow_no, ourBank_account, partner_bank_account,
		 partner_bank_name, partner_bank_branch_name, partner_bank_account_name, receipt_file_cosurl, receipt_file_cosid,
		 post_script, write_off_remain_amount,freeze_flag,valid_flag, flag, request_param,
		 response_param, response_code, created_by, sys_ctime, updated_by,
		 sys_utime
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null" >
			and id = #{id}
		</if>
		<if test="reportNo != null" >
			and report_no = #{reportNo}
		</if>
		<if test="replevyNo != null" >
			and replevy_no = #{replevyNo}
		</if>
		<if test="replevyTimes != null" >
			and replevy_times = #{replevyTimes}
		</if>
		<if test="caseTimes != null" >
			and case_times = #{caseTimes}
		</if>
		<if test="requestType != null" >
			and request_type = #{requestType}
		</if>
		<if test="businessNo != null" >
			and business_no = #{businessNo}
		</if>
		<if test="transDate != null" >
			and trans_date = #{transDate}
		</if>
		<if test="directionType != null" >
			and direction_type = #{directionType}
		</if>
		<if test="transAmount != null" >
			and trans_amount = #{transAmount}
		</if>
		<if test="transPurpose != null" >
			and trans_purpose = #{transPurpose}
		</if>
		<if test="transAbstract != null" >
			and trans_abstract = #{transAbstract}
		</if>
		<if test="paymentFlowno != null" >
			and payment_flowNo = #{paymentFlowno}
		</if>
		<if test="banktransFlowNo != null" >
			and banktrans_flow_no = #{banktransFlowNo}
		</if>
		<if test="ourbankAccount != null" >
			and ourBank_account = #{ourbankAccount}
		</if>
		<if test="partnerBankAccount != null" >
			and partner_bank_account = #{partnerBankAccount}
		</if>
		<if test="partnerBankName != null" >
			and partner_bank_name = #{partnerBankName}
		</if>
		<if test="partnerBankBranchName != null" >
			and partner_bank_branch_name = #{partnerBankBranchName}
		</if>
		<if test="partnerBankAccountName != null" >
			and partner_bank_account_name = #{partnerBankAccountName}
		</if>
		<if test="receiptFileCosurl != null" >
			and receipt_file_cosurl = #{receiptFileCosurl}
		</if>
		<if test="receiptFileCosid != null" >
			and receipt_file_cosid = #{receiptFileCosid}
		</if>
		<if test="postScript != null" >
			and post_script = #{postScript}
		</if>
		<if test="writeOffRemainAmount != null" >
			and write_off_remain_amount = #{writeOffRemainAmount}
		</if>
		<if test="freezeFlag != null" >
			and freeze_flag = #{freezeFlag}
		</if>
		<if test="validFlag != null" >
			and valid_flag = #{validFlag}
		</if>
		<if test="flag != null" >
			and flag = #{flag}
		</if>
		<if test="requestParam != null" >
			and request_param = #{requestParam}
		</if>
		<if test="responseParam != null" >
			and response_param = #{responseParam}
		</if>
		<if test="responseCode != null" >
			and response_code = #{responseCode}
		</if>
		<if test="createdBy != null" >
			and created_by = #{createdBy}
		</if>
		<if test="sysCtime != null" >
			and sys_ctime = #{sysCtime}
		</if>
		<if test="updatedBy != null" >
			and updated_by = #{updatedBy}
		</if>
		<if test="sysUtime != null" >
			and sys_utime = #{sysUtime}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from clms_replevy_payment_record
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_replevy_payment_record
		where id = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_replevy_payment_record
		where id in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyPaymentRecord">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from clms_replevy_payment_record
		where id = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from clms_replevy_payment_record
		where id in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyPaymentRecord">
		insert into clms_replevy_payment_record (id, report_no, replevy_no, replevy_times,case_times, request_type,
			business_no, trans_date, direction_type, trans_amount, trans_purpose, 
			trans_abstract, payment_flowNo, banktrans_flow_no, ourBank_account, partner_bank_account, 
			partner_bank_name, partner_bank_branch_name, partner_bank_account_name, receipt_file_cosurl, receipt_file_cosid, 
			post_script, write_off_remain_amount, freeze_flag,valid_flag, flag, request_param,
			response_param, response_code, created_by, sys_ctime, updated_by, 
			sys_utime)
		values(#{id}, #{reportNo}, #{replevyNo}, #{replevyTimes}, #{requestType}, 
			#{businessNo}, #{transDate}, #{directionType}, #{transAmount}, #{transPurpose}, 
			#{transAbstract}, #{paymentFlowno}, #{banktransFlowNo}, #{ourbankAccount}, #{partnerBankAccount}, 
			#{partnerBankName}, #{partnerBankBranchName}, #{partnerBankAccountName}, #{receiptFileCosurl}, #{receiptFileCosid}, 
			#{postScript}, #{writeOffRemainAmount},#{freezeFlag}, #{validFlag}, #{flag}, #{requestParam},
			#{responseParam}, #{responseCode}, #{createdBy}, #{sysCtime}, #{updatedBy}, 
			#{sysUtime})
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyPaymentRecord">
		insert into clms_replevy_payment_record
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				id,
			</if>
			<if test="reportNo != null" >
				report_no,
			</if>
			<if test="replevyNo != null" >
				replevy_no,
			</if>
			<if test="replevyTimes != null" >
				replevy_times,
			</if>
			<if test="caseTimes != null" >
				case_times,
			</if>
			<if test="requestType != null" >
				request_type,
			</if>
			<if test="businessNo != null" >
				business_no,
			</if>
			<if test="transDate != null" >
				trans_date,
			</if>
			<if test="directionType != null" >
				direction_type,
			</if>
			<if test="transAmount != null" >
				trans_amount,
			</if>
			<if test="transPurpose != null" >
				trans_purpose,
			</if>
			<if test="transAbstract != null" >
				trans_abstract,
			</if>
			<if test="paymentFlowno != null" >
				payment_flowNo,
			</if>
			<if test="banktransFlowNo != null" >
				banktrans_flow_no,
			</if>
			<if test="ourbankAccount != null" >
				ourBank_account,
			</if>
			<if test="partnerBankAccount != null" >
				partner_bank_account,
			</if>
			<if test="partnerBankName != null" >
				partner_bank_name,
			</if>
			<if test="partnerBankBranchName != null" >
				partner_bank_branch_name,
			</if>
			<if test="partnerBankAccountName != null" >
				partner_bank_account_name,
			</if>
			<if test="receiptFileCosurl != null" >
				receipt_file_cosurl,
			</if>
			<if test="receiptFileCosid != null" >
				receipt_file_cosid,
			</if>
			<if test="postScript != null" >
				post_script,
			</if>
			<if test="writeOffRemainAmount != null" >
				write_off_remain_amount,
			</if>
			<if test="freezeFlag != null" >
				freeze_flag,
			</if>
			<if test="validFlag != null" >
				valid_flag,
			</if>
			<if test="flag != null" >
				flag,
			</if>
			<if test="requestParam != null" >
				request_param,
			</if>
			<if test="responseParam != null" >
				response_param,
			</if>
			<if test="responseCode != null" >
				response_code,
			</if>
			<if test="createdBy != null" >
				created_by,
			</if>
			<if test="sysCtime != null" >
				sys_ctime,
			</if>
			<if test="updatedBy != null" >
				updated_by,
			</if>
			<if test="sysUtime != null" >
				sys_utime,
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="reportNo != null" >
				#{reportNo},
			</if>
			<if test="replevyNo != null" >
				#{replevyNo},
			</if>
			<if test="replevyTimes != null" >
				#{replevyTimes},
			</if>
			<if test="caseTimes != null" >
				#{caseTimes},
			</if>
			<if test="requestType != null" >
				#{requestType},
			</if>
			<if test="businessNo != null" >
				#{businessNo},
			</if>
			<if test="transDate != null" >
				#{transDate},
			</if>
			<if test="directionType != null" >
				#{directionType},
			</if>
			<if test="transAmount != null" >
				#{transAmount},
			</if>
			<if test="transPurpose != null" >
				#{transPurpose},
			</if>
			<if test="transAbstract != null" >
				#{transAbstract},
			</if>
			<if test="paymentFlowno != null" >
				#{paymentFlowno},
			</if>
			<if test="banktransFlowNo != null" >
				#{banktransFlowNo},
			</if>
			<if test="ourbankAccount != null" >
				#{ourbankAccount},
			</if>
			<if test="partnerBankAccount != null" >
				#{partnerBankAccount},
			</if>
			<if test="partnerBankName != null" >
				#{partnerBankName},
			</if>
			<if test="partnerBankBranchName != null" >
				#{partnerBankBranchName},
			</if>
			<if test="partnerBankAccountName != null" >
				#{partnerBankAccountName},
			</if>
			<if test="receiptFileCosurl != null" >
				#{receiptFileCosurl},
			</if>
			<if test="receiptFileCosid != null" >
				#{receiptFileCosid},
			</if>
			<if test="postScript != null" >
				#{postScript},
			</if>
			<if test="writeOffRemainAmount != null" >
				#{writeOffRemainAmount},
			</if>
			<if test="validFlag != null" >
				#{validFlag},
			</if>
			<if test="freezeFlag != null" >
				#{freezeFlag},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="requestParam != null" >
				#{requestParam},
			</if>
			<if test="responseParam != null" >
				#{responseParam},
			</if>
			<if test="responseCode != null" >
				#{responseCode},
			</if>
			<if test="createdBy != null" >
				#{createdBy},
			</if>
			<if test="sysCtime != null" >
				#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				#{sysUtime},
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyPaymentRecord">
		update clms_replevy_payment_record
		<set>
			<if test="reportNo != null" >
				report_no=#{reportNo},
			</if>
			<if test="replevyNo != null" >
				replevy_no=#{replevyNo},
			</if>
			<if test="replevyTimes != null" >
				replevy_times=#{replevyTimes},
			</if>
			<if test="caseTimes != null" >
				case_times = #{caseTimes},
			</if>
			<if test="requestType != null" >
				request_type=#{requestType},
			</if>
			<if test="businessNo != null" >
				business_no=#{businessNo},
			</if>
			<if test="transDate != null" >
				trans_date=#{transDate},
			</if>
			<if test="directionType != null" >
				direction_type=#{directionType},
			</if>
			<if test="transAmount != null" >
				trans_amount=#{transAmount},
			</if>
			<if test="transPurpose != null" >
				trans_purpose=#{transPurpose},
			</if>
			<if test="transAbstract != null" >
				trans_abstract=#{transAbstract},
			</if>
			<if test="paymentFlowno != null" >
				payment_flowNo=#{paymentFlowno},
			</if>
			<if test="banktransFlowNo != null" >
				banktrans_flow_no=#{banktransFlowNo},
			</if>
			<if test="ourbankAccount != null" >
				ourBank_account=#{ourbankAccount},
			</if>
			<if test="partnerBankAccount != null" >
				partner_bank_account=#{partnerBankAccount},
			</if>
			<if test="partnerBankName != null" >
				partner_bank_name=#{partnerBankName},
			</if>
			<if test="partnerBankBranchName != null" >
				partner_bank_branch_name=#{partnerBankBranchName},
			</if>
			<if test="partnerBankAccountName != null" >
				partner_bank_account_name=#{partnerBankAccountName},
			</if>
			<if test="receiptFileCosurl != null" >
				receipt_file_cosurl=#{receiptFileCosurl},
			</if>
			<if test="receiptFileCosid != null" >
				receipt_file_cosid=#{receiptFileCosid},
			</if>
			<if test="postScript != null" >
				post_script=#{postScript},
			</if>
			<if test="writeOffRemainAmount != null" >
				write_off_remain_amount=#{writeOffRemainAmount},
			</if>
			<if test="freezeFlag != null" >
				freeze_flag=#{freezeFlag},
			</if>
			<if test="validFlag != null" >
				valid_flag=#{validFlag},
			</if>
			<if test="flag != null" >
				flag=#{flag},
			</if>
			<if test="requestParam != null" >
				request_param=#{requestParam},
			</if>
			<if test="responseParam != null" >
				response_param=#{responseParam},
			</if>
			<if test="responseCode != null" >
				response_code=#{responseCode},
			</if>
			<if test="createdBy != null" >
				created_by=#{createdBy},
			</if>
			<if test="sysCtime != null" >
				sys_ctime=#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				updated_by=#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				sys_utime=#{sysUtime},
			</if>
		</set>
		where id = #{id}
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyPaymentRecord">
		update clms_replevy_payment_record
		set report_no=#{reportNo},
			replevy_no=#{replevyNo},
			replevy_times=#{replevyTimes},
	    	case_times=#{caseTimes},
			request_type=#{requestType},
			business_no=#{businessNo},
			trans_date=#{transDate},
			direction_type=#{directionType},
			trans_amount=#{transAmount},
			trans_purpose=#{transPurpose},
			trans_abstract=#{transAbstract},
			payment_flowNo=#{paymentFlowno},
			banktrans_flow_no=#{banktransFlowNo},
			ourBank_account=#{ourbankAccount},
			partner_bank_account=#{partnerBankAccount},
			partner_bank_name=#{partnerBankName},
			partner_bank_branch_name=#{partnerBankBranchName},
			partner_bank_account_name=#{partnerBankAccountName},
			receipt_file_cosurl=#{receiptFileCosurl},
			receipt_file_cosid=#{receiptFileCosid},
			post_script=#{postScript},
			write_off_remain_amount=#{writeOffRemainAmount},
		    freeze_flag=#{freezeFlag},
			valid_flag=#{validFlag},
			flag=#{flag},
			request_param=#{requestParam},
			response_param=#{responseParam},
			response_code=#{responseCode},
			created_by=#{createdBy},
			sys_ctime=#{sysCtime},
			updated_by=#{updatedBy},
			sys_utime=#{sysUtime}
		where id = #{id}
	</update>
	<select id="selectClmsReplevyPaymentRecord" resultType="com.paic.ncbs.claim.replevy.vo.ClmsReplevyPaymentRecordVo" parameterType="map">
		select
		<include refid="Base_Column_List" />
		from clms_replevy_payment_record
		where 1=1
		<if test="reportNo != null and reportNo!=''" >
			and	report_no=#{reportNo}
		</if>
		<if test="caseTimes != null" >
			and	case_times=#{caseTimes}
		</if>
		<if test="replevyNo != null and replevyNo!=''" >
			and	replevy_no=#{replevyNo}
		</if>
		<if test="replevyTimes != null" >
			and	replevy_times=#{replevyTimes}
		</if>
		<if test="flag != null and flag!=''" >
			and	flag=#{flag}
		</if>
		<if test="freezeFlag != null and freezeFlag!=''" >
			and	freeze_flag=#{freezeFlag}
		</if>
		and valid_flag='Y'
		order by sys_ctime desc
		limit 1
	</select>

</mapper>