<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.indicator.CaseIndicatorMapper">

    <select id="getUnSettleCaseList" resultType="com.paic.ncbs.claim.model.dto.indicator.SettleIndicatorDTO"
            parameterType="com.paic.ncbs.claim.model.dto.indicator.CaseIndicatorQueryDTO">
        SELECT
        cwcb.REPORT_NO AS "reportNo",
        cwcb.CASE_TIMES AS "caseTimes",
        cri.REPORT_DATE AS "reportDate",
        IFNULL( cwcb.END_CASE_DATE, #{nowDate}  ) AS "endCaseDate",
        IFNULL(
        (
        SELECT
        sum( TIMESTAMPDIFF( SECOND, t.CREATED_TIME, IFNULL( t.COMPLETE_TIME, #{nowDate}  ) ) )
        FROM
        clms_task_info t
        WHERE
        t.REPORT_NO = cwcb.REPORT_NO
        AND t.CASE_TIMES = cwcb.CASE_TIMES
        AND t.TASK_DEFINITION_BPM_KEY = 'OC_WAIT_CUSTOMER_SUPPLEMENTS'
        ),
        0
        ) AS "supplementSec"
        FROM
        clm_whole_case_base cwcb
        JOIN clm_report_info cri ON cwcb.REPORT_NO = cri.REPORT_NO
        AND cwcb.END_CASE_DATE IS NULL
        AND cwcb.CASE_TIMES &lt; 2
        WHERE
        1 = 1
        <if test="reportBeginDate != null and reportBeginDate != '' ">
            AND cri.REPORT_DATE>=#{reportBeginDate}
        </if>
        <if test="reportEndDate != null and reportEndDate != '' ">
            AND cri.REPORT_DATE&lt;#{reportEndDate}
        </if>
        <if test="reportNo != null and reportNo != '' ">
            AND cwcb.REPORT_NO = #{reportNo}
        </if>
        <if test="caseTimes != null and caseTimes != '' ">
            AND cwcb.CASE_TIMES = #{caseTimes}
        </if>
    </select>

    <select id="getSettleCaseList" resultType="com.paic.ncbs.claim.model.dto.indicator.SettleIndicatorDTO"
            parameterType="com.paic.ncbs.claim.model.dto.indicator.CaseIndicatorQueryDTO">
        SELECT
        cwcb.REPORT_NO AS "reportNo",
        cwcb.CASE_TIMES AS "caseTimes",
        cri.REPORT_DATE AS "reportDate",
        IFNULL( cwcb.END_CASE_DATE, #{nowDate}  ) AS "endCaseDate",
        IFNULL(
        (
        SELECT
        sum( TIMESTAMPDIFF( SECOND, t.CREATED_TIME, IFNULL( t.COMPLETE_TIME, #{nowDate}  ) ) )
        FROM
        clms_task_info t
        WHERE
        t.REPORT_NO = cwcb.REPORT_NO
        AND t.CASE_TIMES = cwcb.CASE_TIMES
        AND t.TASK_DEFINITION_BPM_KEY = 'OC_WAIT_CUSTOMER_SUPPLEMENTS'
        ),
        0
        ) AS "supplementSec"
        FROM
        clm_whole_case_base cwcb
        JOIN clm_report_info cri ON cwcb.REPORT_NO = cri.REPORT_NO
        AND cwcb.END_CASE_DATE IS NOT NULL
        AND cwcb.CASE_TIMES &lt; 2
        WHERE
        1 = 1
        <if test="reportBeginDate != null and reportBeginDate != '' ">
            AND cri.REPORT_DATE>=#{reportBeginDate}
        </if>
        <if test="reportEndDate != null and reportEndDate != '' ">
            AND cri.REPORT_DATE &lt;#{reportEndDate}
        </if>
        <if test="reportNo != null and reportNo != '' ">
            AND cwcb.REPORT_NO = #{reportNo}
        </if>
        <if test="caseTimes != null and caseTimes != '' ">
            AND cwcb.CASE_TIMES = #{caseTimes}
        </if>
        <if test="endCaseBeginDate != null and endCaseBeginDate != '' ">
            AND cwcb.END_CASE_DATE>=#{endCaseBeginDate}
        </if>
        <if test="endCaseEndDate != null and endCaseEndDate != '' ">
            AND cwcb.END_CASE_DATE &lt;#{endCaseEndDate}
        </if>
    </select>

    <select id="getUnRegCaseList" resultType="com.paic.ncbs.claim.model.dto.indicator.RegistrationIndicatorDTO"
            parameterType="com.paic.ncbs.claim.model.dto.indicator.CaseIndicatorQueryDTO">
        SELECT
        cwcb.REPORT_NO AS "reportNo",
        cwcb.CASE_TIMES AS "caseTimes",
        cri.REPORT_DATE AS "reportDate",
        IFNULL( cwcb.REGISTER_DATE, #{nowDate}  ) AS "registerDate"
        FROM
        clm_whole_case_base cwcb
        JOIN clm_report_info cri ON cwcb.REPORT_NO = cri.REPORT_NO
        AND cwcb.REGISTER_DATE IS NULL
        AND cwcb.CASE_TIMES &lt; 2
        WHERE
        1 = 1
        <if test="reportBeginDate != null and reportBeginDate != '' ">
            AND cri.REPORT_DATE>=#{reportBeginDate}
        </if>
        <if test="reportEndDate != null and reportEndDate != '' ">
            AND cri.REPORT_DATE&lt;#{reportEndDate}
        </if>
        <if test="reportNo != null and reportNo != '' ">
            AND cwcb.REPORT_NO = #{reportNo}
        </if>
        <if test="caseTimes != null and caseTimes != '' ">
            AND cwcb.CASE_TIMES = #{caseTimes}
        </if>
    </select>

    <select id="getRegCaseList" resultType="com.paic.ncbs.claim.model.dto.indicator.RegistrationIndicatorDTO"
            parameterType="com.paic.ncbs.claim.model.dto.indicator.CaseIndicatorQueryDTO">
        SELECT
        cwcb.REPORT_NO AS "reportNo",
        cwcb.CASE_TIMES AS "caseTimes",
        cri.REPORT_DATE AS "reportDate",
        IFNULL( cwcb.REGISTER_DATE, #{nowDate}  ) AS "registerDate"
        FROM
        clm_whole_case_base cwcb
        JOIN clm_report_info cri ON cwcb.REPORT_NO = cri.REPORT_NO
        AND cwcb.REGISTER_DATE IS NOT NULL
        AND cwcb.CASE_TIMES &lt; 2
        WHERE
        1 = 1
        <if test="reportBeginDate != null and reportBeginDate != '' ">
            AND cri.REPORT_DATE>=#{reportBeginDate}
        </if>
        <if test="reportEndDate != null and reportEndDate != '' ">
            AND cri.REPORT_DATE&lt;=#{reportEndDate}
        </if>
        <if test="reportNo != null and reportNo != '' ">
            AND cwcb.REPORT_NO = #{reportNo}
        </if>
        <if test="caseTimes != null and caseTimes != '' ">
            AND cwcb.CASE_TIMES = #{caseTimes}
        </if>
        <if test="registerBeginDate != null and registerBeginDate != '' ">
            AND cwcb.REGISTER_DATE>=#{registerBeginDate}
        </if>
        <if test="registerEndDate != null and registerEndDate != '' ">
            AND cwcb.REGISTER_DATE&lt;#{registerEndDate}
        </if>
    </select>

    <select id="getReopenCaseList" resultType="com.paic.ncbs.claim.model.dto.indicator.ReopenIndicatorDTO"
            parameterType="com.paic.ncbs.claim.model.dto.indicator.CaseIndicatorQueryDTO">
        SELECT
        cwcb.REPORT_NO AS "reportNo",
        cwcb.CASE_TIMES AS "caseTimes",
        IFNULL(
        (
        SELECT
        crcr.UPDATED_DATE
        FROM
        clm_restart_case_record crcr
        WHERE
        crcr.REPORT_NO = cwcb.REPORT_NO
        AND crcr.CASE_TIMES = cwcb.CASE_TIMES - 1
        AND crcr.APPROVAL_OPINIONS = '0'
        ),
        cwcb.CREATED_DATE
        ) AS "reopenApproveDate",
        IFNULL( cwcb.END_CASE_DATE, #{nowDate} ) AS "endCaseDate",
        IFNULL(
        (
        SELECT
        sum( TIMESTAMPDIFF( SECOND, t.CREATED_TIME, IFNULL( t.COMPLETE_TIME, #{nowDate} ) ) )
        FROM
        clms_task_info t
        WHERE
        t.REPORT_NO = cwcb.REPORT_NO
        AND t.CASE_TIMES = cwcb.CASE_TIMES
        AND t.TASK_DEFINITION_BPM_KEY = 'OC_WAIT_CUSTOMER_SUPPLEMENTS'
        ),
        0
        ) AS "supplementSec"
        FROM
        clm_whole_case_base cwcb
        WHERE
        cwcb.CASE_TIMES > 1
        <if test="reportNo != null and reportNo != '' ">
            AND cwcb.REPORT_NO = #{reportNo}
        </if>
        <if test="caseTimes != null and caseTimes != '' ">
            AND cwcb.CASE_TIMES = #{caseTimes}
        </if>
    </select>
    <select id="selectInvestigateReportNo" resultType="java.lang.String">
        select
        ci.report_no
        from clms_case_indicator cci, clms_investigate ci
        where cci.id_linked = ci.ID_AHCS_INVESTIGATE
        and ci.server_code = #{serverCode}
        and cci.indicator_code = #{indicatorCode}
        and cci.indicator_value>#{seconds}
        and value_stable='0' and ci.INIT_MODE = '02'
    </select>
</mapper>