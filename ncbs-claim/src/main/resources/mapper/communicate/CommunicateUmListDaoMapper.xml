<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.communicate.CommunicateUmListMapper">

	<resultMap type="com.paic.ncbs.claim.model.dto.communicate.CommunicateUmListDTO" id="CommunicateUmListDTOMap">
		<id property="idAhcsCommunicateUmList" column="ID_AHCS_COMMUNICATE_UM_LIST" />
		<result property="departmentCode" column="DEPARTMENT_CODE" />
		<result property="departmentName" column="DEPARTMENT_NAME" />
		<result property="communicateUm" column="COMMUNICATE_UM" />
		<result property="communicateName" column="COMMUNICATE_NAME" />
		<result property="departmentLiaisonArr" column="DEPARTMENT_LIAISON_ARR" />	
		<result property="status" column="STATUS" />
	</resultMap>

	<select id="getCommunicateUmList" resultMap="CommunicateUmListDTOMap">
		SELECT distinct T.ID_AHCS_COMMUNICATE_UM_LIST,
		       T.DEPARTMENT_CODE,
		       T.DEPARTMENT_NAME,
		       T.COMMUNICATE_UM,
		       T.COMMUNICATE_NAME,
		       T.STATUS
		FROM   CLMS_COMMUNICATE_UM_LIST T
		WHERE  T.DEPARTMENT_CODE =  #{departmentCodeAndName,jdbcType=VARCHAR}
		OR     T.DEPARTMENT_NAME LIKE CONCAT('%',#{departmentCodeAndName},'%')
		AND    T.STATUS = 'Y'
	</select>
	
	<select id="getCommunicateUmListByDeptCode" resultType="String">
		SELECT T.COMMUNICATE_UM
		FROM   CLMS_COMMUNICATE_UM_LIST T
		WHERE  T.DEPARTMENT_CODE =  #{departmentCode,jdbcType=VARCHAR}
		AND    T.STATUS = 'Y'
	</select>

	<select id="getCommunicateUmListByParam" resultType="String" parameterType="com.paic.ncbs.claim.model.dto.communicate.CommunicateUmListDTO">
		SELECT T.COMMUNICATE_UM
		FROM   CLMS_COMMUNICATE_UM_LIST T
		WHERE  T.STATUS = 'Y' and COMMUNICATE_UM is not null
		<if test="departmentCode!=null and ''!= departmentCode ">
			and t.department_code = #{departmentCode,jdbcType=VARCHAR}
		</if>

	</select>

</mapper>