<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.paic.ncbs.claim.dao.mapper.communicate.CommunicateDetailMapper">

	<resultMap type="com.paic.ncbs.claim.model.dto.communicate.CommunicateDetailDTO"
		id="CommunicateDetailDTOMap">
		<id property="idAhcsCommunicateDetail" column="ID_AHCS_COMMUNICATE_DETAIL" />
		<result property="idAhcsCommunicateBase" column="ID_AHCS_COMMUNICATE_BASE" />
		<result property="initiatorUm" column="INITIATOR_UM" />
		<result property="initiatDate" column="INITIAT_DATE" />
		<result property="dealUm" column="DEAL_UM" />
		<result property="dealDate" column="DEAL_DATE" />
		<result property="role" column="ROLE" />
		<result property="communicateContent" column="COMMUNICATE_CONTENT" />
		<result property="taskStatus" column="TASK_STATUS" />
		<result property="reportNo" column="REPORT_NO" />
		<result property="createdBy" column="CREATED_BY" />
		<result property="payAmount" column="PAY_AMOUNT" />
	</resultMap>
	

	<resultMap type="java.util.Map" id="UserCommunicateTaskMap">
		<result property="communicationNode" column="COMMUNICATE_LINK" />
		<result property="initiatorUm" column="INITIATOR_UM" />
		<result property="initiatDate" column="INITIAT_DATE" />
		<result property="communicateTitle" column="COMMUNICATE_TITLE" />
		<result property="idAhcsCommunicateBase" column="ID_AHCS_COMMUNICATE_BASE" />
		<result property="reportNo" column="REPORT_NO" />
    	<result property="createTime" column="CREATE_TIME" />
		<result property="insuredName" column="INSURED_NAME" />
		<result property="caseTimes" column="CASE_TIMES" />
		<result property="taskDefinitionKey" column="TASK_DEFINITION_KEY" />
		<result property="taskStatus" column="TASK_STATUS" />
		<result property="claimTime" column="CLAIM_TIME" />
		<result property="caseExpedited" column="CASE_EXPEDITED" />
	</resultMap>


	<select id="getCommunicateDetailDTOListById" resultMap="CommunicateDetailDTOMap">
		SELECT
			created_by,
			created_date,
			updated_by,
			updated_date,
			id_ahcs_communicate_detail,
			id_ahcs_communicate_base,
			initiator_um,
			initiat_date,
			deal_um,
			deal_date,
			ROLE,
			communicate_content,
			task_status,
			PAY_AMOUNT
		FROM 
			clms_communicate_detail
		WHERE 
			id_ahcs_communicate_base = #{idAhcsCommunicateBase,jdbcType=VARCHAR} ORDER BY deal_date asc
	</select>
	
	<select id="getDealUmListById" resultType="java.lang.String">
		SELECT T.DEAL_UM
		  FROM CLMS_COMMUNICATE_DETAIL T
		 WHERE T.ID_AHCS_COMMUNICATE_BASE = #{idAhcsCommunicateBase,jdbcType=VARCHAR}
		   AND T.TASK_STATUS = '0'
		   AND T.ROLE = '0'
	</select>

	<update id="batchUpdateCommunicateDetailDao" parameterType="java.util.List">
		<foreach collection="updateCommunicateDetailDTOList" index="index" item="communicateDetail" open="" separator=";" close=";">
			UPDATE CLMS_COMMUNICATE_DETAIL
				SET UPDATED_BY = #{communicateDetail.updatedBy,jdbcType=VARCHAR},
				UPDATED_DATE = now(),
				DEAL_DATE = now(),
				COMMUNICATE_CONTENT = #{communicateDetail.communicateContent,jdbcType=VARCHAR},
				TASK_STATUS = #{communicateDetail.taskStatus,jdbcType=VARCHAR},
				DEAL_UM = #{communicateDetail.dealUm,jdbcType=VARCHAR},
			    PAY_AMOUNT = #{communicateDetail.payAmount,jdbcType=DECIMAL}
			WHERE 
				ID_AHCS_COMMUNICATE_BASE = #{communicateDetail.idAhcsCommunicateBase,jdbcType=VARCHAR}
			AND ID_AHCS_COMMUNICATE_DETAIL = #{communicateDetail.idAhcsCommunicateDetail,jdbcType=VARCHAR}
		</foreach>   
	</update>


	<insert id="batchInsertCommunicateDetailDTO" parameterType="java.util.List">
		INSERT INTO CLMS_communicate_detail
		(
		created_by,
		created_date,
		updated_by,
		updated_date,
		ID_AHCS_COMMUNICATE_DETAIL,
		id_ahcs_communicate_base,
		initiator_um,
		initiat_date,
		deal_um,
		deal_date,
		ROLE,
		communicate_content,
		task_status,
		archive_time)
		<foreach collection="list" item="communicateDetailDTO" index="index" separator=" union all ">
			SELECT
			#{communicateDetailDTO.createdBy,jdbcType=VARCHAR},
			now(),
			#{communicateDetailDTO.updatedBy,jdbcType=VARCHAR},
			now(),
			left(hex(uuid()),32),
			#{communicateDetailDTO.idAhcsCommunicateBase,jdbcType=VARCHAR},
			#{communicateDetailDTO.initiatorUm,jdbcType=VARCHAR},
			#{communicateDetailDTO.initiatDate,jdbcType=TIMESTAMP},
			#{communicateDetailDTO.dealUm,jdbcType=VARCHAR},
			#{communicateDetailDTO.dealDate,jdbcType=TIMESTAMP},
			#{communicateDetailDTO.role,jdbcType=VARCHAR},
			#{communicateDetailDTO.communicateContent,jdbcType=VARCHAR},
			#{communicateDetailDTO.taskStatus,jdbcType=VARCHAR},
			now()
		</foreach>
	</insert>


	<insert id="insertCommunicateDetailDTO" parameterType="com.paic.ncbs.claim.model.dto.communicate.CommunicateDetailDTO">
		INSERT INTO CLMS_communicate_detail
		(
			created_by,
			created_date,
			updated_by,
			updated_date,
		    ID_AHCS_COMMUNICATE_DETAIL,
			id_ahcs_communicate_base,
			initiator_um,
			initiat_date,
			deal_um,
			deal_date,
			ROLE,
			communicate_content,
			task_status,
			ARCHIVE_TIME
		)
		VALUES (
			#{createdBy,jdbcType=VARCHAR},
			now(),
			#{updatedBy,jdbcType=VARCHAR},
			now(),
			left(hex(uuid()),32),
			#{idAhcsCommunicateBase,jdbcType=VARCHAR},
			#{initiatorUm,jdbcType=VARCHAR},
			#{initiatDate,jdbcType=TIMESTAMP},
			#{dealUm,jdbcType=VARCHAR},
			#{dealDate,jdbcType=TIMESTAMP},
			#{role,jdbcType=VARCHAR},
			#{communicateContent,jdbcType=VARCHAR},
			#{taskStatus,jdbcType=VARCHAR},
			now()
		)
	</insert>



	<select id="getMaxCommunicateDetailById" resultType="java.lang.String">
		SELECT
			date_format(MAX(A.DEAL_DATE), '%Y-%m-%d %H:%i:%s') dealDate
		FROM
			CLMS_COMMUNICATE_DETAIL A
		WHERE 
			A.ID_AHCS_COMMUNICATE_BASE = #{idAhcsCommunicateBase,jdbcType=VARCHAR}
	</select>


	<select id="getCommunicateDetailDTOByIdAndDealUm" resultMap="CommunicateDetailDTOMap">
		SELECT T.ID_AHCS_COMMUNICATE_DETAIL,
			T.INITIATOR_UM,
			T.INITIAT_DATE ,
			T.DEAL_UM ,
			T.DEAL_DATE ,
			T.ROLE ,
			T.COMMUNICATE_CONTENT ,
			T.TASK_STATUS ,
			T.ID_AHCS_COMMUNICATE_BASE ,
		    T.CREATED_BY
			FROM CLMS_COMMUNICATE_DETAIL T
		WHERE 
			T.ID_AHCS_COMMUNICATE_BASE = #{idAhcsCommunicateBase,jdbcType=VARCHAR}
		AND T.DEAL_UM = #{dealUm,jdbcType=VARCHAR}
		AND T.TASK_STATUS = '0'
		ORDER BY t.deal_date desc
	</select>
	
	<select id="getUserCommunicateTaskTotal" resultType="java.lang.Integer">
		SELECT COUNT(1)
		  FROM CLMS_COMMUNICATE_DETAIL B, CLMS_COMMUNICATE_BASE A
		 WHERE A.ID_AHCS_COMMUNICATE_BASE = B.ID_AHCS_COMMUNICATE_BASE
		   AND B.TASK_STATUS = '0'
		   AND B.DEAL_UM = #{userId,jdbcType=VARCHAR}
		   <if test="reportNo != null and reportNo != '' ">
		       AND A.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		   </if>
	</select>

	<select id="getCommunicateTaskStatus" resultType="java.lang.String">
        SELECT group_concat(distinct b.task_status) FROM CLMS_COMMUNICATE_DETAIL B, CLMS_COMMUNICATE_BASE A
        WHERE A.ID_AHCS_COMMUNICATE_BASE = B.ID_AHCS_COMMUNICATE_BASE AND A.REPORT_NO =  #{reportNo,jdbcType=VARCHAR}
	</select>


	<select id="getUserCommunicateTaskData" resultMap="UserCommunicateTaskMap">
		SELECT A.REPORT_NO REPORT_NO,
                      (SELECT C.NAME
                         FROM CLMS_POLICY_INFO D, CLMS_insured_person C
                        WHERE D.ID_AHCS_POLICY_INFO = C.ID_AHCS_POLICY_INFO
                          AND D.REPORT_NO = A.REPORT_NO
                          limit 1) INSURED_NAME,
                      (SELECT P.VALUE_CHINESE_NAME
                         FROM CLM_COMMON_PARAMETER P
                        WHERE P.COLLECTION_CODE = 'AHCS_GTZT'
                          AND P.VALUE_CODE = A.COMMUNICATE_TITLE
                          limit 1) COMMUNICATE_TITLE,
		              A.INITIATOR_UM INITIATOR_UM,
                      (SELECT P.VALUE_CHINESE_NAME
                         FROM CLM_COMMON_PARAMETER P
                        WHERE P.COLLECTION_CODE = 'AHCS_GTHJ'
                          AND P.VALUE_CODE = A.COMMUNICATE_LINK
                          limit 1) COMMUNICATE_LINK,
                      date_format(A.INITIAT_DATE, '%Y-%m-%d %H:%i:%s') INITIAT_DATE,
                      date_format(A.CASE_TIMES) CASE_TIMES,
                      A.ID_AHCS_COMMUNICATE_BASE ID_AHCS_COMMUNICATE_BASE,
                      date_format(B.CREATED_DATE, '%Y-%m-%d %H:%i:%s') CREATE_TIME,
                      'AHCS_COMMUNICATE' TASK_DEFINITION_KEY,
                      (CASE A.COMMUNICATE_STATUS WHEN '0' THEN '待处理'  WHEN '1' THEN '处理中' ELSE '处理完成' END) TASK_STATUS,
					  date_format(B.INITIAT_DATE, '%Y-%m-%d %H:%i:%s') CLAIM_TIME,
					  (select TI.CASE_EXPEDITED
			          from CLMS_TASK_INFO TI
			         WHERE TI.TASK_ID = A.ID_AHCS_COMMUNICATE_BASE
			           AND TI.TASK_DEFINITION_BPM_KEY = 'AHCS_COMMUNICATE'
			           AND TI.ASSIGNER = #{userId,jdbcType=VARCHAR}
			           AND TI.STATUS = '0'
			           limit 1) CASE_EXPEDITED
                FROM CLMS_COMMUNICATE_DETAIL B, CLMS_COMMUNICATE_BASE A
               WHERE A.ID_AHCS_COMMUNICATE_BASE = B.ID_AHCS_COMMUNICATE_BASE
                 AND B.TASK_STATUS = '1'
                 AND A.COMMUNICATE_STATUS != '2'
                 AND B.ROLE = '1'
		   		 <if test="reportNo != null and reportNo != '' ">
                 	AND A.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
                 </if>
               ORDER BY B.CREATED_DATE DESC
	</select>


	<update id="updateCommunicateDetailDTO"
		parameterType="com.paic.ncbs.claim.model.dto.communicate.CommunicateDetailDTO">
		UPDATE CLMS_COMMUNICATE_DETAIL T
		SET T.UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
		T.UPDATED_DATE = now(),
		T.DEAL_DATE = now(),
		T.COMMUNICATE_CONTENT = #{communicateContent,jdbcType=VARCHAR},
		T.TASK_STATUS = #{taskStatus,jdbcType=VARCHAR},
		T.DEAL_UM = #{dealUm,jdbcType=VARCHAR}
		WHERE T.ID_AHCS_COMMUNICATE_DETAIL =
		#{idAhcsCommunicateDetail,jdbcType=VARCHAR}
		AND T.ID_AHCS_COMMUNICATE_BASE =
		#{idAhcsCommunicateBase,jdbcType=VARCHAR}
	</update>
	

	<select id="getCommunicateDetailDTOLastDealUm" resultType="java.lang.Integer">
		SELECT COUNT(1) taskCount
		FROM CLMS_COMMUNICATE_DETAIL T
		WHERE T.ID_AHCS_COMMUNICATE_BASE =
		#{idAhcsCommunicateBase,jdbcType=VARCHAR}
		AND T.ROLE = '0'
		AND T.TASK_STATUS = '0'
	</select>
	

	<select id="getCommunicateTaskListByUm" resultMap="CommunicateDetailDTOMap">
		  SELECT B.INITIAT_DATE, B.ID_AHCS_COMMUNICATE_DETAIL,B.TASK_STATUS,A.REPORT_NO
              FROM CLMS_COMMUNICATE_DETAIL B,CLMS_COMMUNICATE_BASE A
             WHERE A.ID_AHCS_COMMUNICATE_BASE = B.ID_AHCS_COMMUNICATE_BASE  
               AND B.DEAL_UM = #{senderUm}
               AND B.ROLE = '0'
	</select>
	
	<select id="getCommunicateTaskListBySysDate" resultMap="CommunicateDetailDTOMap">
		   SELECT B.INITIAT_DATE,B.ID_AHCS_COMMUNICATE_DETAIL,B.TASK_STATUS
             FROM CLMS_COMMUNICATE_DETAIL B
            WHERE B.DEAL_UM = #{senderUm}
              AND B.ROLE = '0'
              AND B.INITIAT_DATE >= str_to_date(date_format(now(), '%Y-%m-%d'), '%Y-%m-%d')
	</select>

	<update id="updateAccepterUMInfo">
    	UPDATE CLMS_COMMUNICATE_DETAIL T
	    SET    T.DEAL_UM =#{accepterUm,jdbcType=VARCHAR},
	           T.UPDATED_DATE = now()
	    WHERE  T.ID_AHCS_COMMUNICATE_DETAIL IN
	           (SELECT TD.ID_AHCS_COMMUNICATE_DETAIL
	            FROM   CLMS_COMMUNICATE_DETAIL TD, CLMS_COMMUNICATE_BASE TT
	            WHERE  TD.ID_AHCS_COMMUNICATE_BASE = TT.ID_AHCS_COMMUNICATE_BASE
	            AND    TT.COMMUNICATE_STATUS != '2'
	            AND    TD.DEAL_UM = #{senderUm,jdbcType=VARCHAR}
	            AND    TD.TASK_STATUS = '0') 
	    AND T.DEAL_UM = #{senderUm,jdbcType=VARCHAR}
	</update>
	


	<select id="getCommunicateDetailDTOList" resultMap="CommunicateDetailDTOMap">
		SELECT 
			T.ID_AHCS_COMMUNICATE_DETAIL,
			T.INITIATOR_UM,
			T.INITIAT_DATE ,
			T.DEAL_UM ,
			T.DEAL_DATE ,
			T.ROLE ,
			T.COMMUNICATE_CONTENT ,
			T.TASK_STATUS ,
			T.ID_AHCS_COMMUNICATE_BASE
		FROM   CLMS_COMMUNICATE_DETAIL T
		WHERE  T.ID_AHCS_COMMUNICATE_BASE = #{idAhcsCommunicateBase,jdbcType=VARCHAR}
		AND    T.ROLE = '0'
		AND    T.TASK_STATUS = '0'
	</select>
	

	<select id="checkInitiatorUmForDealUm" resultType="java.lang.Integer">
		SELECT COUNT(1)
		FROM   CLMS_COMMUNICATE_DETAIL T
		WHERE  T.ID_AHCS_COMMUNICATE_BASE = #{idAhcsCommunicateBase,jdbcType=VARCHAR}
		AND    T.DEAL_UM = #{userId,jdbcType=VARCHAR}
		AND    T.ROLE = '0'
	</select>
	

	<select id="checkDealUnForInitiatorUm" resultType="java.lang.Integer">
		SELECT COUNT(1)
		FROM   CLMS_COMMUNICATE_DETAIL T
		WHERE  T.ID_AHCS_COMMUNICATE_BASE = #{idAhcsCommunicateBase,jdbcType=VARCHAR}
		AND    T.INITIATOR_UM IN
		<foreach item="item" index="index" collection="dealUmList" open="(" separator="," close=")">
			#{item,jdbcType=VARCHAR}
		</foreach>
		AND T.ROLE = '1'
	</select>
	
	
	<delete id="deleteCommunicateDetailById">
		DELETE FROM CLMS_COMMUNICATE_DETAIL T WHERE T.ID_AHCS_COMMUNICATE_BASE IN
		<foreach item="idAhcsCommunicateBase" index="index" collection="deleteCommunicateBaseIdList" open="(" separator="," close=")">
			#{idAhcsCommunicateBase,jdbcType=VARCHAR}
		</foreach>		
	</delete>


	<update id="updateAssignInfoForCommunicate" parameterType="com.paic.ncbs.claim.model.dto.communicate.CommunicateDetailDTO">
		update CLMS_communicate_detail t
		   set t.deal_um = #{dealUm,jdbcType=VARCHAR},
				t.updated_by = #{updatedBy,jdbcType=VARCHAR},
				t.updated_date = now()
		 where t.id_ahcs_communicate_base = #{idAhcsCommunicateBase,jdbcType=VARCHAR}
		   and t.task_status = '0'
	</update>

	
</mapper>