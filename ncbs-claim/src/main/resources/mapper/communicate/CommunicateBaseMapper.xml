<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.communicate.CommunicateBaseMapper">

	<resultMap type="com.paic.ncbs.claim.model.dto.communicate.CommunicateBaseDTO" id="CommunicateBaseVOMap">
		<id property="idAhcsCommunicateBase" column="ID_AHCS_COMMUNICATE_BASE" />
		<result property="reportNo" column="REPORT_NO" />
		<result property="caseTimes" column="CASE_TIMES" />
		<result property="communicateTitle" column="COMMUNICATE_TITLE" />
		<result property="communicateLink" column="COMMUNICATE_LINK" />
		<result property="initiatorUm" column="INITIATOR_UM" />
		<result property="initiatDate" column="INITIAT_DATE" />
		<result property="finishDate" column="FINISH_DATE" />
		<result property="communicateStatus" column="COMMUNICATE_STATUS" />
		<result property="taskInstId" column="TASK_INST_ID" />
		<result property="communicateFrom" column="COMMUNICATE_FROM" />
		<result property="isHangup" column="IS_HANGUP"/>
		<result property="problemCode" column="PROBLEM_CODE"/>

		<collection property="communicateDetailDTOList"
					ofType="com.paic.ncbs.claim.model.dto.communicate.CommunicateDetailDTO"

					select="com.paic.ncbs.claim.dao.mapper.communicate.CommunicateDetailMapper.getCommunicateDetailDTOListById"
					column="{idAhcsCommunicateBase=ID_AHCS_COMMUNICATE_BASE}">
		</collection>
	</resultMap>


	<resultMap type="com.paic.ncbs.claim.model.dto.communicate.CommunicateBaseDTO" id="CommunicateBaseVO">
		<id property="idAhcsCommunicateBase" column="ID_AHCS_COMMUNICATE_BASE" />
		<result property="reportNo" column="REPORT_NO" />
		<result property="caseTimes" column="CASE_TIMES" />
		<result property="communicateTitle" column="COMMUNICATE_TITLE" />
		<result property="communicateLink" column="COMMUNICATE_LINK" />
		<result property="initiatorUm" column="INITIATOR_UM" />
		<result property="initiatDate" column="INITIAT_DATE" />
		<result property="finishDate" column="FINISH_DATE" />
		<result property="communicateStatus" column="COMMUNICATE_STATUS" />
		<result property="taskInstId" column="TASK_INST_ID" />
		<result property="communicateFrom" column="COMMUNICATE_FROM" />
		<result property="isHangup" column="IS_HANGUP"/>
	</resultMap>

	<resultMap type="java.util.Map" id="CommunicatePersonMap">
		<result property="communicateUm" column="COMMUNICATE_UM" />
		<result property="communicateName" column="COMMUNICATE_NAME" />
	</resultMap>

	<select id="getCommunicateBaseById" resultMap="CommunicateBaseVOMap">
		SELECT created_by,
		created_date,
		updated_by,
		updated_date,
		id_ahcs_communicate_base,
		report_no,
		case_times,
		communicate_title,
		communicate_link,
		initiator_um,
		initiat_date,
		finish_date,
		communicate_status,
		task_inst_id,
		communicate_from,
		is_hangup
		FROM   CLMS_communicate_base
		WHERE  id_ahcs_communicate_base = #{idAhcsCommunicateBase,jdbcType=VARCHAR}
	</select>

	<select id="getHistoryCommunicateBaseList" resultMap="CommunicateBaseVO">
		SELECT id_ahcs_communicate_base,
			   report_no,
			   case_times,
			   communicate_title,
			   communicate_link,
			   initiator_um,
			   initiat_date,
			   finish_date,
			   communicate_status,
			   task_inst_id,
			   communicate_from,
			   is_hangup
		FROM   CLMS_communicate_base
		WHERE
			report_no = #{reportNo,jdbcType=VARCHAR}
<!--		  AND CASE_TIMES = #{caseTimes, jdbcType = NUMERIC}-->
		ORDER BY updated_date ASC
	</select>

	<select id="getNotCommunicateBaseDTO" resultMap="CommunicateBaseVO">
		SELECT
		T.ID_AHCS_COMMUNICATE_BASE,
		T.REPORT_NO,
		T.CASE_TIMES,
		T.COMMUNICATE_TITLE,
		T.COMMUNICATE_LINK,
		T.INITIATOR_UM,
		T.COMMUNICATE_STATUS,
		T.TASK_INST_ID,
		T.COMMUNICATE_FROM ,
		T.IS_HANGUP
		FROM CLMS_COMMUNICATE_BASE T, CLMS_COMMUNICATE_DETAIL TT
		WHERE  tt.id_ahcs_communicate_base = t.id_ahcs_communicate_base
		AND T.REPORT_NO = #{reportNo, jdbcType = VARCHAR}
		AND T.CASE_TIMES = #{caseTimes, jdbcType = NUMERIC}
		AND T.COMMUNICATE_LINK = #{communicateLink,jdbcType =VARCHAR}
		AND T.COMMUNICATE_TITLE = #{communicateTitle, jdbcType =VARCHAR}
		AND T.COMMUNICATE_STATUS IN ('0', '1')
		AND TT.DEAL_UM IN
		<foreach item="item" index="index" collection="assignerList" open="(" separator="," close=")">
			#{item,jdbcType=VARCHAR}
		</foreach>
		AND    TT.TASK_STATUS = '0'
		AND    TT.ROLE = '0'
		limit 1
	</select>


	<insert id="insertCommunicateBaseDTO" parameterType="com.paic.ncbs.claim.model.dto.communicate.CommunicateBaseDTO">
		<selectKey keyProperty="idAhcsCommunicateBase" keyColumn="ID_AHCS_COMMUNICATE_BASE" resultType="java.lang.String" order="BEFORE">
			SELECT REPLACE(UUID(),'-','') FROM dual
		</selectKey>
		INSERT INTO CLMS_communicate_base
		(
		CREATED_BY,
		CREATED_DATE,
		UPDATED_BY,
		UPDATED_DATE,
		ID_AHCS_COMMUNICATE_BASE,
		REPORT_NO,
		CASE_TIMES,
		COMMUNICATE_TITLE,
		COMMUNICATE_LINK,
		INITIATOR_UM,
		INITIAT_DATE,
		FINISH_DATE,
		COMMUNICATE_STATUS,
		TASK_INST_ID,
		COMMUNICATE_FROM,
		IS_HANGUP,
		ARCHIVE_TIME
		)
		VALUES (
		#{createdBy,jdbcType=VARCHAR},
		NOW(),
		#{updatedBy,jdbcType=VARCHAR},
		NOW(),
		#{idAhcsCommunicateBase,jdbcType=VARCHAR},
		#{reportNo,jdbcType=VARCHAR},
		#{caseTimes,jdbcType=NUMERIC},
		#{communicateTitle,jdbcType=VARCHAR},
		#{communicateLink,jdbcType=VARCHAR},
		#{initiatorUm,jdbcType=VARCHAR},
		#{initiatDate,jdbcType=TIMESTAMP},
		#{finishDate,jdbcType=TIMESTAMP},
		#{communicateStatus,jdbcType=VARCHAR},
		#{taskInstId,jdbcType=VARCHAR},
		#{communicateFrom,jdbcType=VARCHAR},
		#{isHangup,jdbcType=VARCHAR},
		now()
		)
	</insert>

	<update id="updateCommunicateBaseInfo" parameterType="com.paic.ncbs.claim.model.dto.communicate.CommunicateBaseDTO">
		UPDATE CLMS_COMMUNICATE_BASE T SET
		T.UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
		T.COMMUNICATE_STATUS = #{communicateStatus,jdbcType=VARCHAR},
		<if test="finishDate != null">
			T.FINISH_DATE = #{finishDate,jdbcType=TIMESTAMP},
		</if>
		T.UPDATED_DATE = now()
		WHERE T.ID_AHCS_COMMUNICATE_BASE = #{idAhcsCommunicateBase,jdbcType=VARCHAR}
	</update>


	<select id="getFreeAppraisalCommunicate" resultMap="CommunicateBaseVO">
		SELECT id_ahcs_communicate_base, 
		       report_no, 
		       case_times, 
		       communicate_title, 
		       communicate_link, 
		       initiator_um, 
		       initiat_date, 
		       finish_date, 
		       communicate_status, 
		       task_inst_id, 
		       communicate_from, 
		       is_hangup 
		FROM   CLMS_communicate_base
		WHERE  report_no = #{reportNo,jdbcType=VARCHAR}
		       AND case_times = #{caseTimes,jdbcType=NUMERIC}
		       AND communicate_title = '04'
			   AND communicate_status = '2'
			   limit 1
	</select>



	<select id="getCommunicateBaseInfo" resultType="com.paic.ncbs.claim.model.vo.communicate.CommunicateBaseVO">
		SELECT
			ID_AHCS_COMMUNICATE_BASE idAhcsCommunicateBase,
			REPORT_NO reportNo,
			CASE_TIMES caseTimes,
			COMMUNICATE_TITLE communicateTitle,
			COMMUNICATE_LINK communicateLink,
			INITIATOR_UM initiatorUm,
			COMMUNICATE_STATUS communicateStatus,
			TASK_INST_ID taskInstId,
			COMMUNICATE_FROM communicateFrom,
			IS_HANGUP
		FROM CLMS_COMMUNICATE_BASE t
		WHERE t.report_no=#{reportNo,jdbcType=VARCHAR}
		  AND t.communicate_status!='2'
			order by t.updated_date desc
		  limit 1
	</select>

	<select id="getCommunicateBaseInfoByConditions" resultMap="CommunicateBaseVO">
		SELECT
		T.ID_AHCS_COMMUNICATE_BASE,
		T.REPORT_NO,
		T.CASE_TIMES,
		T.COMMUNICATE_TITLE,
		T.COMMUNICATE_LINK,
		T.INITIATOR_UM,
		T.COMMUNICATE_STATUS,
		T.TASK_INST_ID,
		T.COMMUNICATE_FROM ,
		T.IS_HANGUP
		FROM   CLMS_COMMUNICATE_BASE T
		WHERE  T.REPORT_NO = #{reportNo, jdbcType = VARCHAR}
		<if test='caseTimes != null and caseTimes !=""'>
			AND    T.CASE_TIMES = #{caseTimes, jdbcType = NUMERIC}
		</if>
		<if test='taskInstId != null and taskInstId !=""'>
			AND    T.TASK_INST_ID = #{taskInstId, jdbcType = VARCHAR}
		</if>
		<if test='communicateLink != null and communicateLink !=""'>
			AND    T.COMMUNICATE_LINK = #{communicateLink, jdbcType = VARCHAR}
		</if>
		<if test='communicateFrom != null and communicateFrom !=""'>
			AND    T.COMMUNICATE_FROM = #{communicateFrom, jdbcType = VARCHAR}
		</if>
		<if test='communicateStatus != null and communicateStatus =="2"'>
			AND    T.COMMUNICATE_STATUS = #{communicateStatus, jdbcType = VARCHAR}
		</if>
		<if test='communicateStatus != null and communicateStatus =="1"'>
			AND    T.COMMUNICATE_STATUS = #{communicateStatus, jdbcType = VARCHAR}
		</if>
		<if test='communicateStatus != null and communicateStatus =="0"'>
			AND    T.COMMUNICATE_STATUS = #{communicateStatus, jdbcType = VARCHAR}
		</if>
		<if test='communicateStatus == null or communicateStatus==""'>
			AND  T.COMMUNICATE_STATUS IN ('0','1')
		</if>
	</select>

	<select id="getDefaultCommunicatePerson" resultMap="CommunicatePersonMap">
		SELECT DISTINCT RR.TASK_ACCEPT_UM AS COMMUNICATE_UM, RR.TASK_ACCEPT_NAME AS COMMUNICATE_NAME
		FROM   CLMS_TASK_REASSIGN_RECORD RR
		WHERE  RR.TASK_ID IN (SELECT T.TASK_ID
							  FROM   CLMS_TASK_INFO T
							  WHERE
								T.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
								AND    T.CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
								AND    T.TASK_DEFINITION_BPM_KEY = #{taskDefinitionBpmKey,jdbcType=VARCHAR})
		  AND    RR.TASK_ACCEPT_UM IS NOT NULL
		  and    RR.TASK_ACCEPT_UM != #{userId,jdbcType=VARCHAR}
	</select>

	<select id="getCommunicateFromNoTaskByConditions" resultMap="CommunicateBaseVO">
		SELECT
		T.ID_AHCS_COMMUNICATE_BASE,
		T.REPORT_NO,
		T.CASE_TIMES,
		T.COMMUNICATE_TITLE,
		T.COMMUNICATE_LINK,
		T.INITIATOR_UM,
		T.COMMUNICATE_STATUS,
		T.TASK_INST_ID,
		T.COMMUNICATE_FROM ,
		T.IS_HANGUP
		FROM   CLMS_COMMUNICATE_BASE T
		WHERE  T.REPORT_NO = #{reportNo, jdbcType = VARCHAR}
		<if test='caseTimes != null and caseTimes !=""'>
			AND    T.CASE_TIMES = #{caseTimes, jdbcType = NUMERIC}
		</if>
		AND    T.TASK_INST_ID is null
		<if test='communicateLink != null and communicateLink !=""'>
			AND    T.COMMUNICATE_LINK = #{communicateLink, jdbcType = VARCHAR}
		</if>
		<if test='communicateFrom != null and communicateFrom !=""'>
			AND    T.COMMUNICATE_FROM = #{communicateFrom, jdbcType = VARCHAR}
		</if>
		<if test='communicateStatus != null and communicateStatus =="2"'>
			AND    T.COMMUNICATE_STATUS = #{communicateStatus, jdbcType = VARCHAR}
		</if>
		<if test='communicateStatus != null and communicateStatus =="1"'>
			AND    T.COMMUNICATE_STATUS = #{communicateStatus, jdbcType = VARCHAR}
		</if>
		<if test='communicateStatus != null and communicateStatus =="0"'>
			AND    T.COMMUNICATE_STATUS = #{communicateStatus, jdbcType = VARCHAR}
		</if>
		<if test='communicateStatus == null or communicateStatus==""'>
			AND  T.COMMUNICATE_STATUS IN ('0','1')
		</if>
	</select>


	<select id="getTaskId" resultType="java.lang.String">
		SELECT
			T.TASK_ID
		FROM   CLMS_TASK_INFO T
		WHERE
		      T.STATUS != 1
		  AND    T.END_DATE IS NULL
		  AND    T.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		  AND    T.CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
		  AND    T.TASK_DEFINITION_BPM_KEY = #{taskDefinitionBpmKey,jdbcType=VARCHAR}
		  limit 1
	</select>


	<delete id="deleteCommunicateBaseInfoById" >
		DELETE FROM CLMS_COMMUNICATE_BASE T WHERE T.ID_AHCS_COMMUNICATE_BASE IN
		<foreach item="idAhcsCommunicateBase" index="index" collection="deleteCommunicateBaseIdList" open="(" separator="," close=")">
			#{idAhcsCommunicateBase,jdbcType=VARCHAR}
		</foreach>
	</delete>

	<select id="getLeastCommunicateCount" resultType="java.lang.String">
		SELECT B.DEAL_UM
		FROM CLMS_COMMUNICATE_DETAIL B, CLMS_COMMUNICATE_BASE A
		WHERE A.ID_AHCS_COMMUNICATE_BASE = B.ID_AHCS_COMMUNICATE_BASE
		AND B.TASK_STATUS = '0'
		AND A.COMMUNICATE_STATUS != '2'
		AND B.ROLE = '0'
		AND B.DEAL_UM in
		<foreach item="dealUm" index="index" collection="dealUmList" open="(" separator="," close=")">
			#{dealUm,jdbcType=VARCHAR}
		</foreach>
		group by B.DEAL_UM order by count(B.ID_AHCS_COMMUNICATE_BASE)
	</select>

	<update id="cancelCommunicateTask">
		update CLMS_communicate_base a
		set
			a.updated_by=#{userId},
			a.updated_date=sysdate(),
			a.communicate_status='2',
			a.finish_date=sysdate()
		where a.report_no=#{reportNo, jdbcType=VARCHAR}
        <![CDATA[ and a.case_times <= #{caseTimes} ]]>
    		      and a.finish_date is null
	</update>

</mapper>