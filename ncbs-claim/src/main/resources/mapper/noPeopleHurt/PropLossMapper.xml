<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.noPeopleHurt.PropLossMapper">
  <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.duty.PropLossEntity">
    <!--@mbg.generated-->
    <!--@Table clms_prop_loss-->
    <id column="id" property="id" />
    <result column="report_no" property="reportNo" />
    <result column="case_times" property="caseTimes" />
    <result column="should_pay_amount" property="shouldPayAmount" />
    <result column="deductible_amount" property="deductibleAmount" />
    <result column="deductible_rate" property="deductibleRate" />
    <result column="payable_amount" property="payableAmount" />
    <result column="remark" property="remark" />
    <result column="created_by" property="createdBy" />
    <result column="created_date" property="createdDate" />
    <result column="updated_by" property="updatedBy" />
    <result column="updated_date" property="updatedDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, report_no, case_times, should_pay_amount, deductible_amount, deductible_rate, 
    payable_amount, remark, created_by, created_date, updated_by, updated_date
  </sql>


  <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
    insert into clms_prop_loss(id, report_no, case_times, should_pay_amount, deductible_amount, deductible_rate,
                               payable_amount, remark, created_by, created_date, updated_by, updated_date)
    select replace(UUID(), '-', ''),
           report_no,
           #{reopenCaseTimes},
           should_pay_amount,
           deductible_amount,
           deductible_rate,
           payable_amount,
           remark,
           #{userId},
           NOW(),
           #{userId},
           NOW()
    from clms_prop_loss
    where REPORT_NO = #{reportNo}
      AND CASE_TIMES = #{caseTimes}
  </insert>
</mapper>