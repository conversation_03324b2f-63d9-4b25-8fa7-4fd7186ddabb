<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsLegalLiabilityClassInfoMapper">

    <resultMap type="com.paic.ncbs.claim.dao.entity.clms.ClmsLegalLiabilityClassInfo" id="ClmsLegalLiabilityClassInfoMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="reportNo" column="report_no" jdbcType="VARCHAR"/>
        <result property="caseTimes" column="case_times" jdbcType="INTEGER"/>
        <result property="feeType" column="fee_type" jdbcType="VARCHAR"/>
        <result property="payAmount" column="pay_amount" jdbcType="NUMERIC"/>
        <result property="payObject" column="pay_object" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDate" column="updated_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ClmsLegalLiabilityClassInfoMap">
        select id,
               report_no,
               case_times,
               fee_type,
               pay_amount,
               pay_object,
               remark,
               created_by,
               created_date,
               updated_by,
               updated_date
        from clms_legal_liability_class_info
        where id = #{id}
    </select>

    <!--根据报案号查询单个-->
    <select id="queryByReportNo" resultMap="ClmsLegalLiabilityClassInfoMap">
        select id,
               report_no,
               case_times,
               fee_type,
               pay_amount,
               pay_object,
               remark,
               created_by,
               created_date,
               updated_by,
               updated_date
        from clms_legal_liability_class_info
        where report_no = #{reportNo}
          and case_times = #{caseTimes}
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into clms_legal_liability_class_info(id,report_no, case_times, fee_type, pay_amount, pay_object,remark, created_by,
                                                    created_date, updated_by, updated_date)
        values (#{id},#{reportNo}, #{caseTimes}, #{feeType}, #{payAmount}, #{payObject},#{remark}, #{createdBy}, #{createdDate},
                #{updatedBy}, #{updatedDate})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into clms_legal_liability_class_info(id,report_no, case_times, fee_type, pay_amount, pay_object, remark,created_by,
        created_date, updated_by, updated_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.reportNo}, #{entity.caseTimes}, #{entity.feeType}, #{entity.payAmount}, #{entity.payObject},#{entity.remark},
            #{entity.createdBy}, #{entity.createdDate}, #{entity.updatedBy}, #{entity.updatedDate})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update clms_legal_liability_class_info
        <set>
            <if test="reportNo != null and reportNo != ''">
                report_no = #{reportNo},
            </if>
            <if test="caseTimes != null">
                case_times = #{caseTimes},
            </if>
            <if test="feeType != null and feeType != ''">
                fee_type = #{feeType},
            </if>
            <if test="payAmount != null">
                pay_amount = #{payAmount},
            </if>
            <if test="payObject != null and payObject != ''">
                pay_object = #{payObject},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdDate != null">
                created_date = #{createdDate},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDate != null">
                updated_date = #{updatedDate},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from clms_legal_liability_class_info
        where id = #{id}
    </delete>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        insert into clms_legal_liability_class_info(id, report_no, case_times, fee_type, pay_amount, pay_object, remark,
                                                    created_by,
                                                    created_date, updated_by, updated_date)
        select replace(UUID(), '-', ''),
               report_no,
               #{reopenCaseTimes},
               fee_type,
               pay_amount,
               pay_object,
               remark,
               #{userId},
               NOW(),
               #{userId},
               NOW()
        from clms_legal_liability_class_info
        where REPORT_NO = #{reportNo}
          AND CASE_TIMES = #{caseTimes}
    </insert>

    <delete id="deleteByCondition">
        delete
        from clms_legal_liability_class_info
        where report_no = #{reportNo}
        and case_times = #{caseTimes}
    </delete>

    <select id="getSettleAmout" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(pay_amount), 0)
        FROM clms_legal_liability_class_info
        WHERE report_no = #{reportNo}
        AND case_times = #{caseTimes}
    </select>
</mapper>

