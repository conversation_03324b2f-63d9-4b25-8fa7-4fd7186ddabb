<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsRescueInfoMapper">

    <resultMap type="com.paic.ncbs.claim.dao.entity.clms.ClmsRescueInfo" id="ClmsRescueInfoMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="reportNo" column="report_no" jdbcType="VARCHAR"/>
        <result property="caseTimes" column="case_times" jdbcType="INTEGER"/>
        <result property="rescueType" column="rescue_type" jdbcType="VARCHAR"/>
        <result property="rescueDate" column="rescue_date" jdbcType="TIMESTAMP"/>
        <result property="rescueName" column="rescue_name" jdbcType="VARCHAR"/>
        <result property="rescueAmount" column="rescue_amount" jdbcType="NUMERIC"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDate" column="updated_date" jdbcType="TIMESTAMP"/>
        <result property="rescueAddress" column="rescue_address" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ClmsRescueInfoMap">
        select id,
               report_no,
               case_times,
               rescue_type,
               rescue_date,
               rescue_name,
               rescue_amount,
               remark,
               created_by,
               created_date,
               updated_by,
               updated_date,
               rescue_address
        from clms_rescue_info
        where id = #{id}
    </select>

    <!--根据报案号查询单个-->
    <select id="queryByReportNo" resultMap="ClmsRescueInfoMap">
        select id,
               report_no,
               case_times,
               rescue_type,
               rescue_date,
               rescue_name,
               rescue_amount,
               remark,
               created_by,
               created_date,
               updated_by,
               updated_date,
               rescue_address
        from clms_rescue_info
        where report_no = #{reportNo}
          and case_times = #{caseTimes}
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into clms_rescue_info(id,report_no, case_times, rescue_type, rescue_date, rescue_name, rescue_amount,
                                     remark, created_by, created_date, updated_by, updated_date, rescue_address)
        values (#{id},#{reportNo}, #{caseTimes}, #{rescueType}, #{rescueDate}, #{rescueName}, #{rescueAmount}, #{remark},
                #{createdBy}, #{createdDate}, #{updatedBy}, #{updatedDate},#{rescueAddress})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into clms_rescue_info(id,report_no, case_times, rescue_type, rescue_date, rescue_name, rescue_amount,
        remark, created_by, created_date, updated_by, updated_date, rescue_address)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.reportNo}, #{entity.caseTimes}, #{entity.rescueType}, #{entity.rescueDate}, #{entity.rescueName},
            #{entity.rescueAmount}, #{entity.remark}, #{entity.createdBy}, #{entity.createdDate}, #{entity.updatedBy},
            #{entity.updatedDate},#{entity.rescueAddress})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update clms_rescue_info
        <set>
            <if test="reportNo != null and reportNo != ''">
                report_no = #{reportNo},
            </if>
            <if test="caseTimes != null">
                case_times = #{caseTimes},
            </if>
            <if test="rescueType != null and rescueType != ''">
                rescue_type = #{rescueType},
            </if>
            <if test="rescueDate != null">
                rescue_date = #{rescueDate},
            </if>
            <if test="rescueName != null and rescueName != ''">
                rescue_name = #{rescueName},
            </if>
            <if test="rescueAmount != null">
                rescue_amount = #{rescueAmount},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdDate != null">
                created_date = #{createdDate},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDate != null">
                updated_date = #{updatedDate},
            </if>
            <if test="rescueAddress != null and rescueAddress != ''">
                updated_by = #{rescueAddress},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from clms_rescue_info
        where id = #{id}
    </delete>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        insert into clms_rescue_info(id, report_no, case_times, rescue_type, rescue_date, rescue_name, rescue_amount,
                                     remark, created_by, created_date, updated_by, updated_date, rescue_address)
        select replace(UUID(), '-', ''),
               report_no,
               #{reopenCaseTimes},
               rescue_type,
               rescue_date,
               rescue_name,
               rescue_amount,
               remark,
               #{userId},
               NOW(),
               #{userId},
               NOW(),
               rescue_address
        from clms_rescue_info
        where REPORT_NO = #{reportNo}
          AND CASE_TIMES = #{caseTimes}
    </insert>

    <delete id="deleteByCondition">
        delete
        from clms_rescue_info
        where report_no = #{reportNo}
        and case_times = #{caseTimes}
    </delete>

    <select id="getSettleAmout" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(rescue_amount), 0)
        FROM clms_rescue_info
        WHERE report_no = #{reportNo}
        AND case_times = #{caseTimes}
    </select>
</mapper>

