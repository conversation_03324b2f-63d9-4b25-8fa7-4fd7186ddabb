<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsSubstanceLossInfoMapper">

    <resultMap type="com.paic.ncbs.claim.dao.entity.clms.ClmsSubstanceLossInfo" id="ClmsSubstanceLossInfoMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="reportNo" column="report_no" jdbcType="VARCHAR"/>
        <result property="caseTimes" column="case_times" jdbcType="INTEGER"/>
        <result property="serialNo" column="serial_no" jdbcType="INTEGER"/>
        <result property="lossName" column="loss_name" jdbcType="VARCHAR"/>
        <result property="lossAmount" column="loss_amount" jdbcType="NUMERIC"/>
        <result property="deductionAmount" column="deduction_amount" jdbcType="NUMERIC"/>
        <result property="payableAmount" column="payable_amount" jdbcType="NUMERIC"/>
        <result property="lossReason" column="loss_reason" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDate" column="updated_date" jdbcType="TIMESTAMP"/>
        <result property="insuranceTarget" column="insurance_target" jdbcType="VARCHAR"/>
        <result property="damagedPropertyType" column="damaged_property_type" jdbcType="VARCHAR"/>
        <result property="targetDescribe" column="target_describe" jdbcType="VARCHAR"/>
        <result property="reportAmount" column="report_amount" jdbcType="NUMERIC"/>
        <result property="estimateAmount" column="estimate_amount" jdbcType="NUMERIC"/>
        <result property="salvageValue" column="salvage_value" jdbcType="NUMERIC"/>
        <result property="insuranceRate" column="insurance_rate" jdbcType="NUMERIC"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ClmsSubstanceLossInfoMap">
        select id,
               report_no,
               case_times,
               serial_no,
               loss_name,
               loss_amount,
               deduction_amount,
               payable_amount,
               loss_reason,
               remark,
               created_by,
               created_date,
               updated_by,
               updated_date,
               insurance_target,
               damaged_property_type,
               target_describe,
               report_amount,
               estimate_amount,
               salvage_value,
               insurance_rate
        from clms_substance_loss_info
        where id = #{id}
    </select>

    <!--根据报案号查询单个-->
    <select id="queryByReportNo" resultMap="ClmsSubstanceLossInfoMap">
        select id,
               report_no,
               case_times,
               serial_no,
               loss_name,
               loss_amount,
               deduction_amount,
               payable_amount,
               loss_reason,
               remark,
               created_by,
               created_date,
               updated_by,
               updated_date,
               insurance_target,
               damaged_property_type,
               target_describe,
               report_amount,
               estimate_amount,
               salvage_value,
               insurance_rate
        from clms_substance_loss_info
        where report_no = #{reportNo}
          and case_times = #{caseTimes}
        order by serial_no
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into clms_substance_loss_info(id, report_no, case_times, serial_no, loss_name, loss_amount,
                                             deduction_amount,
                                             payable_amount, loss_reason, remark,
                                             created_by, created_date, updated_by, updated_date,
        insurance_target, damaged_property_type,
        target_describe, report_amount, estimate_amount,
        salvage_value, insurance_rate)
        values (#{id}, #{reportNo}, #{caseTimes}, #{serialNo}, #{lossName}, #{lossAmount}, #{deductionAmount},
                #{payableAmount},
                #{lossReason}, #{remark},
                #{createdBy},
                #{createdDate}, #{updatedBy}, #{updatedDate}, #{insuranceTarget},
        #{damagedPropertyType},#{targetDescribe},#{reportAmount},#{estimateAmount},#{salvageValue},#{insuranceRate})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into clms_substance_loss_info(id, report_no, case_times, serial_no, loss_name, loss_amount,
                                             deduction_amount,
                                             payable_amount, loss_reason,
                                             remark,
                                             created_by, created_date, updated_by, updated_date,
         insurance_target, damaged_property_type, target_describe, report_amount, estimate_amount, salvage_value, insurance_rate)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id}, #{entity.reportNo}, #{entity.caseTimes}, #{entity.serialNo}, #{entity.lossName},
             #{entity.lossAmount}, #{entity.deductionAmount}, #{entity.payableAmount}, #{entity.lossReason},
             #{entity.remark}, #{entity.createdBy}, #{entity.createdDate}, #{entity.updatedBy}, #{entity.updatedDate},
            #{entity.insuranceTarget},#{entity.damagedPropertyType},#{entity.targetDescribe},#{entity.reportAmount},
            #{entity.estimateAmount},#{entity.salvageValue},#{entity.insuranceRate})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update clms_substance_loss_info
        <set>
            <if test="reportNo != null and reportNo != ''">
                report_no = #{reportNo},
            </if>
            <if test="caseTimes != null">
                case_times = #{caseTimes},
            </if>
            <if test="serialNo != null">
                serial_no = #{serialNo,jdbcType=INTEGER},
            </if>
            <if test="lossName != null and lossName != ''">
                loss_name = #{lossName},
            </if>
            <if test="lossAmount != null">
                loss_amount = #{lossAmount},
            </if>
            <if test="deductionAmount != null">
                deduction_amount = #{deductionAmount,jdbcType=DECIMAL},
            </if>
            <if test="payableAmount != null">
                payable_amount = #{payableAmount,jdbcType=DECIMAL},
            </if>
            <if test="lossReason != null and lossReason != ''">
                loss_reason = #{lossReason},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdDate != null">
                created_date = #{createdDate},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDate != null">
                updated_date = #{updatedDate},
            </if>
            <if test="insuranceTarget != null and insuranceTarget != ''">
                insurance_target = #{insuranceTarget},
            </if>
            <if test="damagedPropertyType != null and damagedPropertyType != ''">
                damaged_property_type = #{damagedPropertyType},
            </if>
            <if test="targetDescribe != null and targetDescribe != ''">
                target_describe = #{targetDescribe},
            </if>
            <if test="reportAmount != null">
                report_amount = #{reportAmount},
            </if>
            <if test="estimateAmount != null">
                estimate_amount = #{estimateAmount},
            </if>
            <if test="salvageValue != null">
                salvage_value = #{salvageValue},
            </if>
            <if test="insuranceRate != null">
                insurance_rate = #{insuranceRate}
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from clms_substance_loss_info
        where id = #{id}
    </delete>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        insert into clms_substance_loss_info(id, report_no, case_times, serial_no, loss_name, loss_amount,
                                             deduction_amount, payable_amount,
                                             loss_reason, remark,
                                             created_by, created_date, updated_by, updated_date,
         insurance_target, damaged_property_type, target_describe, report_amount, estimate_amount, salvage_value, insurance_rate)
        select replace(UUID(), '-', ''),
               report_no,
               #{reopenCaseTimes},
               serial_no,
               loss_name,
               loss_amount,
               deduction_amount,
               payable_amount,
               loss_reason,
               remark,
               #{userId},
               NOW(),
               #{userId},
               NOW(),
               insurance_target,
               damaged_property_type,
               target_describe,
               report_amount,
               estimate_amount,
               salvage_value,
               insurance_rate
        from clms_substance_loss_info
        where REPORT_NO = #{reportNo}
          AND CASE_TIMES = #{caseTimes}
    </insert>

    <delete id="deleteByCondition">
        delete
        from clms_substance_loss_info
        where report_no = #{reportNo}
        and case_times = #{caseTimes}
    </delete>

    <select id="getSettleAmout" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(loss_amount), 0)
        FROM clms_substance_loss_info
        WHERE report_no = #{reportNo}
        AND case_times = #{caseTimes}
    </select>
</mapper>

