<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsAllowanceInfoMapper">

    <resultMap type="com.paic.ncbs.claim.dao.entity.clms.ClmsAllowanceInfo" id="ClmsAllowanceInfoMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="reportNo" column="report_no" jdbcType="VARCHAR"/>
        <result property="caseTimes" column="case_times" jdbcType="INTEGER"/>
        <result property="allowanceType" column="allowance_type" jdbcType="VARCHAR"/>
        <result property="otherTypeDescription" column="other_type_description" jdbcType="VARCHAR"/>
        <result property="dailyAllowanceAmount" column="daily_allowance_amount" jdbcType="NUMERIC"/>
        <result property="allowanceAmount" column="allowance_amount" jdbcType="NUMERIC"/>
        <result property="datePeriod" column="date_period" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDate" column="updated_date" jdbcType="TIMESTAMP"/>
        <result property="allowanceUnit" column="allowance_unit" jdbcType="VARCHAR"/>
        <result property="allowanceName" column="allowance_name" jdbcType="VARCHAR"/>
        <result property="monovalent" column="monovalent" jdbcType="NUMERIC"/>
        <result property="estimateAmount" column="estimate_amount" jdbcType="NUMERIC"/>
        <result property="frequency" column="frequency" jdbcType="VARCHAR"/>
        <result property="number" column="number" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="days" column="days" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ClmsAllowanceInfoMap">
        select id,
               report_no,
               case_times,
               allowance_type,
               other_type_description,
               daily_allowance_amount,
               allowance_amount,
               date_period,
               created_by,
               created_date,
               updated_by,
               updated_date,
               allowance_unit,
               allowance_name,
               monovalent,
               estimate_amount,
               frequency,
               number,
               start_time,
               end_time,
               days
        from clms_allowance_info
        where id = #{id}
    </select>

    <!--根据报案号查询单个-->
    <select id="queryByReportNo" resultMap="ClmsAllowanceInfoMap">
        select id,
               report_no,
               case_times,
               allowance_type,
               other_type_description,
               daily_allowance_amount,
               allowance_amount,
               date_period,
               created_by,
               created_date,
               updated_by,
               updated_date,
               allowance_unit,
               allowance_name,
               monovalent,
               estimate_amount,
               frequency,
               number,
               start_time,
               end_time,
               days
        from clms_allowance_info
        where report_no = #{reportNo}
          and case_times = #{caseTimes}
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into clms_allowance_info(id,report_no, case_times, allowance_type,other_type_description, daily_allowance_amount,
                                        allowance_amount,date_period, created_by, created_date, updated_by, updated_date,allowance_unit,allowance_name,monovalent,estimate_amount,frequency,number,start_time,end_time,days)
        values (#{id},#{reportNo}, #{caseTimes}, #{allowanceType},#{otherTypeDescription}, #{dailyAllowanceAmount},  #{allowanceAmount},
                #{datePeriod},#{createdBy}, #{createdDate}, #{updatedBy}, #{updatedDate},#{allowanceUnit},#{allowanceName},#{monovalent},#{estimateAmount},#{frequency},#{number},#{startTime},#{endTime},#{days})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into clms_allowance_info(id,report_no, case_times, allowance_type,other_type_description, daily_allowance_amount,
        allowance_amount,date_period, created_by, created_date, updated_by, updated_date,allowance_unit,allowance_name,monovalent,estimate_amount,frequency,number,start_time,end_time,days)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.reportNo}, #{entity.caseTimes}, #{entity.allowanceType},#{entity.otherTypeDescription}, #{entity.dailyAllowanceAmount},
             #{entity.allowanceAmount},#{entity.datePeriod}, #{entity.createdBy}, #{entity.createdDate}, #{entity.updatedBy},
            #{entity.updatedDate},#{entity.allowanceUnit},#{entity.allowanceName},#{entity.monovalent}，#{entity.estimateAmount},#{entity.frequency},#{entity.number},#{entity.startTime},#{entity.endTime},#{entity.days})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update clms_allowance_info
        <set>
            <if test="reportNo != null and reportNo != ''">
                report_no = #{reportNo},
            </if>
            <if test="caseTimes != null">
                case_times = #{caseTimes},
            </if>
            <if test="allowanceType != null and allowanceType != ''">
                allowance_type = #{allowanceType},
            </if>
            <if test="otherTypeDescription != null and otherTypeDescription != ''">
                other_type_description = #{otherTypeDescription},
            </if>
            <if test="dailyAllowanceAmount != null">
                daily_allowance_amount = #{dailyAllowanceAmount},
            </if>
            <if test="allowanceAmount != null">
                allowance_amount = #{allowanceAmount},
            </if>
            <if test="datePeriod != null">
                date_period = #{datePeriod},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdDate != null">
                created_date = #{createdDate},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDate != null">
                updated_date = #{updatedDate},
            </if>
            <if test="allowanceUnit != null and allowanceUnit != ''">
                allowance_unit = #{allowanceUnit},
            </if>
            <if test="allowanceName != null and allowanceName != ''">
                allowance_name = #{allowanceName},
            </if>
            <if test="monovalent != null">
                monovalent = #{monovalent},
            </if>
            <if test="estimateAmount != null">
                estimate_amount = #{estimateAmount},
            </if>
            <if test="frequency != null and frequency != ''">
                frequency = #{frequency},
            </if>
            <if test="number != null and number != ''">
                number = #{number}
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="days != null and days != ''">
                days = #{days}
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from clms_allowance_info
        where id = #{id}
    </delete>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        insert into clms_allowance_info(id, report_no, case_times, allowance_type, other_type_description,
                                        daily_allowance_amount,allowance_amount, date_period,created_by, created_date, updated_by, updated_date,
        allowance_unit,allowance_name,monovalent,estimate_amount,frequency,number,start_time,end_time,days)
        select replace(UUID(), '-', ''),
               report_no,
               #{reopenCaseTimes},
               allowance_type,
               other_type_description,
               daily_allowance_amount,
               allowance_amount,
               date_period,
               #{userId},
               NOW(),
               #{userId},
               NOW(),
               allowance_unit,
               allowance_name,
               monovalent,
               estimate_amount,
               frequency,
               number,
               start_time,
               end_time,
               days
        from clms_allowance_info
        where REPORT_NO = #{reportNo}
          AND CASE_TIMES = #{caseTimes}
    </insert>

    <delete id="deleteByCondition">
        delete
        from clms_allowance_info
        where report_no = #{reportNo}
        and case_times = #{caseTimes}
    </delete>

    <select id="getSettleAmout" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(allowance_amount), 0)
        FROM clms_allowance_info
        WHERE report_no = #{reportNo}
        AND case_times = #{caseTimes}
    </select>
</mapper>

