<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.noPeopleHurt.PropDetailLossMapper">
  <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.duty.PropDetailLossEntity">
    <!--@mbg.generated-->
    <!--@Table clms_prop_detail_loss-->
    <id column="id" property="id" />
    <result column="id_prop_loss" property="idPropLoss" />
    <result column="report_no" property="reportNo" />
    <result column="case_times" property="caseTimes" />
    <result column="serial_no" property="serialNo" />
    <result column="loss_type" property="lossType" />
    <result column="loss_type_name" property="lossTypeName" />
    <result column="loss_desc" property="lossDesc" />
    <result column="report_amount" property="reportAmount" />
    <result column="estimate_amount" property="estimateAmount" />
    <result column="remnant" property="remnant" />
    <result column="insurance_rate" property="insuranceRate" />
    <result column="remark" property="remark" />
    <result column="created_by" property="createdBy" />
    <result column="created_date" property="createdDate" />
    <result column="updated_by" property="updatedBy" />
    <result column="updated_date" property="updatedDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_prop_loss, report_no, case_times, serial_no, loss_type, loss_type_name, loss_desc, 
    report_amount, estimate_amount, remnant, insurance_rate, remark, created_by, created_date, 
    updated_by, updated_date
  </sql>

  <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
    INSERT INTO CLMS_PROP_DETAIL_LOSS(id, id_prop_loss, report_no, case_times, serial_no, loss_type, loss_type_name,
                                      loss_desc,
                                      report_amount, estimate_amount, remnant, insurance_rate, remark, created_by,
                                      created_date,
                                      updated_by, updated_date)
    values
    <foreach collection="detailLossEntityList" item="item" separator=",">
      (#{item.id}, #{item.idPropLoss}, #{item.reportNo}, #{item.caseTimes}, #{item.serialNo}, #{item.lossType},
       #{item.lossTypeName}, #{item.lossDesc}, #{item.reportAmount},
       #{item.estimateAmount}, #{item.remnant}, #{item.insuranceRate},
       #{item.remark}, #{item.createdBy}, #{item.createdDate}, #{item.updatedBy}, #{item.updatedDate})
    </foreach>
  </insert>


    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        insert into CLMS_PROP_DETAIL_LOSS(id, id_prop_loss, report_no, case_times, serial_no, loss_type, loss_type_name,
                                          loss_desc,
                                          report_amount, estimate_amount, remnant, insurance_rate, remark, created_by,
                                          created_date,
                                          updated_by, updated_date)
        select replace(UUID(), '-', ''),
               (select id from clms_prop_loss where REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}),
               report_no,
               #{reopenCaseTimes},
               serial_no,
               loss_type,
               loss_type_name,
               loss_desc,
               report_amount,
               estimate_amount,
               remnant,
               insurance_rate,
               remark,
               #{userId},
               NOW(),
               #{userId},
               NOW()
        from CLMS_PROP_DETAIL_LOSS
        where REPORT_NO = #{reportNo}
          AND CASE_TIMES = #{caseTimes}
    </insert>
</mapper>