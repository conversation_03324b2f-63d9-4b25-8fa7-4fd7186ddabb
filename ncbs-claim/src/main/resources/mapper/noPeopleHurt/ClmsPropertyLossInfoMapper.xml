<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsPropertyLossInfoMapper">

    <resultMap type="com.paic.ncbs.claim.dao.entity.clms.ClmsPropertyLossInfo" id="ClmsPropertyLossInfoMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="reportNo" column="report_no" jdbcType="VARCHAR"/>
        <result property="caseTimes" column="case_times" jdbcType="INTEGER"/>
        <result property="thirdPartyLossType" column="third_party_loss_type" jdbcType="VARCHAR"/>
        <result property="thirdPartyName" column="third_party_name" jdbcType="VARCHAR"/>
        <result property="thirdPartyCertificateType" column="third_party_certificate_type" jdbcType="VARCHAR"/>
        <result property="thirdPartyCertificateNo" column="third_party_certificate_no" jdbcType="VARCHAR"/>
        <result property="propertyLossCause" column="property_loss_cause" jdbcType="VARCHAR"/>
        <result property="propertyLossProject" column="property_loss_project" jdbcType="VARCHAR"/>
        <result property="payAmount" column="pay_amount" jdbcType="NUMERIC"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDate" column="updated_date" jdbcType="TIMESTAMP"/>
        <result property="reportAmount" column="report_amount" jdbcType="NUMERIC"/>
        <result property="estimateAmount" column="estimate_amount" jdbcType="NUMERIC"/>
        <result property="salvageValue" column="salvage_value" jdbcType="NUMERIC"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ClmsPropertyLossInfoMap">
        select id,
               report_no,
               case_times,
               third_party_loss_type,
               third_party_name,
               third_party_certificate_type,
               third_party_certificate_no,
               property_loss_cause,
               property_loss_project,
               pay_amount,
               remark,
               created_by,
               created_date,
               updated_by,
               updated_date,
               report_amount,
               estimate_amount,
               salvage_value
        from clms_property_loss_info
        where id = #{id}
    </select>

    <!--根据报案号查询单个-->
    <select id="queryByReportNo" resultMap="ClmsPropertyLossInfoMap">
        select id,
               report_no,
               case_times,
               third_party_loss_type,
               third_party_name,
               third_party_certificate_type,
               third_party_certificate_no,
               property_loss_cause,
               property_loss_project,
               pay_amount,
               remark,
               created_by,
               created_date,
               updated_by,
               updated_date,
               report_amount,
               estimate_amount,
               salvage_value
        from clms_property_loss_info
        where report_no = #{reportNo}
        and case_times = #{caseTimes}
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into clms_property_loss_info(id,report_no, case_times, third_party_loss_type, third_party_name,
                                            third_party_certificate_type, third_party_certificate_no,
                                            property_loss_cause, property_loss_project, pay_amount, remark, created_by,
                                            created_date, updated_by, updated_date,  report_amount, estimate_amount, salvage_value)
        values (#{id},#{reportNo}, #{caseTimes}, #{thirdPartyLossType}, #{thirdPartyName}, #{thirdPartyCertificateType},
                #{thirdPartyCertificateNo}, #{propertyLossCause}, #{propertyLossProject}, #{payAmount}, #{remark},
                #{createdBy}, #{createdDate}, #{updatedBy}, #{updatedDate},#{reportAmount},#{estimateAmount},#{salvageValue})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into clms_property_loss_info(id,report_no, case_times, third_party_loss_type, third_party_name,
        third_party_certificate_type, third_party_certificate_no, property_loss_cause, property_loss_project,
        pay_amount, remark, created_by, created_date, updated_by, updated_date,  report_amount, estimate_amount, salvage_value)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.reportNo}, #{entity.caseTimes}, #{entity.thirdPartyLossType}, #{entity.thirdPartyName},
            #{entity.thirdPartyCertificateType}, #{entity.thirdPartyCertificateNo}, #{entity.propertyLossCause},
            #{entity.propertyLossProject}, #{entity.payAmount}, #{entity.remark}, #{entity.createdBy},
            #{entity.createdDate}, #{entity.updatedBy}, #{entity.updatedDate},#{entity.reportAmount},#{entity.estimateAmount},#{entity.salvageValue})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update clms_property_loss_info
        <set>
            <if test="reportNo != null and reportNo != ''">
                report_no = #{reportNo},
            </if>
            <if test="caseTimes != null">
                case_times = #{caseTimes},
            </if>
            <if test="thirdPartyLossType != null and thirdPartyLossType != ''">
                third_party_loss_type = #{thirdPartyLossType},
            </if>
            <if test="thirdPartyName != null and thirdPartyName != ''">
                third_party_name = #{thirdPartyName},
            </if>
            <if test="thirdPartyCertificateType != null and thirdPartyCertificateType != ''">
                third_party_certificate_type = #{thirdPartyCertificateType},
            </if>
            <if test="thirdPartyCertificateNo != null and thirdPartyCertificateNo != ''">
                third_party_certificate_no = #{thirdPartyCertificateNo},
            </if>
            <if test="propertyLossCause != null and propertyLossCause != ''">
                property_loss_cause = #{propertyLossCause},
            </if>
            <if test="propertyLossProject != null and propertyLossProject != ''">
                property_loss_project = #{propertyLossProject},
            </if>
            <if test="payAmount != null">
                pay_amount = #{payAmount},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdDate != null">
                created_date = #{createdDate},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDate != null">
                updated_date = #{updatedDate},
            </if>
            <if test="reportAmount != null">
                report_amount = #{reportAmount},
            </if>
            <if test="estimateAmount != null">
                estimate_amount = #{estimateAmount},
            </if>
            <if test="salvageValue != null">
                salvage_value = #{salvageValue}
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from clms_property_loss_info
        where id = #{id}
    </delete>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        insert into clms_property_loss_info(id, report_no, case_times, third_party_loss_type, third_party_name,
                                            third_party_certificate_type, third_party_certificate_no,
                                            property_loss_cause, property_loss_project, pay_amount, remark, created_by,
                                            created_date, updated_by, updated_date,  report_amount, estimate_amount, salvage_value)
        select replace(UUID(), '-', ''),
               report_no,
               #{reopenCaseTimes},
               third_party_loss_type,
               third_party_name,
               third_party_certificate_type,
               third_party_certificate_no,
               property_loss_cause,
               property_loss_project,
               pay_amount,
               remark,
               #{userId},
               NOW(),
               #{userId},
               NOW(),
               report_amount,
               estimate_amount,
               salvage_value
        from clms_property_loss_info
        where REPORT_NO = #{reportNo}
          AND CASE_TIMES = #{caseTimes}
    </insert>

    <delete id="deleteByCondition">
        delete
        from clms_property_loss_info
        where report_no = #{reportNo}
        and case_times = #{caseTimes}
    </delete>

    <select id="getSettleAmout" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(pay_amount), 0)
        FROM clms_property_loss_info
        WHERE report_no = #{reportNo}
        AND case_times = #{caseTimes}
    </select>
</mapper>

