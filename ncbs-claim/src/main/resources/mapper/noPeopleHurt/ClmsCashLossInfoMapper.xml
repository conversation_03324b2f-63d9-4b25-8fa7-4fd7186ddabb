<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsCashLossInfoMapper">

    <resultMap type="com.paic.ncbs.claim.dao.entity.clms.ClmsCashLossInfo" id="ClmsCashLossInfoMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="reportNo" column="report_no" jdbcType="VARCHAR"/>
        <result property="caseTimes" column="case_times" jdbcType="INTEGER"/>
        <result property="lossAmount" column="loss_amount" jdbcType="NUMERIC"/>
        <result property="lossReason" column="loss_reason" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDate" column="updated_date" jdbcType="TIMESTAMP"/>
        <result property="cashLossAmount" column="cash_loss_amount" jdbcType="NUMERIC"/>
        <result property="remarks" column="remarks" jdbcType="VARCHAR"/>

    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ClmsCashLossInfoMap">
        select id,
               report_no,
               case_times,
               loss_amount,
               loss_reason,
               created_by,
               created_date,
               updated_by,
               updated_date,
               cash_loss_amount,
               remarks
        from clms_cash_loss_info
        where id = #{id}
    </select>

    <!--根据报案号查询单个-->
    <select id="queryByReportNo" resultMap="ClmsCashLossInfoMap">
        select id,
               report_no,
               case_times,
               loss_amount,
               loss_reason,
               created_by,
               created_date,
               updated_by,
               updated_date,
               cash_loss_amount,
               remarks
        from clms_cash_loss_info
        where report_no = #{reportNo}
          and case_times = #{caseTimes}
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into clms_cash_loss_info(id,report_no, case_times, loss_amount, loss_reason, created_by, created_date,
                                        updated_by, updated_date, cash_loss_amount, remarks)
        values (#{id},#{reportNo}, #{caseTimes}, #{lossAmount}, #{lossReason}, #{createdBy}, #{createdDate}, #{updatedBy},
                #{updatedDate},#{cashLossAmount},#{remarks})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into clms_cash_loss_info(id,report_no, case_times, loss_amount, loss_reason, created_by, created_date,
        updated_by, updated_date,  cash_loss_amount, remarks)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.reportNo}, #{entity.caseTimes}, #{entity.lossAmount}, #{entity.lossReason}, #{entity.createdBy},
            #{entity.createdDate}, #{entity.updatedBy}, #{entity.updatedDate},#{entity.cashLossAmount},#{entity.remarks})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update clms_cash_loss_info
        <set>
            <if test="reportNo != null and reportNo != ''">
                report_no = #{reportNo},
            </if>
            <if test="caseTimes != null">
                case_times = #{caseTimes},
            </if>
            <if test="lossAmount != null">
                loss_amount = #{lossAmount},
            </if>
            <if test="lossReason != null and lossReason != ''">
                loss_reason = #{lossReason},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdDate != null">
                created_date = #{createdDate},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDate != null">
                updated_date = #{updatedDate},
            </if>
            <if test="cashLossAmount != null">
                cash_loss_amount = #{cashLossAmount},
            </if>
            <if test="remarks != null and remarks != ''">
                remarks = #{remarks},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from clms_cash_loss_info
        where id = #{id}
    </delete>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        insert into clms_cash_loss_info(id, report_no, case_times, loss_amount, loss_reason, created_by, created_date,
                                        updated_by, updated_date,  cash_loss_amount, remarks)
        select replace(UUID(), '-', ''),
               report_no,
               #{reopenCaseTimes},
               loss_amount,
               loss_reason,
               #{userId},
               NOW(),
               #{userId},
               NOW(),
               cash_loss_amount,
               remarks
        from clms_cash_loss_info
        where REPORT_NO = #{reportNo}
          AND CASE_TIMES = #{caseTimes}
    </insert>

    <delete id="deleteByCondition">
        delete
        from clms_cash_loss_info
        where report_no = #{reportNo}
        and case_times = #{caseTimes}
    </delete>

    <select id="getSettleAmout" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(loss_amount), 0)
        FROM clms_cash_loss_info
        WHERE report_no = #{reportNo}
        AND case_times = #{caseTimes}
    </select>
</mapper>

