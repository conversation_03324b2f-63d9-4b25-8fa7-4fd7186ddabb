<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.noPeopleHurt.ClmsPersonalInjuryDeathInfoMapper">

    <resultMap type="com.paic.ncbs.claim.dao.entity.clms.ClmsPersonalInjuryDeathInfo" id="ClmsPersonalInjuryDeathInfoMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="reportNo" column="report_no" jdbcType="VARCHAR"/>
        <result property="caseTimes" column="case_times" jdbcType="INTEGER"/>
        <result property="injuredName" column="injured_name" jdbcType="VARCHAR"/>
        <result property="injuredGender" column="injured_gender" jdbcType="VARCHAR"/>
        <result property="injuredCertificateType" column="injured_certificate_type" jdbcType="VARCHAR"/>
        <result property="injuredCertificateNo" column="injured_certificate_no" jdbcType="VARCHAR"/>
        <result property="injuredBirthday" column="injured_birthday" jdbcType="TIMESTAMP"/>
        <result property="injuredAge" column="injured_age" jdbcType="INTEGER"/>
        <result property="injuredWhetherDeath" column="injured_whether_death" jdbcType="VARCHAR"/>
        <result property="injuredDiedDate" column="injured_died_date" jdbcType="TIMESTAMP"/>
        <result property="injuredDiedCause" column="injured_died_cause" jdbcType="VARCHAR"/>
        <result property="injuredWhetherDisabled" column="injured_whether_disabled" jdbcType="VARCHAR"/>
        <result property="injuredDisabledDate" column="injured_disabled_date" jdbcType="TIMESTAMP"/>
        <result property="injuredDisabledGrade" column="injured_disabled_grade" jdbcType="VARCHAR"/>
        <result property="personalInjuryCause" column="personal_injury_cause" jdbcType="VARCHAR"/>
        <result property="lossMedicalAmount" column="loss_medical_amount" jdbcType="NUMERIC"/>
        <result property="lossWorkingTimeAmount" column="loss_working_time_amount" jdbcType="NUMERIC"/>
        <result property="lossInjuryDeathAmount" column="loss_injury_death_amount" jdbcType="NUMERIC"/>
        <result property="lossHospitalizationSubsidyAmount" column="loss_hospitalization_subsidy_amount"
                jdbcType="NUMERIC"/>
        <result property="lossOtherAmount" column="loss_other_amount" jdbcType="NUMERIC"/>
        <result property="sumPayAmount" column="sum_pay_amount" jdbcType="NUMERIC"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDate" column="updated_date" jdbcType="TIMESTAMP"/>
        <result property="injuredWhetherInHospital" column="injured_whether_inhospital" jdbcType="VARCHAR"/>
        <result property="injuredHospitalCode" column="injured_hospital_code" jdbcType="VARCHAR"/>
        <result property="injuredHospitalName" column="injured_hospital_name" jdbcType="VARCHAR"/>
        <result property="riskGroupNo" column="risk_group_no" jdbcType="VARCHAR"/>
        <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
        <result property="injuredProfession.gradeCode" column="injured_profession_grade_code" jdbcType="VARCHAR"/>
        <result property="injuredProfession.level1Code" column="injured_profession_level1_code" jdbcType="VARCHAR"/>
        <result property="injuredProfession.level2Code" column="injured_profession_level2_code" jdbcType="VARCHAR"/>
        <result property="injuredProfession.level3Code" column="injured_profession_level3_code" jdbcType="VARCHAR"/>
        <result property="policyNo" column="policy_no" jdbcType="VARCHAR"/>
        <result property="riskGroupName" column="risk_group_name" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ClmsPersonalInjuryDeathInfoMap">
        select id,
               report_no,
               case_times,
               injured_name,
               injured_gender,
               injured_certificate_type,
               injured_certificate_no,
               injured_birthday,
               injured_age,
               injured_whether_death,
               injured_died_date,
               injured_died_cause,
               injured_whether_disabled,
               injured_disabled_date,
               injured_disabled_grade,
               personal_injury_cause,
               loss_medical_amount,
               loss_working_time_amount,
               loss_injury_death_amount,
               loss_hospitalization_subsidy_amount,
               loss_other_amount,
               sum_pay_amount,
               created_by,
               created_date,
               updated_by,
               updated_date,
               remark
        from clms_personal_injury_death_info
        where id = #{id}
    </select>

    <!--根据报案号查询单个-->
    <select id="queryByReportNo" resultMap="ClmsPersonalInjuryDeathInfoMap">
        select id,
               report_no,
               case_times,
               injured_name,
               injured_gender,
               injured_certificate_type,
               injured_certificate_no,
               injured_birthday,
               injured_age,
               injured_whether_death,
               injured_died_date,
               injured_died_cause,
               injured_whether_disabled,
               injured_disabled_date,
               injured_disabled_grade,
               personal_injury_cause,
               loss_medical_amount,
               loss_working_time_amount,
               loss_injury_death_amount,
               loss_hospitalization_subsidy_amount,
               loss_other_amount,
               sum_pay_amount,
               created_by,
               created_date,
               updated_by,
               updated_date,
               injured_whether_inhospital,
               injured_hospital_code,
               injured_hospital_name,
               risk_group_no,
               task_id,
               injured_profession_grade_code,
               injured_profession_level1_code,
               injured_profession_level2_code,
               injured_profession_level3_code,
               policy_no,
               remark
        from clms_personal_injury_death_info
        where report_no = #{reportNo} and case_times = #{caseTimes}
        order by created_date desc limit 1
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into clms_personal_injury_death_info(id,report_no, case_times, injured_name, injured_gender,
                                                    injured_certificate_type, injured_certificate_no, injured_birthday,
                                                    injured_age, injured_whether_death, injured_died_date,
                                                    injured_died_cause, injured_whether_disabled, injured_disabled_date,
                                                    injured_disabled_grade, personal_injury_cause, loss_medical_amount,
                                                    loss_working_time_amount, loss_injury_death_amount,
                                                    loss_hospitalization_subsidy_amount, loss_other_amount,
                                                    sum_pay_amount, created_by, created_date, updated_by, updated_date,client_no,
                                                    injured_whether_inhospital, injured_hospital_code, injured_hospital_name,
                                                    risk_group_no, task_id,
                                                    injured_profession_grade_code, injured_profession_level1_code, injured_profession_level2_code,injured_profession_level3_code,
                                                    policy_no, risk_group_name,remark)
        values (#{id},#{reportNo}, #{caseTimes}, #{injuredName}, #{injuredGender}, #{injuredCertificateType},
                #{injuredCertificateNo}, #{injuredBirthday}, #{injuredAge}, #{injuredWhetherDeath}, #{injuredDiedDate},
                #{injuredDiedCause}, #{injuredWhetherDisabled}, #{injuredDisabledDate}, #{injuredDisabledGrade},
                #{personalInjuryCause}, #{lossMedicalAmount}, #{lossWorkingTimeAmount}, #{lossInjuryDeathAmount},
                #{lossHospitalizationSubsidyAmount}, #{lossOtherAmount}, #{sumPayAmount}, #{createdBy}, #{createdDate},
                #{updatedBy}, #{updatedDate},#{clientNo,jdbcType=VARCHAR},#{injuredWhetherInHospital},#{injuredHospitalCode},#{injuredHospitalName},
                #{riskGroupNo},#{taskId},#{injuredProfession.gradeCode},#{injuredProfession.level1Code},#{injuredProfession.level2Code},#{injuredProfession.level3Code},#{policyNo},#{riskGroupName},#{remark})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into clms_personal_injury_death_info(id,report_no, case_times, injured_name, injured_gender,
        injured_certificate_type, injured_certificate_no, injured_birthday, injured_age, injured_whether_death,
        injured_died_date, injured_died_cause, injured_whether_disabled, injured_disabled_date, injured_disabled_grade,
        personal_injury_cause, loss_medical_amount, loss_working_time_amount, loss_injury_death_amount,
        loss_hospitalization_subsidy_amount, loss_other_amount, sum_pay_amount, created_by, created_date, updated_by,
        updated_date,client_no,remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.reportNo}, #{entity.caseTimes}, #{entity.injuredName}, #{entity.injuredGender},
            #{entity.injuredCertificateType}, #{entity.injuredCertificateNo}, #{entity.injuredBirthday},
            #{entity.injuredAge}, #{entity.injuredWhetherDeath}, #{entity.injuredDiedDate}, #{entity.injuredDiedCause},
            #{entity.injuredWhetherDisabled}, #{entity.injuredDisabledDate}, #{entity.injuredDisabledGrade},
            #{entity.personalInjuryCause}, #{entity.lossMedicalAmount}, #{entity.lossWorkingTimeAmount},
            #{entity.lossInjuryDeathAmount}, #{entity.lossHospitalizationSubsidyAmount}, #{entity.lossOtherAmount},
            #{entity.sumPayAmount}, #{entity.createdBy}, #{entity.createdDate}, #{entity.updatedBy},
            #{entity.updatedDate},#{clientNo,jdbcType=VARCHAR},#{entity.remark})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update clms_personal_injury_death_info
        <set>
            <if test="reportNo != null and reportNo != ''">
                report_no = #{reportNo},
            </if>
            <if test="caseTimes != null">
                case_times = #{caseTimes},
            </if>
            <if test="injuredName != null and injuredName != ''">
                injured_name = #{injuredName},
            </if>
            <if test="injuredGender != null and injuredGender != ''">
                injured_gender = #{injuredGender},
            </if>
            <if test="injuredCertificateType != null and injuredCertificateType != ''">
                injured_certificate_type = #{injuredCertificateType},
            </if>
            <if test="injuredCertificateNo != null and injuredCertificateNo != ''">
                injured_certificate_no = #{injuredCertificateNo},
            </if>
            <if test="injuredBirthday != null">
                injured_birthday = #{injuredBirthday},
            </if>
            <if test="injuredAge != null">
                injured_age = #{injuredAge},
            </if>
            <if test="injuredWhetherDeath != null and injuredWhetherDeath != ''">
                injured_whether_death = #{injuredWhetherDeath},
            </if>
            <if test="injuredDiedDate != null">
                injured_died_date = #{injuredDiedDate},
            </if>
            <if test="injuredDiedCause != null and injuredDiedCause != ''">
                injured_died_cause = #{injuredDiedCause},
            </if>
            <if test="injuredWhetherDisabled != null and injuredWhetherDisabled != ''">
                injured_whether_disabled = #{injuredWhetherDisabled},
            </if>
            <if test="injuredDisabledDate != null">
                injured_disabled_date = #{injuredDisabledDate},
            </if>
            <if test="injuredDisabledGrade != null and injuredDisabledGrade != ''">
                injured_disabled_grade = #{injuredDisabledGrade},
            </if>
            <if test="personalInjuryCause != null and personalInjuryCause != ''">
                personal_injury_cause = #{personalInjuryCause},
            </if>
            <if test="lossMedicalAmount != null">
                loss_medical_amount = #{lossMedicalAmount},
            </if>
            <if test="lossWorkingTimeAmount != null">
                loss_working_time_amount = #{lossWorkingTimeAmount},
            </if>
            <if test="lossInjuryDeathAmount != null">
                loss_injury_death_amount = #{lossInjuryDeathAmount},
            </if>
            <if test="lossHospitalizationSubsidyAmount != null">
                loss_hospitalization_subsidy_amount = #{lossHospitalizationSubsidyAmount},
            </if>
            <if test="lossOtherAmount != null">
                loss_other_amount = #{lossOtherAmount},
            </if>
            <if test="sumPayAmount != null">
                sum_pay_amount = #{sumPayAmount},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdDate != null">
                created_date = #{createdDate},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDate != null">
                updated_date = #{updatedDate},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark}
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from clms_personal_injury_death_info
        where id = #{id}
    </delete>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        insert into clms_personal_injury_death_info(id, report_no, case_times, injured_name, injured_gender,
                                                    injured_certificate_type, injured_certificate_no, injured_birthday,
                                                    injured_age, injured_whether_death,
                                                    injured_died_date, injured_died_cause, injured_whether_disabled,
                                                    injured_disabled_date, injured_disabled_grade,
                                                    personal_injury_cause, loss_medical_amount,
                                                    loss_working_time_amount, loss_injury_death_amount,
                                                    loss_hospitalization_subsidy_amount, loss_other_amount,
                                                    sum_pay_amount, created_by, created_date, updated_by,
                                                    updated_date,client_no,remark)
        select replace(UUID(), '-', ''),
               report_no,
               #{reopenCaseTimes},
               injured_name,
               injured_gender,
               injured_certificate_type,
               injured_certificate_no,
               injured_birthday,
               injured_age,
               injured_whether_death,
               injured_died_date,
               injured_died_cause,
               injured_whether_disabled,
               injured_disabled_date,
               injured_disabled_grade,
               personal_injury_cause,
               loss_medical_amount,
               loss_working_time_amount,
               loss_injury_death_amount,
               loss_hospitalization_subsidy_amount,
               loss_other_amount,
               sum_pay_amount,
               #{userId},
               NOW(),
               #{userId},
               NOW(),
               client_no,
               remark
        from clms_personal_injury_death_info
        where REPORT_NO = #{reportNo}
          AND CASE_TIMES = #{caseTimes}
    </insert>

    <delete id="deleteByCondition">
        delete
        from clms_personal_injury_death_info
        where report_no = #{reportNo}
        and case_times = #{caseTimes}
        and task_id = #{taskId}
    </delete>

    <select id="getSettleAmout" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(sum_pay_amount), 0)
        FROM clms_personal_injury_death_info
        WHERE report_no = #{reportNo}
        AND case_times = #{caseTimes}
    </select>
</mapper>

