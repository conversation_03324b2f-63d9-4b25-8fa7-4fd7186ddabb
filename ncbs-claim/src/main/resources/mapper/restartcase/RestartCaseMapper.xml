<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.restartcase.RestartCaseRecordMapper">

    <resultMap type="com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO" id="wholeCaseVO">
        <result property="reportNo" column="report_no"/>
        <result property="caseTimes" column="case_times"/>
        <result property="reportDate" column="report_date"/>
        <result property="accidentDate" column="accident_date"/>
        <result property="insuredName" column="name"/>
        <result property="clientType" column="client_type"/>
        <result property="clientTypeName" column="client_type_name"/>
        <result property="processStatusName" column="processStatusName"/>
        <result property="processStatus" column="process_status"/>
        <result property="indemnityConclusion" column="indemnity_conclusion"/>
        <result property="indemnityModel" column="indemnity_model"/>
        <result property="isRegister" column="IS_REGISTER"/>
        <result property="endCaseFlag" column="whole_case_status"/>
        <result property="endCaseDate" column="end_case_date"/>
        <result property="verifyUm" column="VERIFY_UM"/>
        <result property="isNewest" column="is_newest"/>
    </resultMap>

    <select id="selectByPrimaryKey" resultType="com.paic.ncbs.claim.dao.entity.restartcase.RestartCaseRecordEntity" parameterType="java.lang.String">
        SELECT  ID_CLM_RESTART_CASE_RECORD,
                RESTART_REASON,
                CASE_TYPE,
                CASE_KIND,
                RESTART_DESCRIPTION,
                REPORT_NO,
                CASE_TIMES,
                APPROVAL_OPINIONS,
                APPROVAL_DESCRIPTION,
                CREATED_BY,
                CREATED_DATE,
                UPDATED_BY,
                UPDATED_DATE,
                RESTART_AMOUNT
        FROM CLM_RESTART_CASE_RECORD
        WHERE ID_CLM_RESTART_CASE_RECORD = #{id}
    </select>

    <select id="getRestartCaseList" resultType="com.paic.ncbs.claim.dao.entity.restartcase.RestartCaseRecordEntity">
        select A.RESTART_REASON,
        A.ID_CLM_RESTART_CASE_RECORD,
        A.REPORT_NO,
        A.CASE_TIMES,
        A.CASE_TYPE,
        A.CASE_KIND,
        A.RESTART_DESCRIPTION,
        A.APPROVAL_OPINIONS,
        A.APPROVAL_DESCRIPTION,
        A.CREATED_BY,
        A.CREATED_DATE,
        A.UPDATED_BY,
        A.UPDATED_DATE,
        A.RESTART_AMOUNT,
        A.ERROR_TYPE
        from clm_restart_case_record A
        WHERE A.REPORT_NO = #{reportNo}
        <if test="caseTimes != null ">
            and A.CASE_TIMES = #{caseTimes}
        </if>
        order by A.CREATED_DATE asc
    </select>
    <select id="getThisRestartCaseList" resultType="com.paic.ncbs.claim.dao.entity.restartcase.RestartCaseRecordEntity">
        select A.RESTART_REASON,
        A.ID_CLM_RESTART_CASE_RECORD,
        A.REPORT_NO,
        A.CASE_TIMES,
        A.CASE_TYPE,
        A.CASE_KIND,
        A.RESTART_DESCRIPTION,
        A.APPROVAL_OPINIONS,
        A.APPROVAL_DESCRIPTION,
        A.CREATED_BY,
        A.CREATED_DATE,
        A.UPDATED_BY,
        A.UPDATED_DATE,
        A.RESTART_AMOUNT,
        A.ERROR_TYPE
        from clm_restart_case_record A
        WHERE A.APPROVAL_OPINIONS IS NOT NULL
        and A.REPORT_NO = #{reportNo}
        and A.CASE_TIMES = #{caseTimes}
        order by A.UPDATED_DATE desc
    </select>
    <select id="getRestartCaseListForDetail" resultType="com.paic.ncbs.claim.dao.entity.restartcase.RestartCaseRecordEntity">
        select A.RESTART_REASON,
        A.ID_CLM_RESTART_CASE_RECORD,
        A.REPORT_NO,
        A.CASE_TIMES,
        A.CASE_TYPE,
        A.CASE_KIND,
        A.RESTART_DESCRIPTION,
        A.APPROVAL_OPINIONS,
        A.APPROVAL_DESCRIPTION,
        A.CREATED_BY,
        A.CREATED_DATE,
        A.UPDATED_BY,
        A.UPDATED_DATE,
        A.RESTART_AMOUNT,
        A.ERROR_TYPE
        from clm_restart_case_record A
        WHERE A.REPORT_NO = #{reportNo}
        and A.CASE_TIMES = #{caseTimes}
        order by A.CREATED_DATE desc
        limit 1
    </select>
    <select id="getRestartList" resultType="com.paic.ncbs.claim.dao.entity.restartcase.RestartCaseRecordEntity">
        select * from (
        select A.RESTART_REASON,
        A.ID_CLM_RESTART_CASE_RECORD,
        A.REPORT_NO,
        A.CASE_TIMES,
        ROW_NUMBER() OVER (
        PARTITION BY A.REPORT_NO, A.CASE_TIMES
        ORDER BY A.UPDATED_DATE DESC
        ) AS rn,
        A.CASE_TYPE,
        A.CASE_KIND,
        A.RESTART_DESCRIPTION,
        A.APPROVAL_OPINIONS,
        A.APPROVAL_DESCRIPTION,
        A.CREATED_BY,
        A.CREATED_DATE,
        A.UPDATED_BY,
        A.UPDATED_DATE,
        A.RESTART_AMOUNT,
        A.ERROR_TYPE
        from clm_restart_case_record A
        where A.REPORT_NO = #{reportNo ,jdbcType=VARCHAR}) t where t.rn = 1
    </select>
    <select id="getRestartCaseListByApproval" resultType="com.paic.ncbs.claim.dao.entity.restartcase.RestartCaseRecordEntity">
        select A.RESTART_REASON,
        A.ID_CLM_RESTART_CASE_RECORD,
        A.REPORT_NO,
        A.CASE_TIMES,
        A.CASE_TYPE,
        A.CASE_KIND,
        A.RESTART_DESCRIPTION,
        A.APPROVAL_OPINIONS,
        A.APPROVAL_DESCRIPTION,
        A.CREATED_BY,
        A.CREATED_DATE,
        A.UPDATED_BY,
        A.UPDATED_DATE,
        A.RESTART_AMOUNT
        from clm_restart_case_record A
        WHERE A.REPORT_NO=#{reportNo ,jdbcType=VARCHAR}
        and A.CASE_TIMES=#{caseTimes ,jdbcType=DECIMAL}
        and isnull(A.APPROVAL_OPINIONS)
    </select>

    <select id="getRestartApprovalReturnList" resultType="com.paic.ncbs.claim.dao.entity.restartcase.RestartCaseRecordEntity">
        select A.RESTART_REASON,
        A.ID_CLM_RESTART_CASE_RECORD,
        A.REPORT_NO,
        A.CASE_TIMES,
        A.CASE_TYPE,
        A.CASE_KIND,
        A.RESTART_DESCRIPTION,
        A.APPROVAL_OPINIONS,
        A.APPROVAL_DESCRIPTION,
        A.CREATED_BY,
        A.CREATED_DATE,
        A.UPDATED_BY,
        A.UPDATED_DATE,
        A.RESTART_AMOUNT
        from clm_restart_case_record A
        WHERE A.REPORT_NO=#{reportNo ,jdbcType=VARCHAR}
        and A.CASE_TIMES=#{caseTimes ,jdbcType=DECIMAL}
        order by A.CREATED_DATE desc
        limit 1
    </select>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.restartcase.RestartCaseRecordEntity">
        insert into clm_restart_case_record (
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_CLM_RESTART_CASE_RECORD,
        RESTART_REASON,
        CASE_TYPE,
        CASE_KIND,
        RESTART_DESCRIPTION,
        REPORT_NO,
        CASE_TIMES,
        APPROVAL_OPINIONS,
        APPROVAL_DESCRIPTION,
        RESTART_AMOUNT)
        values(
        #{createdBy ,jdbcType=VARCHAR},
        now(),
        #{updatedBy ,jdbcType=VARCHAR},
        now(),
        #{idClmRestartCaseRecord ,jdbcType=VARCHAR},
        #{restartReason ,jdbcType=VARCHAR},
        #{caseType ,jdbcType=NUMERIC},
        #{caseKind ,jdbcType=VARCHAR},
        #{restartDescription ,jdbcType=VARCHAR},
        #{reportNo ,jdbcType=VARCHAR},
        #{caseTimes ,jdbcType=NUMERIC},
        #{approvalOpinions ,jdbcType=VARCHAR},
        #{approvalDescription ,jdbcType=VARCHAR},
        #{restartAmount,jdbcType=DECIMAL}
        )
    </insert>

    <update id="invalidRestartCase">
        update clms_estimate_duty_record set IS_EFFECTIVE = 'N'
        where CASE_TIMES = #{caseTimes,jdbcType = INTEGER}
        and CASE_NO in (select CASE_NO from clm_case_base where REPORT_NO = #{reportNo, jdbcType = VARCHAR})
        and ESTIMATE_TYPE = '05'
        and IS_EFFECTIVE = 'Y'
    </update>

    <update id="saveApprovalProcess" parameterType="com.paic.ncbs.claim.dao.entity.restartcase.RestartCaseRecordEntity">
        update clm_restart_case_record set APPROVAL_OPINIONS=#{approvalOpinions ,jdbcType=INTEGER},
        APPROVAL_DESCRIPTION=#{approvalDescription ,jdbcType=VARCHAR},UPDATED_BY= #{updatedBy ,jdbcType=VARCHAR},
        UPDATED_DATE=now(),
        ERROR_TYPE=#{errorType ,jdbcType=VARCHAR}, IS_EFFECTIVE = 'Y'
        where ID_CLM_RESTART_CASE_RECORD=#{idClmRestartCaseRecord ,jdbcType=VARCHAR}

    </update>

    <select id="countTask" resultType="Integer">
        select count(1)
        from
        clms_task_info a
        where a.REPORT_NO=#{reportNo ,jdbcType=VARCHAR}
        and a.CASE_TIMES=#{caseTimes ,jdbcType=INTEGER}
        and a.TASK_DEFINITION_BPM_KEY = 'OC_CHECK_DUTY'
    </select>

    <select id="getHistoryCaseByReportNo" resultMap="wholeCaseVO">
        select a.report_no,
        a.whole_case_status,
        a.case_times,
        d.report_date,
        a.is_newest,
        a.indemnity_conclusion,
        a.indemnity_model
        from
        clm_whole_case_base a,clm_report_info d
        where a.report_no = d.report_no
        and a.report_no = #{reportNo,jdbcType=VARCHAR}
        <if test="departmentCodes != null ">
            and d.ACCEPT_DEPARTMENT_CODE in
            <foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getCaseByReportNoAndCaseTimes" resultMap="wholeCaseVO">
        select a.report_no,
        a.whole_case_status,
        a.case_times,
        d.report_date,
        a.is_newest,
        a.indemnity_conclusion,
        a.indemnity_model
        from
        clm_whole_case_base a,clm_report_info d
        where a.report_no = d.report_no
        and a.report_no = #{reportNo,jdbcType=VARCHAR}
        and a.case_times = #{caseTimes ,jdbcType=DECIMAL}
        limit 1
    </select>


    <select id="getHistoryCaseByPolicyCaseNo" resultMap="wholeCaseVO">
        select DISTINCT a.report_no,
        a.whole_case_status,
        a.case_times,
        d.report_date,
        a.is_newest,
        a.indemnity_conclusion,
        a.indemnity_model
        from
        clm_whole_case_base a,clm_case_base b, clm_report_info d
        where a.report_no = b.report_no
        and b.report_no = d.report_no
        <if test="policyNo != null and policyNo != ''">
            and b.policy_no = #{policyNo,jdbcType=VARCHAR}
        </if>
        <if test="caseNo != null and caseNo != ''">
            and b.case_no = #{caseNo,jdbcType=VARCHAR}
        </if>
        <if test="batchNo != null and batchNo != ''">
            and (exists (select 1 from clms_batch_report_temp t where t.REPORT_BATCH_NO = #{batchNo,jdbcType=VARCHAR})
            or exists (select 1 from clms_batch_auto_close t where t.BATCH_NO=#{batchNo,jdbcType=VARCHAR}))
        </if>
        <if test="departmentCodes != null ">
            and d.ACCEPT_DEPARTMENT_CODE in
            <foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getHistoryCaseByBatchNo" resultMap="wholeCaseVO">
        select a.report_no,
        a.whole_case_status,
        a.case_times,
        d.report_date,
        a.is_newest,
        a.indemnity_conclusion,
        a.indemnity_model
        from
        clm_whole_case_base a,clm_report_info d,clms_batch_report_temp b
        where a.report_no = d.report_no
        and d.REPORT_NO =b.REPORT_NO
        and b.REPORT_BATCH_NO = #{batchNo,jdbcType=VARCHAR}
        <if test="departmentCodes != null ">
            and d.ACCEPT_DEPARTMENT_CODE in
            <foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        union
        select a.report_no,
        a.whole_case_status,
        a.case_times,
        d.report_date,
        a.is_newest,
        a.indemnity_conclusion,
        a.indemnity_model
        from
        clm_whole_case_base a,clm_report_info d,clms_batch_auto_close c
        where a.report_no = d.report_no
        and d.REPORT_NO = c.REPORT_NO
        and c.BATCH_NO  = #{batchNo,jdbcType=VARCHAR}
        <if test="departmentCodes != null ">
            and d.ACCEPT_DEPARTMENT_CODE in
            <foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="updateIsNewest" parameterType="com.paic.ncbs.claim.dao.entity.restartcase.RestartCaseRecordEntity">
        update clm_whole_case_base set is_newest = '0',
        UPDATED_BY= #{updatedBy ,jdbcType=VARCHAR},
        UPDATED_DATE=now()
        where REPORT_NO=#{reportNo ,jdbcType=VARCHAR}
        and CASE_TIMES=#{caseTimes ,jdbcType=DECIMAL}
    </update>
    <select id="getRestartAuditTime" resultType="com.paic.ncbs.claim.dao.entity.restartcase.RestartCaseRecordEntity">
        select UPDATED_DATE updatedDate from clm_restart_case_record
        where REPORT_NO=#{reportNo}
        and case_times=#{caseTimes}
        and APPROVAL_OPINIONS  = '0'
    </select>
    <update id="invalidRestartCaseRecord">
        update clm_restart_case_record a set IS_EFFECTIVE = 'N', UPDATED_DATE = now()
        where a.REPORT_NO=#{reportNo ,jdbcType=VARCHAR}
        and a.CASE_TIMES=#{caseTimes ,jdbcType=INTEGER}
    </update>
    <select id="getRestartCaseRecordcount" resultType="Integer">
        select count(1)
        from
        clm_restart_case_record a
        where a.REPORT_NO=#{reportNo ,jdbcType=VARCHAR}
        and a.CASE_TIMES=#{caseTimes ,jdbcType=INTEGER}
        and a.APPROVAL_OPINIONS='0'
    </select>

</mapper>