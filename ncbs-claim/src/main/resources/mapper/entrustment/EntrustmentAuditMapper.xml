<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.entrustment.EntrustmentAuditMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.entrustment.EntrustAuditDTO" id="result">
        <id property="idEntrustAudit" column="id_entrust_audit" />
        <result property="idEntrustMain" column="id_entrust_main" />
        <result property="reportNo" column="report_no" />
        <result property="caseTimes" column="case_times" />
        <result property="policyNo" column="policy_no" />
        <result property="insuredName" column="insured_name" />
        <result property="thirdPartyType" column="third_party_type" />
        <result property="entrustDptCode" column="entrust_dpt_code" />
        <result property="entrustDptName" column="entrust_dpt_name" />
        <result property="submitCode" column="submit_code" />
        <result property="submitName" column="submit_name" />
        <result property="auditorCode" column="auditor_code" />
        <result property="auditorName" column="auditor_name" />
        <result property="auditorDptCode" column="auditor_dpt_code" />
        <result property="auditorDptName" column="auditor_dpt_name" />
        <result property="auditOpinion" column="audit_opinion" />
        <result property="auditTime" column="audit_time" />
        <result property="validFlag" column="valid_flag" />
        <result property="remark" column="remark" />
        <result property="createdBy" column="created_by" />
        <result property="sysCtime" column="sys_ctime" />
        <result property="updatedBy" column="updated_by" />
        <result property="sysUtime" column="sys_utime" />
        <result property="entrustName" column="entrust_name" />
    </resultMap>

    <!-- 插入委托审批记录（条件插入：先检查是否存在待审核记录） -->
    <insert id="insertEntrustmentAudit" parameterType="com.paic.ncbs.claim.model.dto.entrustment.EntrustAuditDTO">
        INSERT INTO clms_entrust_audit (
            id_entrust_audit, id_entrust_main, report_no, case_times,
            policy_no, insured_name, third_party_type, entrust_dpt_code,
            entrust_dpt_name, submit_code, submit_name, auditor_code,
            auditor_name, auditor_dpt_code, auditor_dpt_name, audit_opinion,
            audit_time, valid_flag, remark,
            created_by, sys_ctime, updated_by, sys_utime
        )
        SELECT
            #{idEntrustAudit}, #{idEntrustMain}, #{reportNo}, #{caseTimes},
            #{policyNo}, #{insuredName}, #{thirdPartyType}, #{entrustDptCode},
            #{entrustDptName}, #{submitCode}, #{submitName}, #{auditorCode},
            #{auditorName}, #{auditorDptCode}, #{auditorDptName}, #{auditOpinion},
            #{auditTime}, #{validFlag}, #{remark},
            #{createdBy}, #{sysCtime}, #{updatedBy}, #{sysUtime}
        WHERE NOT EXISTS (
            SELECT 1 FROM clms_entrust_audit
            WHERE report_no = #{reportNo}
            AND case_times = #{caseTimes}
            AND audit_opinion = '0'
        )
    </insert>

    <!-- 更新委托审批记录 -->
    <update id="updateEntrustmentAudit" parameterType="com.paic.ncbs.claim.model.dto.entrustment.EntrustAuditDTO">
        UPDATE clms_entrust_audit
        <set>
            <if test="idEntrustMain != null">id_entrust_main = #{idEntrustMain},</if>
            <if test="reportNo != null">report_no = #{reportNo},</if>
            <if test="caseTimes != null">case_times = #{caseTimes},</if>
            <if test="policyNo != null">policy_no = #{policyNo},</if>
            <if test="insuredName != null">insured_name = #{insuredName},</if>
            <if test="thirdPartyType != null">third_party_type = #{thirdPartyType},</if>
            <if test="entrustDptCode != null">entrust_dpt_code = #{entrustDptCode},</if>
            <if test="entrustDptName != null">entrust_dpt_name = #{entrustDptName},</if>
            <if test="submitCode != null">submit_code = #{submitCode},</if>
            <if test="submitName != null">submit_name = #{submitName},</if>
            <if test="auditorCode != null">auditor_code = #{auditorCode},</if>
            <if test="auditorName != null">auditor_name = #{auditorName},</if>
            <if test="auditorDptCode != null">auditor_dpt_code = #{auditorDptCode},</if>
            <if test="auditorDptName != null">auditor_dpt_name = #{auditorDptName},</if>
            <if test="auditOpinion != null">audit_opinion = #{auditOpinion},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="validFlag != null">valid_flag = #{validFlag},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="sysUtime != null">sys_utime = #{sysUtime},</if>
        </set>
        WHERE id_entrust_main = #{idEntrustMain}
    </update>

    <!-- 根据主键查询委托审批记录 -->
    <select id="selectById" resultMap="result">
        SELECT 
            ea.*,
            em.entrust_name
        FROM clms_entrust_audit ea
        LEFT JOIN clms_entrust_main em ON ea.id_entrust_main = em.id_entrust
        WHERE ea.id_entrust_audit = #{idEntrustAudit}
    </select>

    <!-- 根据委托主表ID查询审批记录 -->
    <select id="selectByEntrustmentId" resultMap="result">
        SELECT * FROM clms_entrust_audit
        WHERE id_entrust_main = #{idEntrustMain}
        ORDER BY sys_ctime DESC
        LIMIT 1
    </select>

    <!-- 根据报案号查询审批记录 -->
    <select id="selectByReportNo" resultMap="result">
        SELECT * FROM clms_entrust_audit
        WHERE report_no = #{reportNo}
        ORDER BY sys_ctime DESC
    </select>

    <!-- 根据审批人查询待审批列表 -->
    <select id="selectPendingAuditList" resultMap="result">
        SELECT * FROM clms_entrust_audit
        WHERE auditor_code = #{auditorCode}
        AND audit_opinion IS NULL
        AND valid_flag = 'Y'
        ORDER BY sys_ctime DESC
    </select>

    <!-- 根据委托ID查询审批历史轨迹 -->
    <select id="selectAuditHistoryByEntrustId" resultMap="result">
        SELECT * FROM clms_entrust_audit
        WHERE id_entrust_main = #{idEntrust}
        ORDER BY sys_ctime DESC
    </select>

    <!-- 根据委托主表ID查询当前审批记录 -->
    <select id="selectCurrentAuditByEntrustId" resultMap="result">
        SELECT * FROM clms_entrust_audit
        WHERE id_entrust_main = #{idEntrustMain}
        AND valid_flag = 'Y'
        ORDER BY sys_ctime DESC
    </select>

</mapper>