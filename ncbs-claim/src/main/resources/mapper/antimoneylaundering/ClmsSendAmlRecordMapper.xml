<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.antimoneylaundering.ClmsAmlSendRecordMapper">

    <resultMap type="com.paic.ncbs.claim.dao.entity.antimoneylaundering.ClmsSendAmlRecordEntity" id="BaseResultMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="reportNo" column="report_no" jdbcType="VARCHAR"/>
        <result property="caseTimes" column="case_times" jdbcType="INTEGER"/>
        <result property="serialNo" column="serial_no" jdbcType="VARCHAR"/>
        <result property="amlCode" column="aml_code" jdbcType="VARCHAR"/>
        <result property="clientNo" column="client_no" jdbcType="VARCHAR"/>
        <result property="clientName" column="client_name" jdbcType="VARCHAR"/>
        <result property="clientCertificateNo" column="client_certificate_no" jdbcType="VARCHAR"/>
        <result property="clientCertificateType" column="client_certificate_type" jdbcType="VARCHAR"/>
        <result property="nationCode" column="nation_code" jdbcType="VARCHAR"/>
        <result property="auditFlag" column="audit_flag" jdbcType="VARCHAR"/>
        <result property="submissionInstructions" column="submission_instructions" jdbcType="VARCHAR"/>
        <result property="auditorCode" column="auditor_code" jdbcType="VARCHAR"/>
        <result property="auditorName" column="auditor_name" jdbcType="VARCHAR"/>
        <result property="auditTime" column="audit_time" jdbcType="VARCHAR"/>
        <result property="auditDescription" column="audit_description" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDate" column="created_date" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDate" column="updated_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, report_no, case_times, serial_no, aml_code, client_no, client_name, client_certificate_no, client_certificate_type,
        nation_code, audit_flag, submission_instructions, auditor_code, auditor_name, audit_time, audit_description,
        created_by, created_date, updated_by, updated_date
    </sql>

    <insert id="batchInsert" parameterType="java.util.List" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO clms_send_aml_record (
            id,
            report_no,
            case_times,
            serial_no,
            aml_code,
            client_no,
            client_name,
            client_certificate_no,
            client_certificate_type,
            nation_code,
            audit_flag,
            created_by,
            updated_by
        )
        VALUES
        <foreach collection="entityList" item="item" separator=",">
            (
                #{item.id},
                #{item.reportNo},
                #{item.caseTimes},
                #{item.serialNo},
                #{item.amlCode},
                #{item.clientNo},
                #{item.clientName},
                #{item.clientCertificateNo},
                #{item.clientCertificateType},
                #{item.nationCode},
                #{item.auditFlag},
                #{item.createdBy},
                #{item.updatedBy}
            )
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="entityList" item="item" separator=";">
            UPDATE clms_send_aml_record
            SET submission_instructions = #{item.submissionInstructions},
                auditor_code = #{item.auditorCode},
                auditor_name = #{item.auditorName},
                audit_time = #{item.auditTime},
                audit_flag = #{item.auditFlag},
                audit_description = #{item.auditDescription}
            WHERE serial_no = #{item.serialNo}
        </foreach>
    </update>

    <select id="selectOne" resultMap="BaseResultMap" parameterType="com.paic.ncbs.claim.dao.entity.antimoneylaundering.ClmsSendAmlRecordEntity">
        SELECT <include refid="Base_Column_List"/>
        FROM clms_send_aml_record
        WHERE report_no = #{reportNo}
        AND case_times = #{caseTimes}
        AND aml_code = #{amlCode}
        <if test="clientName != null and clientName != '' ">
            AND client_name = #{clientName}
        </if>
        <if test="clientCertificateNo != null and clientCertificateNo != '' ">
            AND client_certificate_no = #{clientCertificateNo}
        </if>
        <if test="clientCertificateType != null and clientCertificateType != '' ">
            AND client_certificate_type = #{clientCertificateType}
        </if>
        <if test="nationCode != null and nationCode != '' ">
            AND nation_code = #{nationCode}
        </if>
        limit 1;
    </select>

    <select id="selectList" resultType="com.paic.ncbs.claim.model.vo.antimoneylaundering.AmlSendRecordVO">
        SELECT  serial_no serialNo,
                submission_instructions submissionInstructions,
                auditor_name auditorName,
                audit_time auditTime,
                audit_flag auditFlag,
                audit_description auditDescription,
                created_by submitter,
                created_date submissionTime
        FROM clms_send_aml_record
        WHERE report_no = #{reportNo}
        AND case_times = #{caseTimes}
        AND aml_code = '00001'
    </select>
</mapper>

