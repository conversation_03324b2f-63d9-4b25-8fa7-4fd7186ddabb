<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.antimoneylaundering.ClmsAmlCompanyInfoMapper">

    <resultMap type="com.paic.ncbs.claim.dao.entity.antimoneylaundering.ClmsAmlCompanyInfoEntity" id="clmsAmlCompanyInfo">
        <result property="id" column="id"/>
        <result property="reportNo" column="report_no"/>
        <result property="caseTimes" column="case_times"/>
        <result property="companyName" column="company_name"/>
        <result property="agencyType" column="agency_type"/>
        <result property="agencyCode" column="agency_code"/>
        <result property="customerNo" column="customer_no"/>
        <result property="bussScope" column="buss_scope"/>
        <result property="bussLicenseStart" column="buss_license_start"/>
        <result property="bussLicenseEnd" column="buss_license_end"/>
        <result property="taxNo" column="tax_no"></result>
        <result property="industry" column="industry"/>
        <result property="regisCapitalType" column="regis_capital_type"/>
        <result property="regisCapital" column="regis_capital"/>
        <result property="bussCertificateStart" column="buss_certificate_start"/>
        <result property="bussCertificateEnd" column="buss_certificate_end"/>
        <result property="legalRepresentName" column="legal_represent_name"/>
        <result property="legalRepresentCardType" column="legal_represent_card_type"/>
        <result property="legalRepresentCardNo" column="legal_represent_card_no"></result>
        <result property="legalRepresentCardStart" column="legal_represent_card_start"></result>
        <result property="legalRepresentCardEnd" column="legal_represent_card_end"></result>
        <result property="legalLongTermFlag" column="legal_long_term_flag"></result>
        <result property="legalFileId" column="legal_file_id"></result>
        <result property="bussPeopleName" column="buss_people_name"></result>
        <result property="bussPeopleCardType" column="buss_people_card_type"></result>
        <result property="bussPeopleCardNo" column="buss_people_card_no"></result>
        <result property="bussPeopleCardStart" column="buss_people_card_start"></result>
        <result property="bussPeopleCardEnd" column="buss_people_card_end"></result>
        <result property="bussPeopleFileId" column="buss_people_file_id"></result>
        <result property="provinceCode" column="province_code"></result>
        <result property="cityCode" column="city_code"></result>
        <result property="countyCode" column="county_code"></result>
        <result property="address" column="address"></result>
        <result property="overseasOccur" column="overseas_occur"></result>
        <result property="createdBy" column="created_by"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="createdDate" column="created_date"/>
        <result property="updatedDate" column="updated_date"/>
    </resultMap>

    <select id="getAmlCompanyInfo" parameterType="com.paic.ncbs.claim.dao.entity.antimoneylaundering.ClmsAmlCompanyInfoEntity" resultMap="clmsAmlCompanyInfo">
        select id,report_no,case_times,company_name,agency_type,agency_code,customer_no,buss_scope,buss_license_start,buss_license_end,
        tax_no,industry,regis_capital_type,regis_capital,buss_certificate_start,buss_certificate_end,legal_represent_name,legal_represent_card_type,
        legal_represent_card_no,legal_represent_card_start,legal_represent_card_end,legal_long_term_flag,legal_file_id,buss_people_name,
        buss_people_card_type,buss_people_card_no,buss_people_card_start,buss_people_card_end,buss_people_file_id,province_code,city_code,
        county_code,address,overseas_occur,updated_by,updated_date,company_card_type
        from clms_aml_company_info
        where report_no=#{reportNo}
        and customer_no=#{customerNo}
        and case_times=#{caseTimes}
        and effective_status='0'
    </select>
    <!--根据id查询 -->
    <select id="getAmlCompanyInfoById" parameterType="java.lang.String" resultType="com.paic.ncbs.claim.dao.entity.antimoneylaundering.ClmsAmlCompanyInfoEntity">
        select id,report_no,case_times,company_name,agency_type,agency_code,customer_no,buss_scope,buss_license_start,buss_license_end,
        tax_no,industry,regis_capital_type,regis_capital,buss_certificate_start,buss_certificate_end,legal_represent_name,legal_represent_card_type,
        legal_represent_card_no,legal_represent_card_start,legal_represent_card_end,legal_long_term_flag,legal_file_id,buss_people_name,
        buss_people_card_type,buss_people_card_no,buss_people_card_start,buss_people_card_end,buss_people_file_id,province_code,county_code,
        county_code,address,overseas_occur,updated_by,updated_date,company_card_type
        from clms_aml_company_info
        where id=#{id}
    </select>

    <select id="getShareHolderInfos" resultType="com.paic.ncbs.claim.dao.entity.antimoneylaundering.ClmsShareholderInfoEntity">
        select id,report_no,case_times,shareholder_name,shareholder_type,shareholder_ratio,shareholder_card_type,shareholder_card_no,
        shareholder_card_start,shareholder_card_end,shareholder_card_file_id,shareholder_address,shareholder_long_term_flag
        from clms_shareholder_info
        where id_clms_aml_company_info=#{idClmsAmlCompanyInfo}
        and effective_status='0'
    </select>

    <!-- 更新股东信息表 有效状态为1-作废 -->
    <update id="updateShareholderData" parameterType="java.lang.String">
        update clms_shareholder_info set effective_status='1'
        where id=#{id}
    </update>
    <!-- 更新公司反洗钱信息表有效状态为1-作废-->
    <update id="updateCompanyStatus" parameterType="java.lang.String">
        update clms_aml_company_info set effective_status='1'
        where id=#{id}
    </update>


    <!--保存反洗钱信息 -->
    <insert id="saveAmlCompanyInfo" parameterType="com.paic.ncbs.claim.dao.entity.antimoneylaundering.ClmsAmlCompanyInfoEntity">
        insert into clms_aml_company_info(
        id,report_no,case_times,company_name,agency_type,agency_code,customer_no,buss_scope,buss_license_start,buss_license_end,tax_no,
        industry,regis_capital_type,regis_capital,buss_certificate_start,buss_certificate_end,legal_represent_name,
        legal_represent_card_type, legal_represent_card_no,legal_represent_card_start,legal_represent_card_end,
        legal_long_term_flag,legal_file_id,buss_people_name,buss_people_card_type, buss_people_card_no,buss_people_card_start,
        buss_people_card_end,buss_people_file_id,province_code,city_code,county_code,address,overseas_occur,created_by,
        created_date,updated_by,updated_date,effective_status,company_card_type)
        values(
        #{id},#{reportNo},#{caseTimes},#{companyName},#{agencyType},#{agencyCode},#{customerNo},#{bussScope},#{bussLicenseStart,jdbcType=TIMESTAMP},#{bussLicenseEnd,jdbcType=TIMESTAMP},#{taxNo},
        #{industry},#{regisCapitalType},#{regisCapital},#{bussCertificateStart,jdbcType=TIMESTAMP},#{bussCertificateEnd,jdbcType=TIMESTAMP},#{legalRepresentName},
        #{legalRepresentCardType},#{legalRepresentCardNo},#{legalRepresentCardStart,jdbcType=TIMESTAMP},#{legalRepresentCardEnd,jdbcType=TIMESTAMP},
        #{legalLongTermFlag},#{legalFileId},#{bussPeopleName},#{bussPeopleCardType},#{bussPeopleCardNo},#{bussPeopleCardStart,jdbcType=TIMESTAMP},
        #{bussPeopleCardEnd,jdbcType=TIMESTAMP},#{bussPeopleFileId},#{provinceCode},
        #{cityCode},#{countyCode},#{address},#{overseasOccur},#{createdBy},
        #{createdDate,jdbcType=TIMESTAMP},#{updatedBy},#{updatedDate,jdbcType=TIMESTAMP},#{effectiveStatus},#{companyCardType}
        )
    </insert>

    <insert id="saveShareholderInfos" parameterType="java.util.List">
        insert  into clms_shareholder_info(
        id,
        report_no,
        case_times,
        shareholder_name,
        shareholder_type,
        shareholder_ratio,
        shareholder_card_type,
        shareholder_card_no,
        shareholder_card_start,
        shareholder_card_end,
        shareholder_card_file_id,
        shareholder_address,
        shareholder_long_term_flag,
        id_clms_aml_company_info,
        created_by,
        created_date,
        updated_by,
        updated_date,
        effective_status)
        <foreach collection="lists" item="shareholder" index="index" separator="union all">
        select
            #{shareholder.id},
            #{shareholder.reportNo},
            #{shareholder.caseTimes},
            #{shareholder.shareholderName},
            #{shareholder.shareholderType},
            #{shareholder.shareholderRatio,jdbcType=DECIMAL},
            #{shareholder.shareholderCardType},
            #{shareholder.shareholderCardNo},
            #{shareholder.shareholderCardStart,jdbcType=TIMESTAMP},
            #{shareholder.shareholderCardEnd,jdbcType=TIMESTAMP},
            #{shareholder.shareholderCardFileId},
            #{shareholder.shareholderAddress},
            #{shareholder.shareholderLongTermFlag},
            #{shareholder.idClmsAmlCompanyInfo},
            #{shareholder.createdBy},
            #{shareholder.createdDate,jdbcType=TIMESTAMP},
            #{shareholder.updatedBy},
            #{shareholder.updatedDate,jdbcType=TIMESTAMP},
            #{shareholder.effectiveStatus}
        </foreach>

    </insert>


    <!--通过主键修改数据-->
    <update id="updateAmlCompanyInfo">
        update clms_aml_company_info
        <set>
            <if test="companyName != null and companyName != ''">
                company_name =  #{companyName},
            </if>
            <if test="agencyType != null and agencyType != ''">
                agency_type = #{agencyType},
            </if>
            <if test="agencyCode != null and agencyCode != ''">
                agency_code = #{agencyCode},
            </if>
            <if test="customerNo != null and customerNo != ''">
                customer_no = #{customerNo},
            </if>
            <if test="bussScope != null and bussScope != ''">
                buss_scope = #{bussScope},
            </if>
            <if test="bussLicenseEnd != null">
                buss_license_end = #{bussLicenseEnd,jdbcType=TIMESTAMP},
            </if>
            <if test="taxNo != null and taxNo != ''">
                tax_no = #{taxNo},
            </if>
            <if test="industry != null and industry != ''">
                industry = #{industry},
            </if>
            <if test="regisCapitalType != null and regisCapitalType != ''">
                regis_capital_type = #{regisCapitalType},
            </if>
            <if test="regisCapital != null and regisCapital != ''">
                regis_capital = #{regisCapital},
            </if>
            <if test="bussCertificateEnd != null ">
                buss_certificate_end = #{bussCertificateEnd,jdbcType=TIMESTAMP},
            </if>
            <if test="legalRepresentName != null and legalRepresentName != ''">
                legal_represent_name = #{legalRepresentName},
            </if>
            <if test="legalRepresentCardType != null and legalRepresentCardType != ''">
                legal_represent_card_type = #{legalRepresentCardType},
            </if>
            <if test="legalRepresentCardNo != null and legalRepresentCardNo != ''">
                legal_represent_card_no = #{legalRepresentCardNo},
            </if>
            <if test="legalRepresentCardEnd != null ">
                legal_represent_card_end = #{legalRepresentCardEnd,jdbcType=TIMESTAMP},
            </if>
            <if test="legalLongTermFlag != null and legalLongTermFlag != ''">
                legal_long_term_flag = #{legalLongTermFlag},
             </if>
            <if test="legalFileId != null and legalFileId != ''">
                legal_file_id = #{legalFileId},
            </if>

            <if test="bussPeopleName != null and bussPeopleName != ''">
                buss_people_name = #{bussPeopleName},
            </if>
            <if test="bussPeopleCardType != null and bussPeopleCardType != ''">
                buss_people_card_type = #{bussPeopleCardType},
            </if>

            <if test="bussPeopleCardNo != null and bussPeopleCardNo != ''">
                buss_people_card_no = #{bussPeopleCardNo},
            </if>
            <if test="bussPeopleCardEnd != null ">
                buss_people_card_end = #{bussPeopleCardEnd,jdbcType=TIMESTAMP},
            </if>
            <if test="bussPeopleFileId != null and bussPeopleFileId != ''">
                buss_people_file_id = #{bussPeopleFileId},
            </if>
            <if test="provinceCode != null and provinceCode != ''">
                province_code = #{provinceCode},
            </if>
            <if test="cityCode!= null and cityCode != ''">
                city_code=#{cityCode},
            </if>
            <if test="countyCode!= null and countyCode != ''">
                county_code=#{countyCode},
            </if>
            <if test="address!= null and address != ''">
                address=#{address},
            </if>
            <if test="overseasOccur!= null and overseasOccur != ''">
                overseas_occur=#{overseasOccur},
            </if>
            <if test="companyCardType!= null and companyCardType != ''">
                company_card_type=#{companyCardType},
            </if>
            <if test="updatedBy != null and updatedBy !='' ">
                updated_by=#{updatedBy},
            </if>
            <if test="updatedDate != null">
                updated_date = #{updatedDate,jdbcType=TIMESTAMP}
            </if>
        </set>
        where id = #{id}
    </update>
    <!--通过主键修改股东信息数据-->
    <update id="updateShareEntity">
        update clms_shareholder_info
        <set>
            <if test="shareholderName != null and shareholderName != ''">
                shareholder_name =  #{shareholderName},
            </if>
            <if test="shareholderType != null and shareholderType != ''">
                shareholder_type = #{shareholderType},
            </if>
            <if test="shareholderRatio != null ">
                shareholder_ratio = #{shareholderRatio},
            </if>
            <if test="shareholderCardType != null and shareholderCardType != ''">
                shareholder_card_type = #{shareholderCardType},
            </if>
            <if test="shareholderCardNo != null and shareholderCardNo != ''">
                shareholder_card_no = #{shareholderCardNo},
            </if>
            <if test="shareholderCardStart != null">
                shareholder_card_start = #{shareholderCardStart,jdbcType=TIMESTAMP},
            </if>
            <if test="shareholderCardEnd != null">
                shareholder_card_end = #{shareholderCardEnd,jdbcType=TIMESTAMP},
            </if>
            <if test="shareholderCardFileId != null and shareholderCardFileId != ''">
                shareholder_card_file_id = #{shareholderCardFileId},
            </if>
            <if test="shareholderAddress != null and shareholderAddress != ''">
                shareholder_address = #{shareholderAddress},
            </if>
            <if test="shareholderLongTermFlag != null and shareholderLongTermFlag != ''">
                shareholder_long_term_flag = #{shareholderLongTermFlag},
            </if>
            <if test="effectiveStatus != null effectiveStatus ！='' ">
                effective_status = #{effectiveStatus},
            </if>
            <if test="updatedBy != null and updatedBy !='' ">
                updated_by=#{updatedBy},
            </if>
            <if test="updatedDate != null">
                updated_date = #{updatedDate,jdbcType=TIMESTAMP}
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getAmlCompanyInfoCount" resultType="java.lang.Integer">
        select count(*)
        from clms_aml_company_info
        where report_no=#{reportNo}
        and case_times=#{caseTimes}
        and effective_status='0'
    </select>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        insert into clms_aml_company_info(
            id,report_no,case_times,company_name,agency_type,agency_code,customer_no,buss_scope,buss_license_start,buss_license_end,tax_no,
            industry,regis_capital_type,regis_capital,buss_certificate_start,buss_certificate_end,legal_represent_name,
            legal_represent_card_type, legal_represent_card_no,legal_represent_card_start,legal_represent_card_end,
            legal_long_term_flag,legal_file_id,buss_people_name,buss_people_card_type, buss_people_card_no,buss_people_card_start,
            buss_people_card_end,buss_people_file_id,province_code,city_code,county_code,address,overseas_occur,effective_status,created_by,
            created_date,updated_by,updated_date
        )
        SELECT
        REPLACE(UUID(),'-',''),
        report_no,
        #{reopenCaseTimes},
        company_name,
        agency_type,
        agency_code,
        customer_no,
        buss_scope,
        buss_license_start,
        buss_license_end,
        tax_no,
        industry,
        regis_capital_type,
        regis_capital,
        buss_certificate_start,
        buss_certificate_end,
        legal_represent_name,
        legal_represent_card_type,
        legal_represent_card_no,
        legal_represent_card_start,
        legal_represent_card_end,
        legal_long_term_flag,
        legal_file_id,
        buss_people_name,
        buss_people_card_type,
        buss_people_card_no,
        buss_people_card_start,
        buss_people_card_end,
        buss_people_file_id,
        province_code,
        city_code,
        county_code,
        address,
        overseas_occur,
        effective_status,
        #{userId},
        NOW(),
        #{userId},
        NOW()
        from clms_aml_company_info
        where report_no = #{reportNo}
        and case_times = #{caseTimes}
        and effective_status = '0'
    </insert>
    <!--根据姓名，证件类型，证件号查询反洗钱信息 -->
    <select id="getAmlCompanyByNameAndCardTypeAndCardNo" parameterType="com.paic.ncbs.claim.dao.entity.antimoneylaundering.ClmsAmlCompanyInfoEntity" resultType="com.paic.ncbs.claim.dao.entity.antimoneylaundering.ClmsAmlCompanyInfoEntity">
        select id,company_name, customer_no,buss_scope,buss_license_end,tax_no,industry,regis_capital_type,regis_capital,buss_certificate_end,legal_represent_name,
        legal_represent_card_type,legal_represent_card_no,legal_represent_card_end,legal_long_term_flag,legal_file_id,buss_people_name,buss_people_card_type,
        buss_people_card_no,buss_people_card_end,buss_people_file_id,province_code,city_code,county_code,address,overseas_occur,report_no,company_card_type,agency_code
        from clms_aml_company_info caci
        where company_name=#{companyName}
        and effective_status='0'
        order by updated_date desc
        limit 1
    </select>

    <select id="getAmlCompanyByCustomerNo" parameterType="com.paic.ncbs.claim.dao.entity.antimoneylaundering.ClmsAmlCompanyInfoEntity" resultType="com.paic.ncbs.claim.dao.entity.antimoneylaundering.ClmsAmlCompanyInfoEntity">
        select id,company_name, customer_no,buss_scope,buss_license_end,tax_no,industry,regis_capital_type,regis_capital,buss_certificate_end,legal_represent_name,
        legal_represent_card_type,legal_represent_card_no,legal_represent_card_end,legal_long_term_flag,legal_file_id,buss_people_name,buss_people_card_type,
        buss_people_card_no,buss_people_card_end,buss_people_file_id,province_code,city_code,county_code,address,overseas_occur,report_no,company_card_type,agency_code
        from clms_aml_company_info
        where customer_no=#{customerNo}
        and effective_status='0'
        order by updated_date desc
        limit 1
    </select>
</mapper>