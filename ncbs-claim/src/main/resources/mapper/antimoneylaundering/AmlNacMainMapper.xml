<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.antimoneylaundering.AmlNacMainMapper">

    <select id="getMoneyLaunderingBlackListCount" resultType="int">
        SELECT COUNT(*)
        FROM aml_nac_main m
        LEFT JOIN aml_nac_native n ON m.aml_nac_main_id = n.aml_nac_main_id
        LEFT JOIN aml_nac_ids i ON m.aml_nac_main_id = i.aml_nac_main_id
        WHERE m.is_delete = 'N'
        AND m.list_id IN ('1044', '1242', '1306', '1316', '44','1172','1064','A0002','102001','102101')
        AND (m.name = #{clientName} or n.native_charname = #{clientName})
        AND (i.id_number = #{clientCertificateNo} or i.alphanumeric = #{clientCertificateNo})
    </select>

    <select id="getHighRiskCountryBlackListCount" resultType="int">
        SELECT COUNT(*)
        FROM aml_nac_main m
        LEFT JOIN aml_nac_address a ON m.aml_nac_main_id = a.aml_nac_main_id
        WHERE m.is_delete = 'N'
        AND m.list_id IN ('1152')
        AND a.country = #{nationCode}
    </select>
</mapper>

