<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.antimoneylaundering.AntiMoneyLaunderingMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoDto" id="clmsAntiMoneyLaunderingInfo">
        <result property="idClmsAntiMoneyLaunderingInfo" column="ID_CLMS_ANTI_MONEY_LAUNDERING_INFO"/>
        <result property="reportNo" column="REPORT_NO"/>
        <result property="clientName" column="CLIENT_NAME"/>
        <result property="clientCertificateType" column="CLIENT_CERTIFICATE_TYPE"/>
        <result property="clientCertificateNo" column="CLIENT_CERTIFICATE_NO"/>
        <result property="caseTimes" column="CASE_TIMES"/>
        <result property="areaCode" column="AREA_CODE"/>
        <result property="nationCode" column="NATION_CODE"/>
        <result property="professionMaximumCode" column="PROFESSION_MAXIMUM_CODE"/>
        <result property="professionMediumCode" column="PROFESSION_MEDIUM_CODE"/>
        <result property="professionMinimumCode" column="PROFESSION_MINIMUM_CODE"/>
        <result property="certificateEffectiveDate" column="CERTIFICATE_EFFECTIVE_DATE"/>
        <result property="certificateExpireDate" column="CERTIFICATE_EXPIRE_DATE"/>
        <result property="gender" column="GENDER"/>
        <result property="incomeCode" column="INCOME_CODE"/>
        <result property="incomeText" column="INCOME_TEXT"/>
        <result property="company" column="COMPANY"/>
        <result property="address" column="ADDRESS"/>
        <result property="createdBy" column="CREATED_BY"/>
        <result property="updatedBy" column="UPDATED_BY"/>
        <result property="createdDate" column="CREATED_DATE"/>
        <result property="updatedDate" column="UPDATED_DATE"/>
        <result property="customerNo" column="CUSTOMER_NO"/>
    </resultMap>

    <!--保存反洗钱信息 -->
    <insert id="addClmsAntiMoneyLaunderingInfo" parameterType="com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoDto">
        insert into clms_anti_money_laundering_info
        (ID_CLMS_ANTI_MONEY_LAUNDERING_INFO,
         REPORT_NO,
         CLIENT_NAME,
         CLIENT_CERTIFICATE_TYPE,
         CLIENT_CERTIFICATE_NO,
         CASE_TIMES,
         AREA_CODE,
         NATION_CODE,
         PROFESSION_MAXIMUM_CODE,
         PROFESSION_MEDIUM_CODE,
         PROFESSION_MINIMUM_CODE,
         CERTIFICATE_EFFECTIVE_DATE,
         CERTIFICATE_EXPIRE_DATE,
         GENDER,
         INCOME_CODE,
         INCOME_TEXT,
         COMPANY,
         ADDRESS,
         CREATED_BY,
         CREATED_DATE,
         UPDATED_BY,
         UPDATED_DATE,
        CUSTOMER_NO,
        IS_EFFECTIVE)
        values(
               REPLACE(UUID(),'-',''),
               #{reportNo,jdbcType=VARCHAR},
               #{clientName,jdbcType=VARCHAR},
               #{clientCertificateType,jdbcType=VARCHAR},
               #{clientCertificateNo,jdbcType=VARCHAR},
               #{caseTimes,jdbcType=DECIMAL},
               #{areaCode,jdbcType=VARCHAR},
               #{nationCode,jdbcType=VARCHAR},
               #{professionMaximumCode,jdbcType=VARCHAR},
               #{professionMediumCode,jdbcType=VARCHAR},
               #{professionMinimumCode,jdbcType=VARCHAR},
               #{certificateEffectiveDate,jdbcType=VARCHAR},
               #{certificateExpireDate,jdbcType=VARCHAR},
               #{gender,jdbcType=VARCHAR},
               #{incomeCode,jdbcType=VARCHAR},
               #{incomeText,jdbcType=VARCHAR},
               #{company,jdbcType=VARCHAR},
               #{address,jdbcType=VARCHAR},
               #{createdBy,jdbcType=VARCHAR},
               sysdate(),
               #{updatedBy,jdbcType=VARCHAR},
               sysdate(),
                #{customerNo},
                "Y")
    </insert>
    <select id="getClmsAntiMoneyLaunderingInfoById" resultType="com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoDto">
        select
        t.ID_CLMS_ANTI_MONEY_LAUNDERING_INFO,
        t.REPORT_NO,
        t.CLIENT_NAME,
        t.CLIENT_CERTIFICATE_TYPE,
        t.CLIENT_CERTIFICATE_NO,
        t.CASE_TIMES,
        t.AREA_CODE,
        t.NATION_CODE,
        t.PROFESSION_MAXIMUM_CODE,
        t.PROFESSION_MEDIUM_CODE,
        t.PROFESSION_MINIMUM_CODE,
        t.CERTIFICATE_EFFECTIVE_DATE,
        t.CERTIFICATE_EXPIRE_DATE,
        t.GENDER,
        t.INCOME_CODE,
        t.INCOME_TEXT,
        t.COMPANY,
        t.ADDRESS,
        t.CREATED_BY,
        t.CREATED_DATE,
        t.UPDATED_BY,
        t.UPDATED_DATE,
        t.CUSTOMER_NO
        from
        clms_anti_money_laundering_info t
        where
        t.ID_CLMS_ANTI_MONEY_LAUNDERING_INFO =#{idClmsAntiMoneyLaunderingInfo}
    </select>
    <!--查询反洗钱信息 根据客户号，报案号，赔付次数查询-->
    <select id="getClmsAntiMoneyLaunderingInfo" parameterType="com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoDto" resultMap="clmsAntiMoneyLaunderingInfo">
        select
            t.ID_CLMS_ANTI_MONEY_LAUNDERING_INFO,
            t.REPORT_NO,
            t.CLIENT_NAME,
            t.CLIENT_CERTIFICATE_TYPE,
            t.CLIENT_CERTIFICATE_NO,
            t.CASE_TIMES,
            t.AREA_CODE,
            t.NATION_CODE,
            t.PROFESSION_MAXIMUM_CODE,
            t.PROFESSION_MEDIUM_CODE,
            t.PROFESSION_MINIMUM_CODE,
            t.CERTIFICATE_EFFECTIVE_DATE,
            t.CERTIFICATE_EXPIRE_DATE,
            t.GENDER,
            t.INCOME_CODE,
            t.INCOME_TEXT,
            t.COMPANY,
            t.ADDRESS,
            t.CREATED_BY,
            t.CREATED_DATE,
            t.UPDATED_BY,
            t.UPDATED_DATE,
            t.CUSTOMER_NO
        from
            clms_anti_money_laundering_info t
        where
              t.REPORT_NO = #{reportNo}
            and
             t.CUSTOMER_NO=#{customerNo}
            and
              t.CASE_TIMES = #{caseTimes}
            and
              t.IS_EFFECTIVE = 'Y'
    </select>

    <!-- 修改反洗钱信息 -->
    <update id="updateClmsAntiMoneyLaunderingInfo" parameterType="com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoDto">
        <![CDATA[
        update
            clms_anti_money_laundering_info t
        set
            t.REPORT_NO = #{reportNo,jdbcType=VARCHAR},
            t.CLIENT_NAME = #{clientName,jdbcType=VARCHAR},
            t.CLIENT_CERTIFICATE_TYPE = #{clientCertificateType,jdbcType=VARCHAR},
            t.CLIENT_CERTIFICATE_NO = #{clientCertificateNo,jdbcType=VARCHAR},
            t.AREA_CODE = #{areaCode,jdbcType=VARCHAR},
            t.NATION_CODE =  #{nationCode,jdbcType=VARCHAR},
            t.PROFESSION_MAXIMUM_CODE = #{professionMaximumCode,jdbcType=VARCHAR},
            t.PROFESSION_MEDIUM_CODE = #{professionMediumCode,jdbcType=VARCHAR},
            t.PROFESSION_MINIMUM_CODE = #{professionMinimumCode,jdbcType=VARCHAR},
            t.CERTIFICATE_EFFECTIVE_DATE = #{certificateEffectiveDate,jdbcType=VARCHAR},
            t.CERTIFICATE_EXPIRE_DATE = #{certificateExpireDate,jdbcType=VARCHAR},
            t.GENDER = #{gender,jdbcType=VARCHAR},
            t.INCOME_CODE = #{incomeCode,jdbcType=VARCHAR},
            t.INCOME_TEXT = #{incomeText,jdbcType=VARCHAR},
            t.COMPANY =  #{company,jdbcType=VARCHAR},
            t.ADDRESS =  #{address,jdbcType=VARCHAR},
            t.UPDATED_BY =  #{updatedBy,jdbcType=VARCHAR},
            t.UPDATED_DATE = sysdate(),
            t.CUSTOMER_NO = #{customerNo}
        where
            t.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
          and
            t.CLIENT_NAME = #{clientName,jdbcType=VARCHAR}
          and
            t.CLIENT_CERTIFICATE_NO = #{clientCertificateNo,jdbcType=VARCHAR}
          and
            t.CLIENT_CERTIFICATE_TYPE = #{clientCertificateType,jdbcType=VARCHAR}
          and
            t.CASE_TIMES = #{caseTimes,jdbcType=DECIMAL}
        ]]>
    </update>

    <update id="invalidClmsAntiMoney" >
        update clms_anti_money_laundering_info set IS_EFFECTIVE = 'N'
        where
        REPORT_NO = #{reportNo}
        and
        CASE_TIMES = #{caseTimes}
    </update>
    <!--删除反洗钱信息-->
    <delete id="deleteClmsAntiMoneyLaunderingInfo" parameterType="com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoDto">
        delete from clms_anti_money_laundering_info t
        where
            t.REPORT_NO = #{reportNo}
          and
           t.CUSTOMER_NO=#{customerNo}
          and
          t.CASE_TIMES = #{caseTimes}
    </delete>

    <!--根据客户号，报案号，赔付次数 删除反洗钱信息-->
    <delete id="deleteClmsAmlInfoByCustomerNo" parameterType="com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoDto">
        delete from clms_anti_money_laundering_info t
        where
        t.REPORT_NO = #{reportNo}
        and
        t.CUSTOMER_NO = #{customerNo}
        and
        t.CASE_TIMES = #{caseTimes}
    </delete>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        INSERT INTO CLMS_ANTI_MONEY_LAUNDERING_INFO (
            ID_CLMS_ANTI_MONEY_LAUNDERING_INFO,
            REPORT_NO,
            CASE_TIMES,
            CLIENT_NAME,
            CLIENT_CERTIFICATE_TYPE,
            CLIENT_CERTIFICATE_NO,
            AREA_CODE,
            NATION_CODE,
            PROFESSION_MAXIMUM_CODE,
            PROFESSION_MEDIUM_CODE,
            PROFESSION_MINIMUM_CODE,
            CERTIFICATE_EFFECTIVE_DATE,
            CERTIFICATE_EXPIRE_DATE,
            GENDER,
            INCOME_CODE,
            INCOME_TEXT,
            COMPANY,
            ADDRESS,
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            CUSTOMER_NO,
            IS_EFFECTIVE
        )
        SELECT
            REPLACE(UUID(),'-',''),
            REPORT_NO,
            #{reopenCaseTimes},
            CLIENT_NAME,
            CLIENT_CERTIFICATE_TYPE,
            CLIENT_CERTIFICATE_NO,
            AREA_CODE,
            NATION_CODE,
            PROFESSION_MAXIMUM_CODE,
            PROFESSION_MEDIUM_CODE,
            PROFESSION_MINIMUM_CODE,
            CERTIFICATE_EFFECTIVE_DATE,
            CERTIFICATE_EXPIRE_DATE,
            GENDER,
            INCOME_CODE,
            INCOME_TEXT,
            COMPANY,
            ADDRESS,
            #{userId},
            NOW(),
            #{userId},
            NOW(),
            CUSTOMER_NO,
            IS_EFFECTIVE
        FROM CLMS_ANTI_MONEY_LAUNDERING_INFO
        WHERE REPORT_NO=#{reportNo}
        AND CASE_TIMES=#{caseTimes}
        AND IS_EFFECTIVE='Y'
    </insert>

    <!--通过主键修改数据-->
    <update id="updateClmsAntiMoneyLaunderingInfoById">
        update clms_anti_money_laundering_info
        <set>
            <if test="clientName != null and clientName != ''">
                CLIENT_NAME = #{clientName,jdbcType=VARCHAR},
            </if>
            <if test="clientCertificateType != null and clientCertificateType != ''">
                CLIENT_CERTIFICATE_TYPE = #{clientCertificateType,jdbcType=VARCHAR},
            </if>
            <if test="clientCertificateNo != null and clientCertificateNo != ''">
                CLIENT_CERTIFICATE_NO = #{clientCertificateNo,jdbcType=VARCHAR},
            </if>
            <if test="areaCode != null and areaCode != ''">
                AREA_CODE = #{areaCode,jdbcType=VARCHAR},
            </if>
            <if test="nationCode != null and nationCode != ''">
                NATION_CODE =  #{nationCode,jdbcType=VARCHAR},
            </if>
            <if test="professionMaximumCode != null and professionMaximumCode !=''">
                PROFESSION_MAXIMUM_CODE = #{professionMaximumCode,jdbcType=VARCHAR},
            </if>
            <if test="professionMediumCode != null and professionMediumCode != ''">
                PROFESSION_MEDIUM_CODE = #{professionMediumCode,jdbcType=VARCHAR},
            </if>
            <if test="professionMinimumCode != null and professionMinimumCode != ''">
                PROFESSION_MINIMUM_CODE = #{professionMinimumCode,jdbcType=VARCHAR},
            </if>
            <if test="certificateEffectiveDate != null ">
                CERTIFICATE_EFFECTIVE_DATE = #{certificateEffectiveDate,jdbcType=VARCHAR},
            </if>
            <if test="certificateExpireDate != null ">
                CERTIFICATE_EXPIRE_DATE = #{certificateExpireDate,jdbcType=VARCHAR},
            </if>
            <if test="gender != null  and gender !=''">
                GENDER = #{gender,jdbcType=VARCHAR},
            </if>
            <if test="incomeCode != null and incomeCode != ''">
                INCOME_CODE = #{incomeCode,jdbcType=VARCHAR},
            </if>
            <if test="incomeText != null and incomeText != ''">
                INCOME_TEXT = #{incomeText,jdbcType=VARCHAR},
            </if>
            <if test="company != null and company != ''">
                COMPANY =  #{company,jdbcType=VARCHAR},
            </if>
            <if test="address != null  and address !=''">
                ADDRESS =  #{address,jdbcType=VARCHAR},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                UPDATED_BY =  #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null ">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="customerNo != null and customerNo != ''">
                CUSTOMER_NO = #{customerNo}
            </if>
        </set>
        where ID_CLMS_ANTI_MONEY_LAUNDERING_INFO = #{idClmsAntiMoneyLaunderingInfo}
    </update>
    <!-- 根据姓名，证件类型，证件号查询反洗钱信息-->
    <select id="getAmlByNameAndCardTypeAndCardNo" parameterType="com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoDto" resultType="com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoDto">
        select  CLIENT_NAME,CLIENT_CERTIFICATE_TYPE,CLIENT_CERTIFICATE_NO,AREA_CODE,NATION_CODE,PROFESSION_MAXIMUM_CODE,PROFESSION_MEDIUM_CODE,PROFESSION_MINIMUM_CODE,
        CERTIFICATE_EFFECTIVE_DATE,CERTIFICATE_EXPIRE_DATE,GENDER,INCOME_CODE,INCOME_TEXT,COMPANY,ADDRESS
        from clms_anti_money_laundering_info
        where CLIENT_CERTIFICATE_TYPE=#{clientCertificateType}
        and CLIENT_CERTIFICATE_NO=#{clientCertificateNo}
        order by updated_date desc
        limit 1
    </select>

    <select id="getAmlByCustomerNo" parameterType="com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoDto" resultType="com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoDto">
        select  CLIENT_NAME,CLIENT_CERTIFICATE_TYPE,CLIENT_CERTIFICATE_NO,AREA_CODE,NATION_CODE,PROFESSION_MAXIMUM_CODE,PROFESSION_MEDIUM_CODE,PROFESSION_MINIMUM_CODE,
        CERTIFICATE_EFFECTIVE_DATE,CERTIFICATE_EXPIRE_DATE,GENDER,INCOME_CODE,INCOME_TEXT,COMPANY,ADDRESS
        from clms_anti_money_laundering_info
        where CUSTOMER_NO=#{customerNo}
        order by updated_date desc
        limit 1
    </select>
</mapper>