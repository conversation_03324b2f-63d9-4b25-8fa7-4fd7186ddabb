server:
  port: 48913
  servlet:
    context-path: /claim

spring:
  logging:
    config: classpath:logback-spring.xml
  application:
    name: ncbs-claim
  mybatis-plus:
    mapper-locations: classpath*:mapper/**/*.xml
  cloud:
    nacos:
      config:
        server-addr: 30.184.76.78:48913
        namespace: 7bce1a28-dcce-4136-a936-491acf312846
        username: nacos
        password: cb3pwd58
        file-extension: yaml
      discovery:
        server-addr: 30.184.76.78:48913
        namespace: 7bce1a28-dcce-4136-a936-491acf312846
        username: nacos
        password: cb3pwd58
    service-registry:
      auto-registration:
        enabled: true
  main:
    allow-circular-references: true