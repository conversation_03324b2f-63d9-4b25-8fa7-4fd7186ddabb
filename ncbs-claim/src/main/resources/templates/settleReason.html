${dutyDetailName}: 合计赔付${autoSettleAmount}元
<@compress single_line=true>
<#if everySetttleList?? && (everySetttleList?size > 0)>
<#list everySetttleList as everySetttle>
${everySetttle.strBillDate}号理算金额：
<#if everySetttle.templateDTOs?? && (everySetttle.templateDTOs?size > 0)>
<#list everySetttle.templateDTOs as templateDto>
<#if templateDto.twoFlag?? && (templateDto.twoFlag=='2')>
${templateDto.medicalFormulaData}+${templateDto.formulaData}=${templateDto.formulaSettleAmount}
<#if templateDto.noLimtsettleFlag?? && (templateDto.noLimtsettleFlag=='1')>(理算金额超日限额本日按照限额给付${templateDto.sum}元)</#if></br>
</#if>
<#if templateDto.twoFlag?? && (templateDto.twoFlag=='1')>
<#if templateDto.medicalSettleFlag?? && (templateDto.medicalSettleFlag=='Y')>
${templateDto.medicalFormulaData}=${templateDto.medicalAutoSettleAmount}
<#if templateDto.noLimtsettleFlag?? && (templateDto.noLimtsettleFlag=='1')>(理算金额超日限额本日按照限额给付${templateDto.sum}元)</#if></br>
<#else>
${templateDto.formulaData}=${templateDto.autoSettleAmount}
<#if templateDto.noLimtsettleFlag?? && (templateDto.noLimtsettleFlag=='1')>(理算金额超日限额本日按照限额给付${templateDto.sum}元)</#if></br>
</#if>
</#if>
</#list>
</#if>
</#list>
</#if>
</@compress>
