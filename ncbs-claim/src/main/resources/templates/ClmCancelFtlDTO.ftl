<?xml version="1.0" encoding="UTF-8"?>
<ROOT>
  <CONFIG>
    <JOB_TYPE>${IDGXmlPrintDTO.configDTO.jobType!}</JOB_TYPE>
    <CLASSES_CODE>${IDGXmlPrintDTO.configDTO.classesCode!}</CLASSES_CODE>
    <FILE_ID>${IDGXmlPrintDTO.configDTO.fileId!}</FILE_ID>
    <BRANCH_ID>${IDGXmlPrintDTO.configDTO.branchId!}</BRANCH_ID>
    <x>50</x>
    <y>50</y>
    <pageno>1</pageno>
    <flag>${IDGXmlPrintDTO.configDTO.flag!}</flag>
  </CONFIG>
  <DATASET>
    <InsuredName>${IDGXmlPrintDTO.dataSet.insuredName!}</InsuredName>
    <ReportNo>${IDGXmlPrintDTO.dataSet.reportNo!}</ReportNo>
    <CertificateNo>${IDGXmlPrintDTO.dataSet.certificateNo!}</CertificateNo>
    <AccidentDate>${IDGXmlPrintDTO.dataSet.accidentDate!}</AccidentDate>
    <AccidentTime>${IDGXmlPrintDTO.dataSet.accidentTime!}</AccidentTime>
    <EndCaseDate>${IDGXmlPrintDTO.dataSet.endCaseDate!}</EndCaseDate>
    <PolicyNo>${IDGXmlPrintDTO.dataSet.policyNo!}</PolicyNo>
    <EndYear>${IDGXmlPrintDTO.dataSet.endYear!}</EndYear>
    <EndMonth>${IDGXmlPrintDTO.dataSet.endMonth!}</EndMonth>
    <EndDay>${IDGXmlPrintDTO.dataSet.endDay!}</EndDay>
    <PolicyInfos>
<#if IDGXmlPrintDTO.dataSet.policyInfos?? && (IDGXmlPrintDTO.dataSet.policyInfos?size > 0) >
<#list IDGXmlPrintDTO.dataSet.policyInfos as PolicyInfo>
      <PolicyInfo>
        <PolicyNo>${PolicyInfo.policyNo!}</PolicyNo>
        <DutyName>${PolicyInfo.dutyName!}</DutyName>
        <PolicyTotalAmount>${PolicyInfo.policyTotalAmount!}</PolicyTotalAmount>
        <TotalPayAmount>${PolicyInfo.totalPayAmount!}</TotalPayAmount>
        <RemainAmount>${PolicyInfo.remainAmount!}</RemainAmount>
      </PolicyInfo>
</#list>
</#if>
    </PolicyInfos>
    <RefuseReson>${IDGXmlPrintDTO.dataSet.refuseReson!}</RefuseReson>
  </DATASET>
</ROOT>