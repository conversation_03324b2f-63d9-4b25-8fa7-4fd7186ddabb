<@compress single_line=true>
    ${dutyDetailName}: 合计赔付${autoSettleAmount}元</br>
    <#if everySetttleList?? && (everySetttleList?size > 0)>
        <#list everySetttleList as everySetttle>
            ${everySetttle.strBillDate}号理算金额：
            <#if everySetttle.effectiveFlag?? && (everySetttle.effectiveFlag=='N')>
                发票不在保单有效期,理算金额=0.00</br>
            <#elseif everySetttle.waitFlag?? && (everySetttle.waitFlag=='Y')>
                发票在等待期,理算金额=0.00</br>
            <#elseif everySetttle.exceedMothPayDays?? && (everySetttle.exceedMothPayDays=='Y')>
                发票超每月赔付天数,理算金额=0.00</br>
            <#elseif everySetttle.exceedYearlyPayDays?? && (everySetttle.exceedYearlyPayDays=='Y')>
                发票超年度赔付天数，理算金额=0.00</br>
            <#else>
                <#if everySetttle.templateDto??>
                    <#if everySetttle.templateDto.twoFlag?? && (everySetttle.templateDto.twoFlag=='2')>
                        ${everySetttle.templateDto.medicalFormulaData}+${everySetttle.templateDto.formulaData}
                        <#if (everySetttle.templateDto.lessThanRemitAmountFlag?? && everySetttle.templateDto.lessThanRemitAmountFlag=='Y' && everySetttle.templateDto.remitAmount?? && everySetttle.templateDto.remitAmount!='0.00')
                        || (everySetttle.templateDto.medicalLessThanRemitAmountFlag?? && everySetttle.templateDto.medicalLessThanRemitAmountFlag=='Y' && everySetttle.templateDto.medicalRemitAmount?? && everySetttle.templateDto.medicalRemitAmount!='0.00')>
                        <#else>
                            =${everySetttle.templateDto.formulaSettleAmount}
                        </#if>
                        <#if everySetttle.exceedMonthLimit??>
                            <#if everySetttle.exceedMonthLimit?? && everySetttle.exceedMonthLimit=='0'>(剩余月限额为0赔付金额=0.00元)</#if>
                            <#if everySetttle.exceedMonthLimit?? && everySetttle.exceedMonthLimit=='1'>(超月限额赔付金额按剩余月限额赔付=${everySetttle.exceedMonthLimitAmount}元)</#if>
                            </br>
                        <#elseif everySetttle.exceedDayLimit??>
                            <#if everySetttle.exceedDayLimit?? && everySetttle.exceedDayLimit=='0'>(剩余保单日限额为0赔付金额=0.00元)</#if>
                            <#if everySetttle.exceedDayLimit?? && everySetttle.exceedDayLimit=='1'>(超保单日限额赔付金额按剩余保单日限额赔付=${everySetttle.exceedDayLimitAmount}元)</#if>
                            </br>
                        <#elseif everySetttle.exceedDayOrgLimit??>
                            <#if everySetttle.exceedDayOrgLimit?? && everySetttle.exceedDayOrgLimit=='Y'>(超保单日限次，金额按${everySetttle.exceedDayOrgLimitAmount}元赔付)</#if>
                            </br>
                        <#elseif everySetttle.exceedMonthOrgLimit??>
                            <#if everySetttle.exceedMonthOrgLimit?? && everySetttle.exceedMonthOrgLimit=='Y'>(超保单月限次，金额按${everySetttle.exceedMonthOrgLimitAmount}元赔付)</#if>
                            </br>
                        <#else>
                            <#if everySetttle.templateDto.noLimtsettleFlag?? && (everySetttle.templateDto.noLimtsettleFlag=='1')>(理算金额超日限额本日按照限额给付${everySetttle.templateDto.sum}元)</#if>
                            <#if everySetttle.templateDto.settleZeroFlag?? && (everySetttle.templateDto.settleZeroFlag=='Y')>(当日赔付金额已超限额,本次按0元赔付)</#if>
                            </br>
                        </#if>

                    </#if>
                    <#if everySetttle.templateDto.twoFlag?? && (everySetttle.templateDto.twoFlag=='1')>
                        <#if everySetttle.templateDto.medicalSettleFlag?? && (everySetttle.templateDto.medicalSettleFlag=='Y')>
                            ${everySetttle.templateDto.medicalFormulaData}
                            <#if (everySetttle.templateDto.lessThanRemitAmountFlag?? && everySetttle.templateDto.lessThanRemitAmountFlag=='Y' && everySetttle.templateDto.remitAmount?? && everySetttle.templateDto.remitAmount!='0.00')
                            || (everySetttle.templateDto.medicalLessThanRemitAmountFlag?? && everySetttle.templateDto.medicalLessThanRemitAmountFlag=='Y' && everySetttle.templateDto.medicalRemitAmount?? && everySetttle.templateDto.medicalRemitAmount!='0.00')>
                            <#else>
                                =${everySetttle.templateDto.medicalAutoSettleAmount}
                            </#if>
                            <#if everySetttle.exceedMonthLimit??>
                                <#if everySetttle.exceedMonthLimit?? && everySetttle.exceedMonthLimit=='0'>(剩余月限额为0赔付金额=0.00元)</#if>
                                <#if everySetttle.exceedMonthLimit?? && everySetttle.exceedMonthLimit=='1'>(超月限额赔付金额按剩余月限额赔付=${everySetttle.exceedMonthLimitAmount}元)</#if></br>
                            <#elseif everySetttle.exceedDayLimit??>
                                <#if everySetttle.exceedDayLimit?? && everySetttle.exceedDayLimit=='0'>(剩余保单日限额为0赔付金额=0.00元)</#if>
                                <#if everySetttle.exceedDayLimit?? && everySetttle.exceedDayLimit=='1'>(超保单日限额赔付金额按剩余保单日限额赔付=${everySetttle.exceedDayLimitAmount}元)</#if>
                                </br>
                            <#elseif everySetttle.exceedDayOrgLimit??>
                                <#if everySetttle.exceedDayOrgLimit?? && everySetttle.exceedDayOrgLimit=='Y'>(超保单日限次，金额按${everySetttle.exceedDayOrgLimitAmount}元赔付)</#if>
                                </br>
                            <#elseif everySetttle.exceedMonthOrgLimit??>
                                <#if everySetttle.exceedMonthOrgLimit?? && everySetttle.exceedMonthOrgLimit=='Y'>(超保单月限次，金额按${everySetttle.exceedMonthOrgLimitAmount}元赔付)</#if>
                                </br>
                            <#else>
                                <#if everySetttle.templateDto.noLimtsettleFlag?? && (everySetttle.templateDto.noLimtsettleFlag=='1')>(理算金额超日限额本日按照限额给付${everySetttle.templateDto.sum}元)</#if>
                                <#if everySetttle.templateDto.settleZeroFlag?? && (everySetttle.templateDto.settleZeroFlag=='Y')>(当日赔付金额已超限额,本次按0元赔付)</#if>
                                </br>
                            </#if>

                        <#else>
                            ${everySetttle.templateDto.formulaData}=${everySetttle.templateDto.autoSettleAmount}
                            <#if everySetttle.exceedMonthLimit??>
                                <#if everySetttle.exceedMonthLimit?? && everySetttle.exceedMonthLimit=='0'>(剩余月限额为0赔付金额=0.00元)</#if>
                                <#if everySetttle.exceedMonthLimit?? && everySetttle.exceedMonthLimit=='1'>(超月限额赔付金额按剩余月限额赔付=${everySetttle.exceedMonthLimitAmount}元)</#if></br>
                            <#elseif everySetttle.exceedDayLimit??>
                                <#if everySetttle.exceedDayLimit?? && everySetttle.exceedDayLimit=='0'>(剩余保单日限额为0赔付金额=0.00元)</#if>
                                <#if everySetttle.exceedDayLimit?? && everySetttle.exceedDayLimit=='1'>(超保单日限额赔付金额按剩余保单日限额赔付=${everySetttle.exceedDayLimitAmount}元)</#if>
                                </br>
                            <#elseif everySetttle.exceedDayOrgLimit??>
                                <#if everySetttle.exceedDayOrgLimit?? && everySetttle.exceedDayOrgLimit=='Y'>(超保单日限次，金额按${everySetttle.exceedDayOrgLimitAmount}元赔付)</#if>
                                </br>
                            <#elseif everySetttle.exceedMonthOrgLimit??>
                                <#if everySetttle.exceedMonthOrgLimit?? && everySetttle.exceedMonthOrgLimit=='Y'>(超保单月限次，金额按${everySetttle.exceedMonthOrgLimitAmount}元赔付)</#if>
                                </br>
                            <#else>
                                <#if everySetttle.templateDto.noLimtsettleFlag?? && (everySetttle.templateDto.noLimtsettleFlag=='1')>(理算金额超日限额本日按照限额给付${everySetttle.templateDto.sum}元)</#if>
                                <#if everySetttle.templateDto.settleZeroFlag?? && (everySetttle.templateDto.settleZeroFlag=='Y')>(当日赔付金额已超限额,本次按0元赔付)</#if>
                                </br>
                            </#if>
                        </#if>
                    </#if>
                </#if>
            </#if>
        </#list>
    </#if>
    <#if beInHospitalFlag?? && (beInHospitalFlag=='Y')>住院费用无可赔付责任</br></#if>
    <#if notice?? && (notice!='')>${notice}</#if>

</@compress>
