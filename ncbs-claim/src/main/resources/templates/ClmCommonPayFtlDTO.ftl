<?xml version="1.0" encoding="UTF-8"?>
<ROOT>
  <CONFIG>
    <JOB_TYPE>${IDGXmlPrintDTO.configDTO.jobType!}</JOB_TYPE>
    <CLASSES_CODE>${IDGXmlPrintDTO.configDTO.classesCode!}</CLASSES_CODE>
    <FILE_ID>${IDGXmlPrintDTO.configDTO.fileId!}</FILE_ID>
    <BRANCH_ID>${IDGXmlPrintDTO.configDTO.branchId!}</BRANCH_ID>
    <x>50</x>
    <y>50</y>
    <pageno>1</pageno>
    <flag>${IDGXmlPrintDTO.configDTO.flag!}</flag>
  </CONFIG>
  <DATASET>
    <InsuredName>${IDGXmlPrintDTO.dataSet.insuredName!}</InsuredName>
    <ReportNo>${IDGXmlPrintDTO.dataSet.reportNo!}</ReportNo>
    <CertificateNo>${IDGXmlPrintDTO.dataSet.certificateNo!}</CertificateNo>
    <AccidentDate>${IDGXmlPrintDTO.dataSet.accidentDate!}</AccidentDate>
    <AccidentTime>${IDGXmlPrintDTO.dataSet.accidentTime!}</AccidentTime>
    <EndCaseDate>${IDGXmlPrintDTO.dataSet.endCaseDate!}</EndCaseDate>
    <PolicyNo>${IDGXmlPrintDTO.dataSet.policyNo!}</PolicyNo>
    <EndYear>${IDGXmlPrintDTO.dataSet.endYear!}</EndYear>
    <EndMonth>${IDGXmlPrintDTO.dataSet.endMonth!}</EndMonth>
    <EndDay>${IDGXmlPrintDTO.dataSet.endDay!}</EndDay>
    <TotalBillAmount>${IDGXmlPrintDTO.dataSet.totalBillAmount!}</TotalBillAmount>
    <TotalReasonableAmount>${IDGXmlPrintDTO.dataSet.totalReasonableAmount!}</TotalReasonableAmount>
    <TotalOwnAmount>${IDGXmlPrintDTO.dataSet.totalOwnAmount!}</TotalOwnAmount>
    <TotalPartOwnAmount>${IDGXmlPrintDTO.dataSet.totalPartOwnAmount!}</TotalPartOwnAmount>
    <TotalImmoderateAmount>${IDGXmlPrintDTO.dataSet.totalImmoderateAmount!}</TotalImmoderateAmount>
    <TotalThirdPartyAmount>${IDGXmlPrintDTO.dataSet.totalThirdPartyAmount!}</TotalThirdPartyAmount>
    <PolicyInfos>
<#if IDGXmlPrintDTO.dataSet.policyInfos?? && (IDGXmlPrintDTO.dataSet.policyInfos?size > 0) >
<#list IDGXmlPrintDTO.dataSet.policyInfos as PolicyInfo>
      <PolicyInfo>
        <PolicyNo>${PolicyInfo.policyNo!}</PolicyNo>
        <DutyName>${PolicyInfo.dutyName!}</DutyName>
        <PolicyTotalAmount>${PolicyInfo.policyTotalAmount!}</PolicyTotalAmount>
        <TotalPayAmount>${PolicyInfo.totalPayAmount!}</TotalPayAmount>
        <RemainAmount>${PolicyInfo.remainAmount!}</RemainAmount>
      </PolicyInfo>
</#list>
</#if>
    </PolicyInfos>
    <NeedPayAmount>${IDGXmlPrintDTO.dataSet.needPayAmount!}</NeedPayAmount>
    <PayedAmount>${IDGXmlPrintDTO.dataSet.payedAmount!}</PayedAmount>
    <ActualPayAmount>${IDGXmlPrintDTO.dataSet.actualPayAmount!}</ActualPayAmount>
    <ReceiptInfos>
<#if IDGXmlPrintDTO.dataSet.receiptInfos?? && (IDGXmlPrintDTO.dataSet.receiptInfos?size > 0) >
<#list IDGXmlPrintDTO.dataSet.receiptInfos as ReceiptInfo>
      <ReceiptInfo>
        <ClientName>${ReceiptInfo.clientName!}</ClientName>
        <ReceiptAmount>${ReceiptInfo.receiptAmount!}</ReceiptAmount>
        <ClientBankName>${ReceiptInfo.clientBankName!}</ClientBankName>
        <ClientBankAccoount>${ReceiptInfo.clientBankAccoount!}</ClientBankAccoount>
      </ReceiptInfo>
</#list>
</#if>
    </ReceiptInfos>
    <CliamInfos>
<#if IDGXmlPrintDTO.dataSet.cliamInfos?? && (IDGXmlPrintDTO.dataSet.cliamInfos?size > 0) >
<#list IDGXmlPrintDTO.dataSet.cliamInfos as CliamInfo>
      <CliamInfo>
        <ApplicationName>${CliamInfo.applicationName!}</ApplicationName>
        <PolicyNo>${CliamInfo.policyNo!}</PolicyNo>
        <DutyName>${CliamInfo.dutyName!}</DutyName>
        <PayAmount>${CliamInfo.payAmount!}</PayAmount>
        <PaymentBasis>${CliamInfo.paymentBasis!}</PaymentBasis>
      </CliamInfo>
</#list>
</#if>
    </CliamInfos>
    <FeeDeductionInfos>
<#if IDGXmlPrintDTO.dataSet.feeDeductionInfos?? && (IDGXmlPrintDTO.dataSet.feeDeductionInfos?size > 0) >
<#list IDGXmlPrintDTO.dataSet.feeDeductionInfos as FeeDeductionInfo>
      <FeeDeductionInfo>
        <SerialNo>${FeeDeductionInfo.serialNo!}</SerialNo>
        <ReceiptNo>${FeeDeductionInfo.receiptNo!}</ReceiptNo>
        <VisitDate>${FeeDeductionInfo.visitDate!}</VisitDate>
        <VisitType>${FeeDeductionInfo.visitType!}</VisitType>
        <VisitHospital>${FeeDeductionInfo.visitHospital!}</VisitHospital>
        <BillAmount>${FeeDeductionInfo.billAmount!}</BillAmount>
        <ReasonableAmount>${FeeDeductionInfo.reasonableAmount!}</ReasonableAmount>
        <OwnAmount>${FeeDeductionInfo.ownAmount!}</OwnAmount>
        <PartOwnAmount>${FeeDeductionInfo.partOwnAmount!}</PartOwnAmount>
        <ImmoderateAmount>${FeeDeductionInfo.immoderateAmount!}</ImmoderateAmount>
        <ThirdPartyAmount>${FeeDeductionInfo.thirdPartyAmount!}</ThirdPartyAmount>
      </FeeDeductionInfo>
</#list>
</#if>
    </FeeDeductionInfos>
  </DATASET>
</ROOT>