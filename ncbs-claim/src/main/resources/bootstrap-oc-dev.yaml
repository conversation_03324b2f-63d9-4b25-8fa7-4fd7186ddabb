spring:
  application:
    name: ncbs-claim
  cloud:
    nacos:
      config:
        server-addr: 10.20.0.38:8848
        namespace: 7e513f91-13ac-4b95-a3f1-a6129d2898d7
        group: DEFAULT_GROUP
        username: ncbs_nbs_develop
        password: N5r%JwjK
        file-extension: yaml
      discovery:
        server-addr: 10.20.0.38:8848
        namespace: 7e513f91-13ac-4b95-a3f1-a6129d2898d7
        group: DEFAULT_GROUP
        username: nacos
        password: nacos
  main:
    allow-circular-references: true

# keywords    加密关键词,同一类型字段用英文逗号分割
# type        加密关键词看到种类，已固定写死，不要改
# pre-length  字符串前半部分留几位明文
# suf-length  字符串后半部分留几位明文
sensitive:
  log-info:
    enable: true # 开启日志脱敏
    categories:
      - keywords: name,chineseName,thirdPartyName,injuredN<PERSON>,applyName,insuredName,clientName,payeeN<PERSON>,linkMan<PERSON><PERSON>,applicant<PERSON><PERSON>,accident<PERSON><PERSON>,reporter<PERSON><PERSON>,link<PERSON><PERSON>,sale<PERSON><PERSON><PERSON><PERSON>,applicant<PERSON><PERSON>,send<PERSON><PERSON>,reporter<PERSON><PERSON>,holder<PERSON><PERSON>,insuranceName,customercName,CUSTOMERCNAME # 加密关键词
        type: name
      - keywords: mail,email
        type: mail
      - keywords: mobile,phone,telephone,bileTelephone,linkManTelephone,linkPhone,mobileNo,clientMobile,reporterMobile,mobileNo,fixedTelephone
        type: mobile
      - keywords: address,addr
        type: address
      - keywords: cerNo,certificateNo,injuredCertificateNo,thirdPartyCertificateNo,injuredCertificateNo,applicantCertificateNo,clientCertificateNo,identifyNumber,IDENTIFYNUMBER
        type: certificateNo
      - keywords: bankNo,bankAccount,clientBankAccount
        type: bankNo
      - keywords: otherTypeDefine # 其他类型大家自己定义类型
        type: other
        pre-length: 6 # 其他类型大家自己定义展示 明文前面位数
        suf-length: 3 # 其他类型大家自己定义类型 明文后面位数