<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
	<conversionRule conversionWord="msg" converterClass="com.paic.ncbs.log.encryption.log.core.LogBackCoreConverter"/>
	<!-- 从配置文件取信息 -->
<!--	<springProperty scope="context" name="springAppName" source="spring.application.name"/>-->
	<springProperty scope="context" name="LOG_HOME" source="logging.path"/>
	<!-- 处理日志命名要求，必须application开头 -->
	<property name="springAppName" value="application"/>

	<!-- 定义打印格式 -->
	<property name="FILE_LOG_PATTERN"
			  value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%X{userId},%X{requestId},%X{traceId},%X{spanId}] %X{processId} [%thread] %logger{50} %msg%n" />
	<property name="CONSOLE_LOG_PATTERN"
			  value="%yellow(%date{yyyy-MM-dd HH:mm:ss}) |%highlight(%-5level[%X{userId},%X{requestId}]) |%blue(%thread) |%blue(%file:%line) |%green(%logger) |%cyan(%msg%n) "/>

	<!-- 定义常量log.maxHistory，日志最大的历史 30天 -->
	<property name="log.maxHistory" value="15"/>
	<!-- 定义日志根级别，默认是DEBUG级别，我们也可以主动去定义，在下面会用到-->
	<property name="log.level" value="INFO"/>
	<!-- 定义日志文件最大的大小-->
	<property name="log.maxSize" value="500MB" />

	<appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
		<!--此日志appender是为开发使用，只配置最底级别，控制台输出的日志级别是大于或等于此级别的日志信息-->
		<!-- 例如：如果此处配置了INFO级别，则后面其他位置即使配置了DEBUG级别的日志，也不会被输出 -->
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>INFO</level>
		</filter>
		<encoder>
			<Pattern>${CONSOLE_LOG_PATTERN}</Pattern>
			<!-- 设置字符集 -->
			<charset>UTF-8</charset>
		</encoder>
	</appender>

	<!-- 时间滚动输出 level为 DEBUG 日志 -->
	<appender name="DEBUG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<!-- 正在记录的日志文件的路径及文件名 -->
		<file>${LOG_HOME:-logs}/${springAppName}.log.DEBUG</file>
		<!--日志文件输出格式-->
		<encoder>
			<pattern>${FILE_LOG_PATTERN}</pattern>
			<charset>UTF-8</charset>
		</encoder>
		<!-- 日志记录器的滚动策略，按日期，按大小记录 -->
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<!-- 每天日志归档路径以及格式 -->
			<fileNamePattern>${LOG_HOME}/${springAppName}.log.DEBUG.%d{yyyy-MM-dd}.%3i</fileNamePattern>
			<maxFileSize>${log.maxSize}</maxFileSize>
			<!--日志文件保留天数-->
			<maxHistory>${log.maxHistory}</maxHistory>
		</rollingPolicy>
		<!-- 此日志文件只记录DEBUG级别的 -->
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>DEBUG</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>

	<!-- 时间滚动输出 level为 INFO 日志 -->
	<appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<!-- 正在记录的日志文件的路径及文件名 -->
		<file>${LOG_HOME:-logs}/${springAppName}.log.INFO</file>
		<!--日志文件输出格式-->
		<encoder>
			<pattern>${FILE_LOG_PATTERN}</pattern>
			<charset>UTF-8</charset>
		</encoder>
		<!-- 日志记录器的滚动策略，按日期，按大小记录 -->
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<!-- 每天日志归档路径以及格式 -->
			<fileNamePattern>${LOG_HOME}/${springAppName}.log.INFO.%d{yyyy-MM-dd}.%3i</fileNamePattern>
			<maxFileSize>${log.maxSize}</maxFileSize>
			<!--日志文件保留天数-->
			<maxHistory>${log.maxHistory}</maxHistory>
		</rollingPolicy>
		<!-- 此日志文件只记录info级别的 -->
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>INFO</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>

	<!-- 时间滚动输出 level为 WARN 日志 -->
	<appender name="WARN_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<!-- 正在记录的日志文件的路径及文件名 -->
		<file>${LOG_HOME:-logs}/${springAppName}.log.WARN</file>
		<!--日志文件输出格式-->
		<encoder>
			<pattern>${FILE_LOG_PATTERN}</pattern>
			<charset>UTF-8</charset>
		</encoder>
		<!-- 日志记录器的滚动策略，按日期，按大小记录 -->
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<!-- 每天日志归档路径以及格式 -->
			<fileNamePattern>${LOG_HOME}/${springAppName}.log.WARN.%d{yyyy-MM-dd}.%3i</fileNamePattern>
			<maxFileSize>${log.maxSize}</maxFileSize>
			<!--日志文件保留天数-->
			<maxHistory>${log.maxHistory}</maxHistory>
		</rollingPolicy>
		<!-- 此日志文件只记录WARN级别的 -->
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>WARN</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>

	<!-- 时间滚动输出 level为 ERROR 日志 -->
	<appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<!-- 正在记录的日志文件的路径及文件名 -->
		<file>${LOG_HOME:-logs}/${springAppName}.log.ERROR</file>
		<!--日志文件输出格式-->
		<encoder>
			<pattern>${FILE_LOG_PATTERN}</pattern>
			<charset>UTF-8</charset>
		</encoder>
		<!-- 日志记录器的滚动策略，按日期，按大小记录 -->
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<!-- 每天日志归档路径以及格式 -->
			<fileNamePattern>${LOG_HOME}/${springAppName}.log.ERROR.%d{yyyy-MM-dd}.%3i</fileNamePattern>
			<maxFileSize>${log.maxSize}</maxFileSize>
			<!--日志文件保留天数-->
			<maxHistory>${log.maxHistory}</maxHistory>
		</rollingPolicy>
		<!-- 此日志文件只记录ERROR级别的 -->
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>ERROR</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>

	<root level="${log.level}">
		<appender-ref ref="CONSOLE" />
		<appender-ref ref="DEBUG_FILE" />
		<appender-ref ref="INFO_FILE" />
		<appender-ref ref="WARN_FILE" />
		<appender-ref ref="ERROR_FILE" />
	</root>

</configuration>