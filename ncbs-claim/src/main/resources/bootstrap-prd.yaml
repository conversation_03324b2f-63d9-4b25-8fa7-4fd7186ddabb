spring:
  logging:
    config: classpath:logback-spring.xml
  application:
    name: ncbs-claim
  mybatis-plus:
    mapper-locations: classpath*:mapper/**/*.xml
  cloud:
    nacos:
      config:
        server-addr: ncbs-nacos01.ssprd.com:8848,ncbs-nacos02.ssprd.com:8848,ncbs-nacos03.ssprd.com:8848
        namespace: 373ba31a-a8e7-449f-bc6d-be99d761b1ab
        username: ncbs_nacos
        password: LnxKn2j3
        file-extension: yaml
      discovery:
        server-addr: ncbs-nacos01.ssprd.com:8848,ncbs-nacos02.ssprd.com:8848,ncbs-nacos03.ssprd.com:8848
        namespace: 373ba31a-a8e7-449f-bc6d-be99d761b1ab
        username: ncbs_nacos
        password: LnxKn2j3
    service-registry:
      auto-registration:
        enabled: true
  main:
    allow-circular-references: true

  # 日志脱敏
sensitive:
  log-info:
    enable: true # 开启日志脱敏
    categories:
      - keywords: name,chinese<PERSON>ame,thirdP<PERSON>y<PERSON>ame,injured<PERSON><PERSON>,apply<PERSON><PERSON>,insured<PERSON>ame,client<PERSON><PERSON>,payee<PERSON><PERSON>,link<PERSON>an<PERSON><PERSON>,applicant<PERSON><PERSON>,accident<PERSON>ame,reporterName,linkName,saleAgentName,applicantName,sendUser,reporterName,holderName,insuranceName,customercName,CUSTOMERCNAME # 加密关键词
        type: name
      - keywords: mail,email
        type: mail
      - keywords: mobile,phone,telephone,bileTelephone,linkManTelephone,linkPhone,mobileNo,clientMobile,reporterMobile,mobileNo,fixedTelephone
        type: mobile
      - keywords: address,addr
        type: address
      - keywords: cerNo,certificateNo,injuredCertificateNo,thirdPartyCertificateNo,injuredCertificateNo,applicantCertificateNo,clientCertificateNo,identifyNumber,IDENTIFYNUMBER
        type: certificateNo
      - keywords: bankNo,bankAccount,clientBankAccount
        type: bankNo
      - keywords: otherTypeDefine # 其他类型大家自己定义类型
        type: other
        pre-length: 6 # 其他类型大家自己定义展示 明文前面位数
        suf-length: 3 # 其他类型大家自己定义类型 明文后面位数