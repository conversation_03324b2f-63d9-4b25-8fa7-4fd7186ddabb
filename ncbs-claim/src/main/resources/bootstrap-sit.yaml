server:
  port: 8092
  servlet:
    context-path: /claim

spring:
  logging:
    config: classpath:logback-spring.xml
  application:
    name: ncbs-claim
  mybatis-plus:
    mapper-locations: classpath*:mapper/**/*.xml
  cloud:
    nacos:
      config:
        server-addr: ncbs-nacos01.sssit.com:8848,ncbs-nacos02.sssit.com:8848,ncbs-nacos03.sssit.com:8848
        namespace: 373ba31a-a8e7-449f-bc6d-be99d761b1ab
        username: nacos
        password: nacos
        file-extension: yaml
      discovery:
        server-addr: ncbs-nacos01.sssit.com:8848,ncbs-nacos02.sssit.com:8848,ncbs-nacos03.sssit.com:8848
        namespace: 373ba31a-a8e7-449f-bc6d-be99d761b1ab
        username: nacos
        password: nacos
    service-registry:
      auto-registration:
        enabled: true
  main:
    allow-circular-references: true