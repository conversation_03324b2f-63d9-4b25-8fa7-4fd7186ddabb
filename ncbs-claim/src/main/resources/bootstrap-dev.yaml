spring:
  logging:
    config: classpath:logback-spring.xml
  application:
    name: ncbs-claim
  mybatis-plus:
    mapper-locations: classpath*:mapper/**/*.xml
  cloud:
    nacos:
      config:
        server-addr: ncbs-nacos01.sssit.com:8848,ncbs-nacos02.sssit.com:8848,ncbs-nacos03.sssit.com:8848
        namespace: efd54a73-f36c-44be-bf38-ffb9706f3372
        username: nacos
        password: nacos
        file-extension: yaml
      discovery:
        server-addr: ncbs-nacos01.sssit.com:8848,ncbs-nacos02.sssit.com:8848,ncbs-nacos03.sssit.com:8848
        namespace: efd54a73-f36c-44be-bf38-ffb9706f3372
        username: nacos
        password: nacos
    service-registry:
      auto-registration:
        enabled: true
  main:
    allow-circular-references: true
  # 日志脱敏
sensitive:
  log-info:
    enable: true # 开启日志脱敏
    categories:
      - keywords: name,chineseName,thirdPartyName,injured<PERSON><PERSON>,apply<PERSON><PERSON>,insured<PERSON><PERSON>,client<PERSON><PERSON>,payee<PERSON><PERSON>,linkMan<PERSON>ame,applicant<PERSON><PERSON>,accident<PERSON><PERSON>,reporter<PERSON><PERSON>,link<PERSON><PERSON>,saleAgentName,applicantName,sendUser,reporterName,holderName,insuranceName,customercName,CUSTOMERCNAME # 加密关键词
        type: name
      - keywords: mail,email
        type: mail
      - keywords: mobile,phone,telephone,bileTelephone,linkManTelephone,linkPhone,mobileNo,clientMobile,reporterMobile,mobileNo,fixedTelephone
        type: mobile
      - keywords: address,addr
        type: address
      - keywords: cerNo,certificateNo,injuredCertificateNo,thirdPartyCertificateNo,injuredCertificateNo,applicantCertificateNo,clientCertificateNo,identifyNumber,IDENTIFYNUMBER
        type: certificateNo
      - keywords: bankNo,bankAccount,clientBankAccount
        type: bankNo
      - keywords: otherTypeDefine # 其他类型大家自己定义类型
        type: other
        pre-length: 6 # 其他类型大家自己定义展示 明文前面位数
        suf-length: 3 # 其他类型大家自己定义类型 明文后面位数