package com.paic.ncbs.claim.service.estimate.impl;

import com.google.common.collect.Lists;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyFormDTO;
import com.paic.ncbs.claim.service.estimate.EstimateService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.math.BigDecimal;
import java.util.Date;

@ExtendWith(SpringExtension.class)
@SpringBootTest
class EstimateServiceImplTest {

    @Autowired
    private EstimateService estimateService;

    @Test
    void getRegiterEstimateDataList() {
        EstimatePolicyFormDTO estimatePolicyForm = new EstimatePolicyFormDTO();
        estimatePolicyForm.setReportNo("aaaa");
        estimatePolicyForm.setCaseTimes(1);
        estimateService.getRegiterEstimateDataList(estimatePolicyForm);

    }

    @Test
    void saveEstimateByReportTrack() {
        EstimatePolicyFormDTO estimatePolicyFormDTO = new EstimatePolicyFormDTO();
        estimatePolicyFormDTO.setUnOfferAmountTips("");
        estimatePolicyFormDTO.setEstimateAmount(Lists.newArrayList());
        estimatePolicyFormDTO.setArbitrageFee(Lists.newArrayList());
        estimatePolicyFormDTO.setLawsuitFee(Lists.newArrayList());
        estimatePolicyFormDTO.setCommonEstimateFee(Lists.newArrayList());
        estimatePolicyFormDTO.setLawyerFee(Lists.newArrayList());
        estimatePolicyFormDTO.setExecuteFee(Lists.newArrayList());
        estimatePolicyFormDTO.setVerifyFee(Lists.newArrayList());
        estimatePolicyFormDTO.setAwardFee(Lists.newArrayList());
        estimatePolicyFormDTO.setDataResource("");
        estimatePolicyFormDTO.setEstimatePolicyList(Lists.newArrayList());
        estimatePolicyFormDTO.setAdjustingRemark("");
        estimatePolicyFormDTO.setReportNo("");
        estimatePolicyFormDTO.setCaseTimes(0);
        estimatePolicyFormDTO.setEstimateType("");
        estimatePolicyFormDTO.setOperateType("");
        estimatePolicyFormDTO.setIsAmend("");
        estimatePolicyFormDTO.setOldEstimateAmountSum(new BigDecimal("0"));
        estimatePolicyFormDTO.setNewEstimateAmountSum(new BigDecimal("0"));
        estimatePolicyFormDTO.setNewSubtractOldValue(new BigDecimal("0"));
        estimatePolicyFormDTO.setApprovalOpinions("");
        estimatePolicyFormDTO.setIdAhcsTrackInto("");
        estimatePolicyFormDTO.setFromPage("");
        estimatePolicyFormDTO.setNeedEstimateReview(false);
        estimatePolicyFormDTO.setRegisterUm("");
        estimatePolicyFormDTO.setRegisterName("");
        estimatePolicyFormDTO.setRegisterDate(new Date());
        estimatePolicyFormDTO.setUserId("");
        String loginUm = "TEST";
        estimateService.saveEstimateByReportTrack(estimatePolicyFormDTO,loginUm);

    }

}