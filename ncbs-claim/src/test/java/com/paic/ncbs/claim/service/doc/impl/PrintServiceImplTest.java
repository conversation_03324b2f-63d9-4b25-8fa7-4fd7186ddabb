package com.paic.ncbs.claim.service.doc.impl;


import com.paic.ncbs.claim.model.dto.doc.IDGXmlPrintDTO;
import com.paic.ncbs.claim.model.dto.doc.PrintFormalPayInfoDTO;
import com.paic.ncbs.claim.model.dto.print.ClmCollegiatePayFtlDTO;
import com.paic.ncbs.claim.model.vo.doc.PrintDutyPayVO;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

//@ExtendWith(SpringExtension.class)
//@SpringBootTest
class PrintServiceImplTest {

    @Autowired
    private PrintServiceImpl printService;

    @Test
    void getFormalPay() {
        PrintDutyPayVO printDutyPayVO = new PrintDutyPayVO();
    }

    @Test
    public void test_03() throws IOException, TemplateException {
        Configuration configuration = new Configuration(Configuration.DEFAULT_INCOMPATIBLE_IMPROVEMENTS);
        String classpath = this.getClass().getResource("/").getPath()+ "templates";
        System.out.println(classpath);
        //设置模板路径
        try {
            configuration.setDirectoryForTemplateLoading(new File(classpath));
        } catch (IOException e) {
            e.printStackTrace();
        }
        //设置字符集
        configuration.setDefaultEncoding("UTF-8");
        Template template = configuration.getTemplate("printFormalPayInfo.ftl");
        //???
        PrintFormalPayInfoDTO infoDTO = new PrintFormalPayInfoDTO();
//        infoDTO.setAccidentDate(new Date());
        Map<String, Object> map = new HashMap<String, Object>() {{
            put("PrintFormalPayInfoDTO", infoDTO);
        }};

        //xml报文
        String content = FreeMarkerTemplateUtils.processTemplateIntoString(template, map);
        System.out.println(content);

    }



    @Test
    public void creatFTL() throws Exception {
        // ClmCollegiatePayFtlDTO ClmCommonPayFtlDTO
        // ClmRefuseFtlDTO ClmZeroCancelFtlDTO FeeDeductionInfotlDTO

        //创建ftl,需要对象
        IDGXmlPrintDTO<ClmCollegiatePayFtlDTO> idgXmlPrintDTO = new IDGXmlPrintDTO();
//        createFtlFile(idgXmlPrintDTO, ClmCollegiatePayFtlDTO.class, "ClmCollegiatePayFtlDTO", "D:\\zking-claim\\zking-claim\\src\\main\\resources\\templates");
    }


}