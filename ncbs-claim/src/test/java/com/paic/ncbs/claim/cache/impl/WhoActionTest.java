package com.paic.ncbs.claim.cache.impl;

import com.paic.ncbs.claim.service.checkloss.ChannelProcessService;
import com.paic.ncbs.claim.service.endcase.CaseClassService;
import com.paic.ncbs.claim.service.endcase.WholeCaseService;
import com.paic.ncbs.claim.service.settle.EndorsementService;
import com.paic.ncbs.claim.model.dto.endcase.CaseInfoParameterDTO;


import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class WhoActionTest {

    @Autowired
    private CaseClassService caseClassService;
    @Autowired
    private ChannelProcessService channelProcessService;
    @Autowired
    private EndorsementService endorsementService;
    @Autowired
    private WholeCaseService wholeCaseService;

    @Test
    public void getCaseClassListTest(){
        //success
        caseClassService.getCaseClassDefines("82010000000000000052",1);
    }

    @Test
    public void getCaseClassDefineListTest(){
        //success
        caseClassService.getCaseClassDefineList();
    }

    @Test
    public void addChannelProcessIdTest(){
        //success
        CaseInfoParameterDTO caseInfoParameter = new CaseInfoParameterDTO();
        caseInfoParameter.setReportNo("82010000000000000052");
        caseInfoParameter.setPartyNo("");
        caseInfoParameter.setCaseTimes(1);
        caseInfoParameter.setUserId("admin");
        caseInfoParameter.setIdAhcsChannelProcess("");
        caseInfoParameter.setChannelType("");
        channelProcessService.addChannelProcessId(caseInfoParameter);
    }

    @Test
    public void getEndorsementInfoTest(){
        //success
        String reportNo = "82010000000000000052";
        int caseTimes = 1;
        String lossObjectNo = "";
        endorsementService.getByReportNoAndCaseTime(reportNo, caseTimes);
    }

    @Test
    public void getReportBaseInfoTest(){
        //success
        wholeCaseService.getReportBaseInfo("82010000000000000052",1 );
    }

    @Test
    public void getTaskListTest(){

    }
}
