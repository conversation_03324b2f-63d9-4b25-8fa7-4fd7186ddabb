package com.paic.ncbs.claim;

import com.paic.ncbs.print.util.GeneratePDFUtil;
import lombok.Data;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.FileOutputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
class PrintTemplatePdfServiceImplTest {

    @Test
    void testPrintDemo2() throws Exception {
        Map<String, String> map = new HashMap<>();
        map.put("investigateDepartmentName", "北京环球医疗救援有限责任公司");
        GeneratePDFUtil generatePDFUtil = new GeneratePDFUtil();
        OutputStream out = Files.newOutputStream(Paths.get("D:\\test.pdf"));
        generatePDFUtil.generatePDF4SX("ClmInvestigateLetter.html", map, out);
    }


    @Test
    void testPrintDemo() throws Exception {
        GeneratePDFUtil generatePDFUtil =new GeneratePDFUtil();
        String templatePath = "demoPrint.html";
        Aa aa = new Aa();
        aa.setTitle("标题");
        aa.setAge(18);
        aa.setAmount(new BigDecimal("10000.00"));
        List<User> users = new ArrayList<>();
        User user1 = new User();
        user1.setName("张三");
        user1.setAge(12);
        users.add(user1);
        User user2 = new User();
        user2.setName("李四");
        user2.setAge(16);
        users.add(user2);
        aa.setUsers(users);
        Bb bb = new Bb();
        bb.setName("王五");
        aa.setBb(bb);
        OutputStream out = new FileOutputStream("D:\\out.PDF");
        generatePDFUtil.generatePDF4SX(templatePath,aa,out);
    }
}

@Data
class Aa {
    private String title;

    private Integer age;

    private BigDecimal amount;

    private List<User> users;

    private Bb bb;
}

@Data
class Bb {
    private String name;

}

@Data
class User{
    private String name;

    private Integer age;
}
