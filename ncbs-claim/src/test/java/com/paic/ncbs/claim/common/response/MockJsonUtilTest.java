package com.paic.ncbs.claim.common.response;

import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.util.DateUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Date;

@SpringBootTest
class MockJsonUtilTest {

    @Test
    void getJsonObjectFromFile() {
        JSONObject jsonObjectFromFile = MockJsonUtil.getJsonObjectFromFile("1.json");
    }

    @Test
    void testDate(){
        Date startDate = new Date("2023-08-05");
        Date endDate = new Date();
       int days = DateUtils.getDaysBetween(startDate, endDate);
       System.out.println("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@:"+days);
    }

}