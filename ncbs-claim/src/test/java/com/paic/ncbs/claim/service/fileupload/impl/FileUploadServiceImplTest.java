package com.paic.ncbs.claim.service.fileupload.impl;

import com.paic.ncbs.claim.model.dto.fileupload.FileInfoDTO;
import com.paic.ncbs.claim.service.fileupload.FileUploadService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@SpringBootTest
class FileUploadServiceImplTest {

    @Autowired
    private FileUploadService fileUploadService;

    @Test
    void getDocumentList() {
        String userId = "";
        FileInfoDTO fileInfoDTO = new FileInfoDTO();
     //  fileUploadService.getDocumentList(userId,fileInfoDTO);
    }
}