package com.paic.ncbs.claim.service.pay.impl;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.model.dto.pay.PaymentItemComData;
import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureInfoDTO;
import com.paic.ncbs.claim.service.pay.PaymentItemService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@ExtendWith(SpringExtension.class)
@SpringBootTest
public class PaymentItemServiceImplTest {
    @Autowired
    PaymentItemService paymentItemService;

    @Test
    public void splitCoinsureItem(){
        PaymentItemComData p1 = new PaymentItemComData();
        p1.setPaymentAmount(new BigDecimal("100"));
        p1.setPolicyNo("101");
        List<PaymentItemComData> list = new ArrayList<>();
        list.add(p1);

        List<CoinsureInfoDTO> coinsureList = new ArrayList<>();
        CoinsureInfoDTO c1 = new CoinsureInfoDTO();
        c1.setPolicyNo("101");
        List<CoinsureDTO> list2 = new ArrayList<>();
        CoinsureDTO c2 = new CoinsureDTO();
        c2.setAcceptInsuranceFlag("1");
        c2.setReinsureScale(new BigDecimal("0.7"));
        CoinsureDTO c3 = new CoinsureDTO();
        c3.setAcceptInsuranceFlag("0");
        c3.setReinsureScale(new BigDecimal("0.2"));
        CoinsureDTO c4 = new CoinsureDTO();
        c4.setAcceptInsuranceFlag("0");
        c4.setReinsureScale(new BigDecimal("0.1"));
        list2.add(c2);
        list2.add(c3);
        list2.add(c4);
        c1.setCoinsureDtos(list2);
        coinsureList.add(c1);
        List<PaymentItemComData> res = paymentItemService.splitCoinsureItem(coinsureList,list);
        System.out.println(JSON.toJSONString(res));
    }
}
