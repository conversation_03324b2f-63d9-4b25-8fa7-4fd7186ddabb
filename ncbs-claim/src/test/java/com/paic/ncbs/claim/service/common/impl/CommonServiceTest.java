package com.paic.ncbs.claim.service.common.impl;


import com.paic.ncbs.claim.common.constant.CommonConstant;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoExMapper;
import com.paic.ncbs.claim.service.other.CommonService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Date;

@ExtendWith(SpringExtension.class)
@SpringBootTest
public class CommonServiceTest {
    @Autowired
    private CommonService commonService;
    @Autowired
    private ReportInfoExMapper reportInfoExMapper;

    @Test
    void getReportNo(){
        System.out.println(commonService.generateReportNo("201"));
    }

    @Test
    void getCaseNo(){
        System.out.println(commonService.generateCaseNo("201"));
    }

    @Test
    void insertReportInfoEx(){
        ReportInfoExEntity reportInfoEx = new ReportInfoExEntity();
        reportInfoEx.setSuccorService(CommonConstant.NO);
        reportInfoEx.setIdAhcsReportInfoEx(UuidUtil.getUUID());
        reportInfoEx.setIsSpecialReport("1");
        reportInfoEx.setReportNo("1");
        reportInfoEx.setCreatedBy("sys");
        reportInfoEx.setUpdatedBy("sys");
        reportInfoEx.setCreatedDate(new Date());
        reportInfoEx.setUpdatedDate(new Date());
        reportInfoEx.setCaseClass("1");
        reportInfoEx.setIdAhcsReportInfoEx(UuidUtil.getUUID());
        reportInfoEx.setLinkManName("张三");
        reportInfoEx.setLinkManRelation("1");
        reportInfoEx.setRelationWithReporter("1");
        reportInfoEx.setSendMessage("1");
        reportInfoEx.setResidenceAddress("1");
        reportInfoEx.setResidenceCity("1");
        reportInfoEx.setResidenceDistrict("1");
        reportInfoEx.setResidenceProvince("1");
        reportInfoEx.setWesureAutoClaim("Y");
        reportInfoEx.setRiskLevelScore(2);
        reportInfoEx.setRiskLevelDesc("用户有既往症");
        reportInfoExMapper.insert(reportInfoEx);
    }
}
