package com.paic.ncbs.claim.service.report.impl;

import cn.hutool.core.date.DateUtil;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.service.report.RegisterCaseService;
import com.paic.ncbs.claim.utils.JsonUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.math.BigDecimal;
import java.util.Date;

@ExtendWith(SpringExtension.class)
@SpringBootTest
class RegisterCaseServiceImplTest {

    @Autowired
    private RegisterCaseService registerCaseService;

    @Test
    void isExistRegisterRecord() {
    }

    @Test
    void registerCaseCheck() {
        registerCaseService.registerCaseCheck("123656", 1);
    }

    @Test
    void getLastCaseRegisterApplyVOList() {
    }

    @Test
    void getLastCaseRegisterApplyDTO() {
        registerCaseService.getLastCaseRegisterApplyVOList("123456", 1);
    }

    @Test
    void addCaseRegisterApplyDTO() {
    }

    @Test
    void batchAddRegisterAmountRelInfo() {
    }

    @Test
    void registerHistoryCase() {
    }

    @Test
    void modifyRegisterAuditStatus() {
    }

    @Test
    void addRegisterCaseLog() {
        String insuranceBegintime = "2024-04-26";
        Date   insuranceBeginDate =DateUtils.formatStringToDate(insuranceBegintime,DateUtils.SIMPLE_DATE_STR);

        Date endWaitDate=DateUtils.addDate(insuranceBeginDate,29);
        Date reEndDate= DateUtil.offsetMillisecond(endWaitDate,-1);//
        String sbillDate="2024-05-26";

        Date billdate =DateUtils.formatStringToDate(sbillDate,DateUtils.SIMPLE_DATE_STR);
        if(billdate.compareTo(insuranceBeginDate)>=0 && billdate.compareTo(reEndDate)<=0){
           System.out.println("等待期发票");
        }else{
            System.out.println("不是等待期");
        }

    }

    @Test
    void registerCaseForReport() {
        BigDecimal settleAmount=new BigDecimal(200077);
       int a = settleAmount.compareTo(new BigDecimal(20000));
       System.out.println("#######################"+a);
        if(settleAmount.compareTo(new BigDecimal(20000))>0){
            throw new GlobalBusinessException("赔付金额超过2万，不能选择领款“微信零钱”领款方式，微信零钱限额2万");
        }
    }
}