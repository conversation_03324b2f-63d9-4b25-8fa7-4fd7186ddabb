package com.paic.ncbs.claim.service.sop.impl;

import com.paic.ncbs.claim.dao.entity.sop.ClmsSopMain;
import com.paic.ncbs.claim.dao.mapper.sop.ClmsSopMainMapper;
import com.paic.ncbs.claim.dao.mapper.sop.ClmsSopFileMapper;
import com.paic.ncbs.claim.dao.mapper.sop.ClmsSopConfigMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.sop.SopMainDTO;
import com.paic.ncbs.claim.service.iobs.IOBSFileUploadService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SopMainServiceImpl测试类
 */
@ExtendWith(MockitoExtension.class)
class SopMainServiceImplTest {

    @Mock
    private ClmsSopMainMapper clmsSopMainMapper;

    @Mock
    private ClmsSopFileMapper clmsSopFileMapper;

    @Mock
    private ClmsSopConfigMapper clmsSopConfigMapper;

    @Mock
    private IOBSFileUploadService iobsFileUploadService;

    @Mock
    private com.paic.ncbs.claim.service.common.RedisService redisService;

    @InjectMocks
    private SopMainServiceImpl sopMainService;

    private SopMainDTO sopMainDTO;
    private MockHttpServletRequest request;
    private UserInfoDTO userInfo;

    @BeforeEach
    void setUp() {
        sopMainDTO = new SopMainDTO();
        sopMainDTO.setSopName("测试SOP");
        sopMainDTO.setSopDescription("测试描述");
        sopMainDTO.setIsAllProcess("Y");
        sopMainDTO.setSopContent("测试内容");

        request = new MockHttpServletRequest();
        
        userInfo = new UserInfoDTO();
        userInfo.setUserCode("TEST001");
        userInfo.setUserName("测试用户");
    }

    @Test
    void testSaveOrUpdateSop_InvalidInitFlag() {
        // 测试不支持的操作类型
        sopMainDTO.setInitFlag("04");
        sopMainDTO.setIdSopMain(null);

        try (MockedStatic<com.paic.ncbs.claim.common.context.WebServletContext> mockedContext =
             mockStatic(com.paic.ncbs.claim.common.context.WebServletContext.class)) {
            mockedContext.when(com.paic.ncbs.claim.common.context.WebServletContext::getUser)
                       .thenReturn(userInfo);

            GlobalBusinessException exception = assertThrows(GlobalBusinessException.class,
                () -> sopMainService.saveOrUpdateSop(sopMainDTO, request));

            assertTrue(exception.getMessage().contains("不支持的操作类型"));
        }
    }

    @Test
    void testSaveOrUpdateSop_SaveOperation_NewSop() {
        // 测试新增暂存操作
        sopMainDTO.setInitFlag("01");
        sopMainDTO.setIdSopMain(null); // 新增情况

        when(clmsSopMainMapper.insertSelective(any(ClmsSopMain.class))).thenReturn(1);

        try (MockedStatic<com.paic.ncbs.claim.common.context.WebServletContext> mockedContext =
             mockStatic(com.paic.ncbs.claim.common.context.WebServletContext.class)) {
            mockedContext.when(com.paic.ncbs.claim.common.context.WebServletContext::getUser)
                       .thenReturn(userInfo);

            String result = sopMainService.saveOrUpdateSop(sopMainDTO, request);

            assertNotNull(result);
            verify(clmsSopMainMapper).insertSelective(any(ClmsSopMain.class));
        }
    }

    @Test
    void testSaveOrUpdateSop_SaveOperation_DuplicateSubmission() {
        // 测试重复提交防护
        sopMainDTO.setInitFlag("01");
        sopMainDTO.setIdSopMain(null); // 新增情况

        // Mock Redis 服务返回重复提交
        when(redisService.checkDuplicateSubmission(anyString())).thenReturn(true);

        try (MockedStatic<com.paic.ncbs.claim.common.context.WebServletContext> mockedContext =
             mockStatic(com.paic.ncbs.claim.common.context.WebServletContext.class)) {
            mockedContext.when(com.paic.ncbs.claim.common.context.WebServletContext::getUser)
                       .thenReturn(userInfo);

            GlobalBusinessException exception = assertThrows(GlobalBusinessException.class,
                () -> sopMainService.saveOrUpdateSop(sopMainDTO, request));

            assertTrue(exception.getMessage().contains("SOP正在处理中，请勿重复提交"));
        }
    }

    @Test
    void testSaveOrUpdateSop_SaveOperation_WithIdSopMain() {
        // 测试有IdSopMain的暂存操作
        sopMainDTO.setInitFlag("01");
        sopMainDTO.setIdSopMain("test-id");

        ClmsSopMain existingSop = new ClmsSopMain();
        existingSop.setIdSopMain("test-id");
        existingSop.setBatchNo("test-batch");
        existingSop.setStatus("01");

        when(clmsSopMainMapper.selectByPrimaryKey("test-id")).thenReturn(existingSop);
        when(clmsSopMainMapper.selectLatestByBatchNo("test-batch")).thenReturn(existingSop);
        when(clmsSopMainMapper.updateByPrimaryKeySelective(any(ClmsSopMain.class))).thenReturn(1);

        try (MockedStatic<com.paic.ncbs.claim.common.context.WebServletContext> mockedContext = 
             mockStatic(com.paic.ncbs.claim.common.context.WebServletContext.class)) {
            mockedContext.when(com.paic.ncbs.claim.common.context.WebServletContext::getUser)
                       .thenReturn(userInfo);

            String result = sopMainService.saveOrUpdateSop(sopMainDTO, request);

            assertEquals("test-id", result);
            verify(clmsSopMainMapper).selectByPrimaryKey("test-id");
            verify(clmsSopMainMapper).selectLatestByBatchNo("test-batch");
            verify(clmsSopMainMapper).updateByPrimaryKeySelective(any(ClmsSopMain.class));
        }
    }

    @Test
    void testSaveOrUpdateSop_PublishOperation() {
        // 测试发布操作
        sopMainDTO.setInitFlag("02");
        sopMainDTO.setIdSopMain("test-id");

        ClmsSopMain existingSop = new ClmsSopMain();
        existingSop.setIdSopMain("test-id");
        existingSop.setBatchNo("test-batch");
        existingSop.setStatus("01");
        existingSop.setVersionNo("1.0");
        existingSop.setSopName("测试SOP");

        when(clmsSopMainMapper.selectByPrimaryKey("test-id")).thenReturn(existingSop);
        when(clmsSopMainMapper.selectLatestByBatchNo("test-batch")).thenReturn(existingSop);
        when(clmsSopMainMapper.selectBySopName("测试SOP")).thenReturn(null);
        when(clmsSopMainMapper.updateByPrimaryKeySelective(any(ClmsSopMain.class))).thenReturn(1);

        try (MockedStatic<com.paic.ncbs.claim.common.context.WebServletContext> mockedContext = 
             mockStatic(com.paic.ncbs.claim.common.context.WebServletContext.class)) {
            mockedContext.when(com.paic.ncbs.claim.common.context.WebServletContext::getUser)
                       .thenReturn(userInfo);

            String result = sopMainService.saveOrUpdateSop(sopMainDTO, request);

            assertNotNull(result);
            verify(clmsSopMainMapper, times(2)).updateByPrimaryKeySelective(any(ClmsSopMain.class));
        }
    }

    @Test
    void testSaveOrUpdateSop_DisableOperation() {
        // 测试停用操作
        sopMainDTO.setInitFlag("03");
        sopMainDTO.setIdSopMain("test-id");

        ClmsSopMain existingSop = new ClmsSopMain();
        existingSop.setIdSopMain("test-id");
        existingSop.setStatus("02"); // 有效状态

        when(clmsSopMainMapper.selectByPrimaryKey("test-id")).thenReturn(existingSop);
        when(clmsSopMainMapper.updateByPrimaryKeySelective(any(ClmsSopMain.class))).thenReturn(1);

        try (MockedStatic<com.paic.ncbs.claim.common.context.WebServletContext> mockedContext = 
             mockStatic(com.paic.ncbs.claim.common.context.WebServletContext.class)) {
            mockedContext.when(com.paic.ncbs.claim.common.context.WebServletContext::getUser)
                       .thenReturn(userInfo);

            String result = sopMainService.saveOrUpdateSop(sopMainDTO, request);

            assertEquals("test-id", result);
            verify(clmsSopMainMapper).updateByPrimaryKeySelective(any(ClmsSopMain.class));
        }
    }

    @Test
    void testSaveOrUpdateSop_DisableOperation_InvalidStatus() {
        // 测试停用操作 - 无效状态
        sopMainDTO.setInitFlag("03");
        sopMainDTO.setIdSopMain("test-id");

        ClmsSopMain existingSop = new ClmsSopMain();
        existingSop.setIdSopMain("test-id");
        existingSop.setStatus("01"); // 暂存状态，不能停用

        when(clmsSopMainMapper.selectByPrimaryKey("test-id")).thenReturn(existingSop);

        try (MockedStatic<com.paic.ncbs.claim.common.context.WebServletContext> mockedContext = 
             mockStatic(com.paic.ncbs.claim.common.context.WebServletContext.class)) {
            mockedContext.when(com.paic.ncbs.claim.common.context.WebServletContext::getUser)
                       .thenReturn(userInfo);

            GlobalBusinessException exception = assertThrows(GlobalBusinessException.class, 
                () -> sopMainService.saveOrUpdateSop(sopMainDTO, request));
            
            assertTrue(exception.getMessage().contains("只有有效状态的SOP才能停用"));
        }
    }
}
