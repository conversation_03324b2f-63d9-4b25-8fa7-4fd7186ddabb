{"code": "000000", "msg": "成功", "data": [{"policyNo": "10101006600010129205", "caseNo": "41803004100004539173", "noticeNo": "18140044000384568163", "bankSerialNum": null, "clientName": "测试", "paymentAmount": "118.79", "paymentType": "赔款", "collectPayApproach": "实时收付", "collectPaySign": "付款", "clientBankName": "平安银行", "clientBankAccount": "****************", "paymentItemStatus": "待支付", "sendDate": null, "payDate": "2022-02-15 16:30:43", "backDate": "2022-02-15 16:30:43", "downNoticeDate": "2022-02-15 16:20:00", "paymentItemType": "20", "noticeStatus": null}]}